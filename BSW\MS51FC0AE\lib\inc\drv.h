#ifndef _DRV_H_
#define _DRV_H_

/**********************************************************************************************
* External objects
**********************************************************************************************/
#include <string.h>
#include <stdio.h>

#include "sys.h"
#include "flash.h"
#include "TIMER.h"
#include "UART.h"
#include "WDOG.h"
#include "PWM.h"
#include "ADC.h"

/**********************************************************************************************
* Constants and macros
**********************************************************************************************/

/**********************************************************************************************
* Local types
**********************************************************************************************/
typedef enum
{
    // PWM_CH_NONE,
    PWM_CH_OEC,
    // PWM_CH_IR,
    PWM_CH_IEC,
} pwm_ch_t;

typedef enum
{
    KEY_NONE,
    KEY_ON,
    KEY_MAX
} key_value_t;

void UART_Task(void);
void uart_rcv_timeout_tick(void);
void uart_tat_send(uint8_t *buffer, uint8_t length);
void uart_tat_rcv_buffer_clear(void);
uint8_t *uart_tat_get_buffer(uint8_t *length);

void nvm_erase(uint16_t addr);
void nvm_read(uint32_t addr, uint8_t *buffer, uint16_t size);
void nvm_write(uint32_t addr, uint8_t *buffer, uint16_t size);
uint8_t nvm_readchipid(uint8_t *cid);

uint8_t gpio_test_mode(void);

uint16_t adc_get_ec_int(void);
uint16_t adc_get_back(void);
uint16_t adc_get_front(void);
uint16_t adc_get_ign(void);
uint16_t adc_get_ec_ext(void);
uint16_t adc_get_ntc(void);

void pwm_set_duty(pwm_ch_t ch, uint8_t duty);

uint8_t gpio_rev_in(void);
uint8_t gpio_get_off(void);


void hw_init(void);

void offpin_value_set(uint8_t value);

#endif
