#ifndef __SYS_H
#define __SYS_H	

#include "N76E003.h"
#include "Function_Define.h"
#include "SFR_Macro.h"
#include "Common.h"


typedef bit                   BIT;
typedef unsigned char         uint8_t;
typedef unsigned int          uint16_t;
typedef unsigned long         uint32_t;

typedef char         int8_t;
typedef int          int16_t;
typedef long         int32_t;
   
	

typedef void(*VoidFuncVoid)(void);	



extern bit BIT_TMP;
 

#endif 
