#include "asm31ax_hal.h"

#ifdef HAL_IWDG_MODULE_ENABLED

/* Includes ------------------------------------------------------------------*/

uint8_t iwdt_flg;

void HAL_IWDG_Init(IWDG_InitTypeDef *IWDG_InitStruct)
{

    IWDG_InitStruct->Instance->UNLOCK = IWDG_WriteAccess_Disable;
    IWDG_InitStruct->Instance->CFGR |= IWDG_InitStruct->Mode;
    IWDG_InitStruct->Instance->CFGR &= ~COMMAND_WDTINTSHIELD_ENBLE;
    IWDG_InitStruct->Instance->RLOAD = IWDG_InitStruct->Reload;
    IWDG_InitStruct->Instance->CMDCR = COMMAND_IWDT_ENABLE;
    IWDG_InitStruct->Instance->UNLOCK = IWDG_WriteAccess_Enable;
}

void HAL_IWDG_Feed(IWDG_InitTypeDef *IWDG_InitStruct, uint32_t time)
{
    IWDG_InitStruct->Instance->UNLOCK = IWDG_WriteAccess_Disable;
    IWDG_InitStruct->Instance->RLOAD = time;
    IWDG_InitStruct->Instance->UNLOCK = IWDG_WriteAccess_Enable;
}

/** @defgroup IWDG_Group1 Prescaler and Counter configuration functions
 *  @brief   Prescaler and Counter configuration functions
 *
@verbatim
 ===============================================================================
              ##### Prescaler and Counter configuration functions #####
 ===============================================================================
  *
  * @brief  Enables or disables write access to IWDG_PR and IWDG_RLR registers.
  * @param  IWDG_WriteAccess: new state of write access to IWDG_PR and IWDG_RLR registers.
  *          This parameter can be one of the following values:
  *            @arg IWDG_WriteAccess_Enable: Enable write access to IWDG_PR and IWDG_RLR registers
  *            @arg IWDG_WriteAccess_Disable: Disable write access to IWDG_PR and IWDG_RLR registers
  * @retval None
  */

void HAL_IWDG_WriteAccessCmd(uint32_t IWDG_WriteAccess)
{
    /* Check the parameters */
    assert_param(IS_IWDG_WRITE_ACCESS(IWDG_WriteAccess));
    IWDG->UNLOCK = IWDG_WriteAccess;
}

/**
 * @brief  Sets IWDG Reload value.
 * @param  Reload: specifies the IWDG Reload value.
 *          This parameter must be a number between 0 and 0x0FFF.
 * @retval None
 */
void HAL_IWDG_SetReload(uint32_t Reload)
{
    /* Check the parameters */
    assert_param(IS_IWDG_RELOAD(Reload));
    /*Set the value to the RLOAD Register*/
    IWDG->RLOAD = Reload;
}

/**
 * @brief  Sets the mode of IWDG
 * @param  Mode: specifies the IWDG Mode.
 * @retval None
 */
void HAL_IWDG_SetWdtMode(uint32_t Mode)
{
    /* Check the parameters */
    if (Mode == COMMAND_WDT_INT)
    {
        IWDG->CFGR |= COMMAND_WDT_INT;
    }
    else
    {
        IWDG->CFGR &= ~COMMAND_WDT_INT;
    }
}

/**
 * @brief  Gets IWDG Reload value.
 * @param  None
 * @retval None
 */
uint32_t HAL_IWDG_GetReload(void)
{
    /* Check the parameters */
    return (uint32_t)(IWDG->CNTVAL);
}

/**
 * @brief  Reloads IWDG counter with value defined in the reload register
 *         (write access to IWDG_PR and IWDG_RLR registers disabled).
 * @param  None
 * @retval None
 */
void HAL_IWDG_ReloadCounter(void)
{
    IWDG->CMDCR = COMMAND_IWDT_RELOAD;
}

/**
 * @brief  Enables IWDG (write access to Command registers disabled).
 * @param  None
 * @retval None
 */
void HAL_IWDG_Cmd(void)
{
    /*Set the vlaue to the command*/
    IWDG->CMDCR = COMMAND_IWDT_ENABLE;
}

/**
 * @brief  Enables IWDG (write access to Command registers disabled).
 * @param  None
 * @retval None
 */
void HAL_IWDG_RELOAD(void)
{
    /*Set the vlaue to the command*/
    IWDG->CMDCR = COMMAND_IWDT_RELOAD;
}

/**
 * @brief  Checks whether the specified IWDG flag is set or not.
 * @param  IWDG_FLAG: specifies the flag to check.
 *          This parameter can be one of the following values:
 *            @arg IWDG_INT_FLAG: IWDG Interrupt Flags
 * @retval The new state of IWDG_FLAG (SET or RESET).
 */
FlagStatus HAL_IWDG_GetFlagStatus(uint16_t IWDG_FLAG)
{
    FlagStatus bitstatus = RESET;
    /* Check the parameters */
    if ((IWDG->ISR & IWDG_FLAG) != (uint32_t)RESET)
    {
        bitstatus = SET;
    }
    else
    {
        bitstatus = RESET;
    }
    /* Return the flag status */
    return bitstatus;
}

/**
 * @brief  Enable interrupt shield  function
 * @param  NewState: new state of the WWDG peripheral.
 * This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_IWDG_WdtITShieldCmd(FunctionalState NewStatus)
{
    /*Enables the flag of iwdt interrupt*/
    if (NewStatus != DISABLE)
    {
        IWDG->CFGR |= COMMAND_WDTINTSHIELD_ENBLE;
    }
    else
    {
        /*Disables the flag of iwdt interrupt*/
        IWDG->CFGR &= ~COMMAND_WDTINTSHIELD_ENBLE;
    }
}

/**
 * @brief  Clears the flag of IWDG
 * @param  None
 * @retval None
 */
void HAL_IWDG_WdtITClear(void)
{
    /*Clears the flag of IWDG*/
    IWDG->ICR = COMMAND_WDTINTCLR_ENBLE;
}

#endif
