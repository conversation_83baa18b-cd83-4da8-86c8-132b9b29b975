/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_CRC_H
#define __ASM31AX_CRC_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

/** @defgroup crc_flag_mask
 * @{
 */
#define crc_flag_mask 0x10000

    /* Exported functions --------------------------------------------------------*/
    void CRC_init(void);
    uint16_t CRC_CalcCRC8(uint8_t Data);
    ErrorStatus CRC_CheckCRC8(uint8_t Data);
    uint16_t CRC_MultiCalcCRC8(uint8_t *buffer, uint32_t count);
    ErrorStatus CRC_MultiCheckCRC8(uint8_t *buffer, uint32_t count);
    uint16_t CRC_CalcCRC16(uint16_t Data);
    ErrorStatus CRC_CheckCRC16(uint16_t Data);
    uint16_t CRC_MultiCalcCRC16(uint16_t *buffer, uint32_t count);
    ErrorStatus CRC_MultiCheckCRC16(uint16_t *buffer, uint32_t count);
    uint16_t CRC_CalcCRC32(uint32_t Data);
    ErrorStatus CRC_CheckCRC32(uint32_t Data);
    uint16_t CRC_MultiCalcCRC32(uint32_t *buffer, uint32_t count);
    ErrorStatus CRC_MultiCheckCRC32(uint32_t *buffer, uint32_t count);

#ifdef __cplusplus
}
#endif

#endif /* __ASM31X_CRC_H */
