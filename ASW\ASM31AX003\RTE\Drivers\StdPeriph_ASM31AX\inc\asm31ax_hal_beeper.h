/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_BEEPER_H // asm31x
#define __ASM31AX_BEEPER_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

    typedef struct
    {
        BEEPER_TypeDef *Instance;
        uint32_t ClkSel;
        uint32_t ClkDiv;
        uint32_t BeepSel;

    } Beeper_InitTypeDef;

    /* Exported types ------------------------------------------------------------*/
#define IS_BEEPER_ALL_PERIPH(PERIPH) ((PERIPH) == BEEPER)

/**
 * @}BEEPER MASK
 */
#define BEEPERDIV_MASK (uint32_t)0x00000fff
#define BEEPERSELECT_MASK (uint32_t)(0x03 << 16) /* 蜂鸣器输出 BEEP_O 频率选择位: 1x: fBEEP_PRE/2 */
#define BEEPERCLOCK_MASK (uint32_t)(0x03 << 20)  /* 蜂鸣器时钟源选择：PCLK */
#define BEEPER_ENABLE (uint32_t)(0x01 << 18)     /* 蜂鸣器使能 */
#define IS_BEEPER_PRESCALER(PRESCALER) ((PRESCALER <= 0xfff) && (PRESCALER >= 0))

/**
 * @}BEEPER_CLOCKSEL DIV
 */
#define BEEPER_CLOCKSEL_DIV8 (uint32_t)0x00000000
#define BEEPER_CLOCKSEL_DIV4 (uint32_t)0x00010000
#define BEEPER_CLOCKSEL_DIV2 (uint32_t)0x00020000
#define IS_BEEPER_CLOCKSEL(CLK) ((CLK == BEEPER_CLOCKSEL_DIV8) || \
                                 (CLK == BEEPER_CLOCKSEL_DIV4) || \
                                 (CLK == BEEPER_CLOCKSEL_DIV2))
/**
 * @}BEEPER CLK SELECTION
 */
#define BEEPER_CLKSEL_STOP (uint32_t)0x00000000
#define BEEPER_CLKSEL_LICR (uint32_t)0x00100000
#define BEEPER_CLKSEL_HXT (uint32_t)0x00200000
#define BEEPER_CLKSEL_PCLK (uint32_t)0x00300000
#define IS_BEEPER_SELECLOCK(CLK) ((CLK == BEEPER_CLKSEL_STOP) || \
                                  (CLK == BEEPER_CLKSEL_LICR) || \
                                  (CLK == BEEPER_CLKSEL_HXT) ||  \
                                  (CLK == BEEPER_CLKSEL_PCLK))

    /* Exported functions --------------------------------------------------------*/

    void BEEPER_Init(Beeper_InitTypeDef *Beeper_InitStruct);
    void BEEPER_SetPrescaler(BEEPER_TypeDef *BEEPERx, uint32_t BeeperPrescaler);
    void BEEPER_SelectPrescaler(BEEPER_TypeDef *BEEPERx, uint32_t PreClk);
    void BEEPER_Cmd(BEEPER_TypeDef *BEEPERx, FunctionalState NewState);
    void BEEPER_SetClock(BEEPER_TypeDef *BEEPERx, uint32_t CLK);

#ifdef __cplusplus
}
#endif

#endif /*__ASM31X_BEEPER_H*/

/**
 * @}
 */

/**
 * @}
 */
