/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_UART_H
#define __ASM31AX_UART_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"
    // #include "asm31x_hal_conf.h"
    // #include  "stdio.h"

    /**
     * @brief  UART Init Structure definition
     */
    typedef struct
    {
        UART_TypeDef *Instance;

        uint32_t ITEnable;

        uint32_t BaudRate; /*!< This member configures the UART communication baud rate.
                                  The baud rate is computed using the following formula:
                                   - IntegerDivider = ((PCLKx) / (16 * (UART_InitStruct->BaudRate)))
                                   - FractionalDivider = ((IntegerDivider - ((u32) IntegerDivider)) * 16) + 0.5 */

        uint8_t BraudDouble; /*!< Baud Rate double enable*/

        uint8_t TimerEnable; /*!< Select Baud Rate Clock */

        uint8_t Mode; /*!< Specifies wether the Receive or Transmit mode is enabled or disabled.
                              This parameter can be a value of Mode */
        uint32_t TxPin;
        uint32_t RxPin;

    } UART_InitTypeDef;

    /**
     * @brief  UART_IRD Init Structure definition
     */
    typedef struct
    {
        UART_TypeDef *Instance;

        uint32_t UART_IRDIRLPMOD; /*!< This member configures the UART communication baud rate.
                                    The baud rate is computed using the following formula:
                                     - IntegerDivider = ((PCLKx) / (16 * (UART_InitStruct->BaudRate)))
                                     - FractionalDivider = ((IntegerDivider - ((u32) IntegerDivider)) * 16) + 0.5 */

        uint32_t UART_IRDIRRXINV; /*!<IRD  receiving Data Flip Control Bit*/

        uint32_t UART_IRDIRTXINV; /*!<IRD  send Data Flip Control Bit*/

        uint32_t UART_IRDPSC; /*!< UART IRD PSC
                                This parameter can be a value of Mode */
    } UART_IRDInitTypeDef;

/** @defgroup UART_Exported_Constants
 * @{
 */
#define IS_UART_ALL_PERIPH(PERIPH) (((PERIPH) == UART0) || \
                                    ((PERIPH) == UART1))

/** @defgroup UART_Clock
 * @{
 */
#define UART_Clock_Disable ((uint32_t)0x00000000)
#define UART_Clock_Enable ((uint32_t)0x00000800)
#define IS_UART_CLOCK(CLOCK) (((CLOCK) == UART_Clock_Disable) || \
                              ((CLOCK) == UART_Clock_Enable))

#define UART_IT_DISABLE (0x00000000UL)
#define UART_IT_TIEN (0x00000001UL)
#define UART_IT_RIEN (0x00000002UL)
#define UART_IT_TIRIEN (0x00000004UL)

/** @defgroup UART_MODE
 * @{
 */
#define UART_MODE0 (uint32_t)0x00000000 /* 同步模式，半双工 */
#define UART_MODE1 (uint32_t)0x00000001 /* 异步模式，全双工，帧结构10bit*/
#define UART_MODE2 (uint32_t)0x00000002 /* 异步模式，全双工，帧结构11bit, 用于多机通信*/
#define UART_MODE3 (uint32_t)0x00000003 /* 异步模式，全双工, 与模式2区别在于波特率产生方式种类不同 */
#define IS_UART_MODE(MODE) (((MODE) == UART_MODE_0) || \
                            ((MODE) == UART_MODE_1) || \
                            ((MODE) == UART_MODE_2) || \
                            ((MODE) == UART_MODE_3))

#define IS_UART_BAUDRATE(BAUDRATE) (((BAUDRATE) > 0) && ((BAUDRATE) < 0x0044AA21))
#define UART_Address_Mask ((uint32_t)0x0000FF00) /*!< USART address Mask */
#define IS_UART_ADDRESS(ADDRESS) ((ADDRESS) <= 0xFF)

#define UART_IRDACR_IRLP (uint32_t)0x00000800    /*!< Ir低功耗使能   */
#define UART_IRDACR_IRRXINV (uint32_t)0x00000400 /*!< IrRXD 数据反转位使能 */
#define UART_IRDACR_IRTXINV (uint32_t)0x00000200 /*!< IrTXD 数据反转位使能 */
#define UART_IRDACR_IREN (uint32_t)0x00000100    /*!< IrDA使能  */

/******************  Bit definition for USART_SCON register  *******************/
#define UART_SCON_RIEN 0x00000001  /*!< 接收完成中断使能 */
#define UART_SCON_TIEN 0x00000002  /*!< 发送完成中断使能 */
#define UART_SCON_RB8 0x00000004   /*!< 接收 RB8 位 */
#define UART_SCON_TB8 0x00000008   /*!< 发送 TB8 位 */
#define UART_SCON_REN 0x00000010   /*!< 接收/发送 */
#define UART_SCON_SM2 0x00000020   /*!< 多主机通信 */
#define UART_SCON_SM1 0x00000040   /*!< 工作模式 */
#define UART_SCON_SM0 0x00000080   /*!< 工作模式 */
#define UART_SCON_FEEN 0x00000100  /*!<  */
#define UART_SCON_DBAUD 0x00000200 /*!< 双倍波特率 */

#define UART_SCON_MODE0 ~(UART_SCON_SM1 | UART_SCON_SM0)
#define UART_SCON_MODE1 UART_SCON_SM1
#define UART_SCON_MODE2 UART_SCON_SM0
#define UART_SCON_MODE3 UART_SCON_SM1 | UART_SCON_SM0

/******************  Bit definition for USART_ISR register  *******************/
#define UART_ISR_RI 0x00000001             /*!< 接收完成中断标志位   */
#define UART_ISR_TI 0x00000002             /*!< 发送完成中断标志位   */
#define UART_ISR_FEI 0x00000004            /*!< 接收帧错误中断标志位   */
#define UART_ISR_TxBuffer_Empty 0x00000008 /*!< 发送缓存空中断标志位 */

/******************  Bit definition for USART_ICR register  *******************/
#define UART_ICR_RI 0x00000001
#define UART_ICR_TI 0x00000002
#define UART_ICR_FEI 0x00000004
#define UART_ICR_ALL UART_ICR_RI | UART_ICR_TI | UART_ICR_FEI

#define UART_SELF_BRG (uint32_t)0x00010000
#define UART_RXEN_EABLE (uint32_t)0x00000001 /*!< UART接收使能 */
#define UART_TXEN_EABLE (uint32_t)0x00000002 /*!< UART发送使能 */

#define UART_RIEN_EABLE (uint32_t)0x00000001 /*!< UART接收中断使能 */
#define UART_TIEN_EABLE (uint32_t)0x00000002 /*!< UART发送中断使能 */
#define IS_UART_IT(IT) ((IT == UART_RIEN_EABLE) || \
                        (IT == UART_TIEN_EABLE))

#define UART_TIM_EABLE (uint32_t)0x000000E0

    /** @defgroup UART_Exported_Functions
     * @{
     */
    void HAL_UART_DeInit(UART_TypeDef *UARTx);
    void HAL_UART_Init(UART_InitTypeDef *UART_InitStruct);

    void HAL_UART_StructInit(UART_InitTypeDef *UART_InitStruct);
    void HAL_UART_Cmd(UART_TypeDef *UARTx, uint16_t FuncState, FunctionalState NewState);
    void HAL_UART_ITConfig(UART_TypeDef *UARTx, uint32_t UART_IT, FunctionalState NewState);
    void HAL_UART_SetAddress(UART_TypeDef *UARTx, uint8_t UART_Address, uint8_t UART_Address_Enable);
    void HAL_UART_ReciveCmd(UART_TypeDef *UARTx, UART_InitTypeDef *UART_InitStruct, FunctionalState NewState);
    void HAL_UART_SendData(UART_TypeDef *UARTx, uint8_t Data);
    uint8_t HAL_UART_ReceiveData(UART_TypeDef *UARTx);
    void HAL_UART_IrDAConfig(UART_TypeDef *UARTx, UART_IRDInitTypeDef *UART_IRDInitStruct);
    void HAL_UART_IrDACmd(UART_TypeDef *UARTx, FunctionalState NewState);
    ITStatus HAL_UART_GetITStatus(UART_TypeDef *UARTx, uint32_t UART_IT);
    void HAL_UART_ClearITBit(UART_TypeDef *UARTx, uint32_t UART_IT);
    void HAL_UART_TB8Cmd(UART_TypeDef *UARTx, FunctionalState NewState);
    void HAL_UART_RB8Cmd(UART_TypeDef *UARTx, FunctionalState NewState);

#ifdef __cplusplus
}
#endif

#endif /* __ASM31X_UART_H */
