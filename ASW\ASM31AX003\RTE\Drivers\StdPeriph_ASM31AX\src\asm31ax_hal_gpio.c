#include "asm31ax_hal.h"

#ifdef HAL_GPIO_MODULE_ENABLED

const GPIO_UnitTypeDef pinmap[] = {
    {PA00, GPIOA, GPIO_PIN_0},
    {PA01, GPIOA, GPIO_PIN_1},
    {PA02, GPIOA, GPIO_PIN_2},
    {PA03, GPIOA, GPIO_PIN_3},
    {PA04, GPIOA, GPIO_PIN_4},
    {PA05, GPIOA, GPIO_PIN_5},
    {PA06, GPIOA, GPIO_PIN_6},
    {PA07, GPIOA, GPIO_PIN_7},
    {PB00, GPIOB, GPIO_PIN_0},
    {PB01, GPIOB, GPIO_PIN_1},
    {PB02, GP<PERSON>B, GPIO_PIN_2},
    {PB03, GPIOB, GPIO_PIN_3},
    {PB04, GPIOB, GPIO_PIN_4},
    {PB05, GPIOB, GPIO_PIN_5},
    {<PERSON>B06, GP<PERSON>B, GPIO_PIN_6},
    {PB07, GPIOB, GPIO_PIN_7},
    {PC00, GPIO<PERSON>, GPIO_PIN_0},
    {PC01, GPIOC, GPIO_PIN_1},
    {PC02, GPIOC, GPIO_PIN_2},
    {PC03, GPIOC, GPIO_PIN_3},
    {PC04, GPIOC, GPIO_PIN_4},
    {PC05, GPIOC, GPIO_PIN_5},
    {PC06, GPIOC, GPIO_PIN_6},
    {PC07, GPIOC, GPIO_PIN_7},
    {PD00, GPIOD, GPIO_PIN_0},
    {PD01, GPIOD, GPIO_PIN_1},
    {PD02, GPIOD, GPIO_PIN_2},
    {PD03, GPIOD, GPIO_PIN_3},
    {PD04, GPIOD, GPIO_PIN_4},
    {PD05, GPIOD, GPIO_PIN_5},
    {PD06, GPIOD, GPIO_PIN_6},
    {PD07, GPIOD, GPIO_PIN_7},
};

/* Includes ------------------------------------------------------------------*/

/**
 * @brief  Deinitializes the GPIOx peripheral registers to their default reset values.
 * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral.
 * @retval None
 */
void HAL_GPIO_DeInit(GPIO_TypeDef *GPIOx)
{
    /* Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));

    if (GPIOx == GPIOA)
    {
        RCC_PeriphResetCmd(RCC, RCC_AHBPeriph_GPIOARST, ENABLE);
        RCC_PeriphResetCmd(RCC, RCC_AHBPeriph_GPIOARST, DISABLE);
    }
    else if (GPIOx == GPIOB)
    {
        RCC_PeriphResetCmd(RCC, RCC_AHBPeriph_GPIOBRST, ENABLE);
        RCC_PeriphResetCmd(RCC, RCC_AHBPeriph_GPIOBRST, DISABLE);
    }
    else if (GPIOx == GPIOC)
    {
        RCC_PeriphResetCmd(RCC, RCC_AHBPeriph_GPIOCRST, ENABLE);
        RCC_PeriphResetCmd(RCC, RCC_AHBPeriph_GPIOCRST, DISABLE);
    }
    else if (GPIOx == GPIOD)
    {
        RCC_PeriphResetCmd(RCC, RCC_AHBPeriph_GPIODRST, ENABLE);
        RCC_PeriphResetCmd(RCC, RCC_AHBPeriph_GPIODRST, DISABLE);
    }
}

/**
 * @brief  Deinitializes the Alternate Functions (remap, event control
 *   and EXTI configuration) registers to their default reset values.
 * @param  None
 * @retval None
 */
void HAL_GPIO_AFIODeInit(void)
{
    GPIOA->AFR = (uint32_t)0x0;
    GPIOB->AFR = (uint32_t)0x0;
    GPIOC->AFR = (uint32_t)0x0;
    GPIOD->AFR = (uint32_t)0x0;
}

/**
 * @brief  Initializes the GPIOx peripheral according to the specified
 *         parameters in the GPIO_InitStruct.
 * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral.
 * @param  GPIO_InitStruct: pointer to a GPIO_InitTypeDef structure that
 *         contains the configuration information for the specified GPIO peripheral.
 * @retval None
 */
void HAL_GPIO_Init(GPIO_TypeDef *GPIOx, GPIO_InitTypeDef *GPIO_InitStruct)
{
    uint32_t pinpos = 0x00, pos = 0x00, currentpin = 0x00;
    /* Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_GPIO_PIN(GPIO_InitStruct->Pin));
    assert_param(IS_GPIO_MODE(GPIO_InitStruct->GPIO_Mode));
    assert_param(IS_GPIO_PUPDR(GPIO_InitStruct->GPIO_PuPd));

    /* ------------------------- Configure the port pins ---------------- */
    /*-- GPIO Mode Configuration --*/
    for (pinpos = 0x00; pinpos < 0x08; pinpos++)
    {
        pos = ((uint32_t)0x01) << pinpos;
        /* Get the port pins position */
        currentpin = (GPIO_InitStruct->Pin) & pos;

        if (currentpin == pos)
        {
            GPIOx->AFR &= ~(GPIO_MODER_MODER0 << (pinpos * 4)); // 端口功能选择设置成IO功能
            GPIOx->INTANY &= ~(1 << pinpos);
            GPIOx->DRVCR &= ~(1 << pinpos);
            GPIOx->SLEWCR &= ~(1 << pinpos);
            GPIOx->INDBEN &= ~(1 << pinpos);
            switch (GPIO_InitStruct->Mode)
            {
            case GPIO_MODE_INPUT:
                GPIOx->DIRCR &= ~(1 << pinpos);
                break;
            case GPIO_MODE_OUTPUT_PP:
                GPIOx->DIRCR |= (1 << pinpos);
                GPIOx->OTYPER &= ~(1 << pinpos);
                break;
            case GPIO_MODE_OUTPUT_OD:
                GPIOx->DIRCR |= (1 << pinpos);
                GPIOx->OTYPER |= (1 << pinpos);
                break;
            case GPIO_Mode_ANALOG:
                GPIOx->DIRCR &= ~(1 << pinpos);
                HAL_GPIO_PinAFConfig1(GPIOx, currentpin, 0x0F);
                break;
            case GPIO_MODE_IT_LOW_LEVEL:
                GPIOx->DIRCR &= ~(1 << pinpos);
                GPIOx->INTEN |= (1 << pinpos);
                GPIOx->INTTYPCR |= (1 << pinpos);  // 电平触发
                GPIOx->INTPOLCR &= ~(1 << pinpos); // 低电平或下降沿触发中断
                break;
            case GPIO_MODE_IT_HIGH_LEVEL:
                GPIOx->DIRCR &= ~(1 << pinpos);
                GPIOx->INTEN |= (1 << pinpos);
                GPIOx->INTTYPCR |= (1 << pinpos); // 电平触发
                GPIOx->INTPOLCR |= (1 << pinpos); // 高电平或上升沿触发中断
                break;
            case GPIO_MODE_IT_RISING:
                GPIOx->DIRCR &= ~(1 << pinpos);
                GPIOx->INTEN |= (1 << pinpos);
                GPIOx->INTTYPCR &= ~(1 << pinpos); // 边沿触发
                GPIOx->INTPOLCR |= (1 << pinpos);  // 高电平或上升沿触发中断
                break;
            case GPIO_MODE_IT_FALLING:
                GPIOx->DIRCR &= ~(1 << pinpos);
                GPIOx->INTEN |= (1 << pinpos);
                GPIOx->INTTYPCR &= ~(1 << pinpos); // 边沿触发
                GPIOx->INTPOLCR &= ~(1 << pinpos); // 低电平或下降沿触发中断
                break;
            case GPIO_MODE_IT_BOTH_EDGE:
                GPIOx->DIRCR &= ~(1 << pinpos);
                GPIOx->INTEN |= (1 << pinpos);
                GPIOx->INTANY |= (1 << pinpos);
                break;
            }

            GPIOx->PUPDR &= ~(GPIO_PUPDR_PUPDR0 << ((uint16_t)pinpos * 2)); // 乘2，两位一起移
            GPIOx->PUPDR |= (((uint32_t)GPIO_InitStruct->PuPd) << (pinpos * 2));
            GPIOx->DRVCR |= (GPIO_InitStruct->Drive << pinpos);
            GPIOx->SLEWCR |= (GPIO_InitStruct->Speed << pinpos);

            if (GPIO_InitStruct->IndbEn)
            {
                GPIOx->INDBEN |= (1 << pinpos);
            }
            if (GPIOx->INDBEN & 0xFF)
            {
                GPIOx->INDBEN |= (1 << 8);
            }

            // 消抖
            GPIOx->DBCLKCR |= GPIO_InitStruct->Debounce;

            // if ((GPIO_InitStruct->GPIO_Mode == GPIO_Mode_OUT) || (GPIO_InitStruct->GPIO_Mode == GPIO_Mode_AF))
            // {
            //     /*set gpio direction*/
            //     GPIOx->DIRCR &= ~(GPIO_PIN_OUT_MODE << (uint8_t)pinpos); // default input mode
            //     GPIOx->DIRCR |= (GPIO_PIN_OUT_MODE << (uint8_t)pinpos);  // output mode
            //     /* Output mode configuration*/
            //     GPIOx->OTYPER &= ~((GPIO_OTYPER_OT_0) << ((uint8_t)pinpos)); // default pull-push output
            //     GPIOx->OTYPER |= (uint32_t)(((uint32_t)GPIO_InitStruct->GPIO_OType) << ((uint8_t)pinpos));
            // }
            // /* Pull-up Pull down resistor configuration*/
            // GPIOx->PUPDR &= ~(GPIO_PUPDR_PUPDR0 << ((uint16_t)pinpos * 2)); // 乘2，两位一起移
            // GPIOx->PUPDR |= (((uint32_t)GPIO_InitStruct->GPIO_PuPd) << (pinpos * 2));
        }
    }
}

/**
 * @brief  Reads the specified input port pin.
 * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral.
 * @param  Pin:  specifies the port bit to read.
 *   This parameter can be Pin_x where x can be (0..7).
 * @retval The input port pin value.
 */
uint8_t HAL_GPIO_ReadInputDataBit(GPIO_TypeDef *GPIOx, uint16_t Pin)
{
    uint8_t bitstatus = 0x00;
    /* Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_GET_GPIO_PIN(Pin));

    if ((GPIOx->IDR & Pin) != (uint32_t)Bit_RESET)
    {
        bitstatus = (uint8_t)Bit_SET;
    }
    else
    {
        bitstatus = (uint8_t)Bit_RESET;
    }
    return bitstatus;
}

/**
 * @brief  Reads the specified GPIO input data port.
 * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral.
 * @retval GPIO input data port value.
 */
uint16_t HAL_GPIO_ReadInputData(GPIO_TypeDef *GPIOx)
{
    /* Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));

    return ((uint8_t)GPIOx->IDR);
}

/**
 * @brief  Enable the specified GPIO port DB of SYNC_EN.
 * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral.
 * @param  NewState: new state of the sync_en.
 *         This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_GPIO_DBSyncCmd(GPIO_TypeDef *GPIOx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        GPIOx->INDBEN |= GPIO_DIDB_SYNCEN;
    }
    else
    {
        GPIOx->INDBEN &= ~GPIO_DIDB_SYNCEN;
    }
}

/**
  * @brief  Set the specified GPIOx peripheral pins DB function .
  * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral.
  * @param  DB_Pin: 端子Pxn(n=0~7)消抖使能配置位配置
  *   This parameter can be any combination of the following values:
       @arg GPIO_DIDB_PIN0EN     ((uint32_t)0x00000001)
       @arg GPIO_DIDB_PIN1EN     ((uint32_t)0x00000002)
       @arg GPIO_DIDB_PIN2EN     ((uint32_t)0x00000004)
       @arg GPIO_DIDB_PIN3EN     ((uint32_t)0x00000008)
       @arg GPIO_DIDB_PIN4EN     ((uint32_t)0x00000010)
       @arg GPIO_DIDB_PIN5EN     ((uint32_t)0x00000020)
       @arg GPIO_DIDB_PIN6EN     ((uint32_t)0x00000040)
       @arg GPIO_DIDB_PIN7EN     ((uint32_t)0x00000080)

  * @param  NewState: new state of DB function.
  *         This parameter can be: ENABLE or DISABLE.
  * @retval None
  */
void HAL_GPIO_DBPinSyncCmd(GPIO_TypeDef *GPIOx, uint32_t DB_Pin, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        GPIOx->INDBEN |= DB_Pin;
    }
    else
    {
        GPIOx->INDBEN &= ~DB_Pin;
    }
}

/**
 * @brief  Open the specified GPIO port DB Clock enable.
 * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral.
 * @param  NewState: new state of DB CLock enable.
 *         This parameter can be: ENABLE or DISABLE.
 * @retval None.
 */
void HAL_GPIO_DBClkCmd(GPIO_TypeDef *GPIOx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        GPIOx->DBCLKCR |= GPIO_DBCLK_EN;
    }
    else
    {
        GPIOx->DBCLKCR &= ~GPIO_DBCLK_EN;
    }
}

/**
  * @brief  Set the specified GPIO port DB Clock
  * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral.
  * @param  DBClk: DB Clock of cycle .
  *   This parameter can be the following values:
      @arg GPIO_DBCLK_1CYCLE            ((uint32_t)0x00000000)
      @arg GPIO_DBCLK_2CYCLE            ((uint32_t)0x00000001)
      @arg GPIO_DBCLK_4CYCLE            ((uint32_t)0x00000002)
      @arg GPIO_DBCLK_8CYCLE            ((uint32_t)0x00000003)
      @arg GPIO_DBCLK_16CYCLE           ((uint32_t)0x00000004)
      @arg GPIO_DBCLK_32CYCLE           ((uint32_t)0x00000005)
      @arg GPIO_DBCLK_64CYCLE           ((uint32_t)0x00000006)
      @arg GPIO_DBCLK_128CYCLE          ((uint32_t)0x00000007)
      @arg GPIO_DBCLK_256CYCLE          ((uint32_t)0x00000008)
      @arg GPIO_DBCLK_512CYCLE          ((uint32_t)0x00000009)
      @arg GPIO_DBCLK_1024CYCLE         ((uint32_t)0x0000000A)
      @arg GPIO_DBCLK_2048CYCLE         ((uint32_t)0x0000000B)
      @arg GPIO_DBCLK_4096CYCLE         ((uint32_t)0x0000000C)
      @arg GPIO_DBCLK_8192CYCLE         ((uint32_t)0x0000000D)
      @arg GPIO_DBCLK_16384CYCLE        ((uint32_t)0x0000000E)
    @arg GPIO_DBCLK_32768CYCLE        ((uint32_t)0x0000000F)

  * @retval None.
  */
void HAL_GPIO_SetDBClk(GPIO_TypeDef *GPIOx, uint32_t DBClk)
{
    /* Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_GPIO_DBCLK_CYCLE(DBClk));
    /* Set the specified GPIO port DB Clock */
    GPIOx->DBCLKCR &= ~GPIO_DBCLK_CYCLEMASK;
    GPIOx->DBCLKCR |= DBClk;
}

/**
 * @brief  Reads the specified output data port bit.
 * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral.
 * @param  Pin:  specifies the port bit to read.
 *         This parameter can be Pin_x where x can be (0..7).
 * @retval The output port pin value.
 */
uint8_t HAL_GPIO_ReadOutputDataBit(GPIO_TypeDef *GPIOx, uint16_t Pin)
{
    uint8_t bitstatus = 0x00;

    /* Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_GET_GPIO_PIN(Pin));

    if (((GPIOx->ODR) & Pin) != (uint32_t)Bit_RESET)
    {
        bitstatus = (uint8_t)Bit_SET;
    }
    else
    {
        bitstatus = (uint8_t)Bit_RESET;
    }
    return bitstatus;
}

/**
 * @brief  Reads the specified GPIO output data port.
 * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral.
 * @retval GPIO output data port value.
 */
uint16_t HAL_GPIO_ReadOutputData(GPIO_TypeDef *GPIOx)
{
    /* Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));

    return ((uint8_t)GPIOx->ODR);
}

/**
 * @brief  Sets the selected data port bits.
 * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral.
 * @param  Pin: specifies the port bits to be written.
 *         This parameter can be any combination of Pin_x where x can be (0..7).
 * @retval None
 */
void HAL_GPIO_SetBits(GPIO_TypeDef *GPIOx, uint16_t Pin)
{
    /*Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_GPIO_PIN(Pin));

    GPIOx->ODSET = Pin;
}

/**
 * @brief  Clears the selected data port bits.
 * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral.
 * @param  Pin: specifies the port bits to be written.
 *         This parameter can be any combination of GPIO_PIN_x where x can be (0..7).
 * @retval None
 */
void HAL_GPIO_ResetBits(GPIO_TypeDef *GPIOx, uint16_t Pin)
{
    /*Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_GPIO_PIN(Pin));

    GPIOx->ODCLR = Pin;
}

/**
 * @brief  Sets or clears the selected data port bit.
 * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral.
 * @param  Pin: specifies the port bit to be written.
 *         This parameter can be one of GPIO_PIN_x where x can be (0..7).
 * @param  BitVal: specifies the value to be written to the selected bit.
 *         This parameter can be one of the BitAction enum values:
 * @arg    Bit_RESET: to clear the port pin
 * @arg    Bit_SET: to set the port pin
 * @retval None
 */
void HAL_GPIO_WriteBit(GPIO_TypeDef *GPIOx, uint16_t Pin, BitAction BitVal)
{
    /* Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_GET_GPIO_PIN(Pin));
    assert_param(IS_GPIO_BIT_ACTION(BitVal));

    if (BitVal != Bit_RESET)
    {
        GPIOx->ODSET = Pin;
    }
    else
    {
        GPIOx->ODCLR = Pin;
    }
}

/**
 * @brief  Writes data to the specified GPIO data port.
 * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral.
 * @param  PortVal: specifies the value to be written to the port output data register.
 * @retval None
 */
void HAL_GPIO_Write(GPIO_TypeDef *GPIOx, uint16_t PortVal)
{
    /* Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));

    GPIOx->ODR = PortVal;
}

/**
 * @brief  Toggles the specified GPIO pins..
 * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral
 * @param  Pin: specifies the port bit to be written.
 *         This parameter can be one of GPIO_PIN_x where x can be (0..7).
 * @retval None
 */
void HAL_GPIO_ToggleBits(GPIO_TypeDef *GPIOx, uint16_t Pin)
{
    /*Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));

    GPIOx->ODR ^= Pin;
}

/**
  * @brief  Enables or disables pu pd
  * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral
  * @param  Pin: specifies the port bit to be written.
  *         This parameter can be one of GPIO_PIN_x where x can be (0..7).
  * @param  GPIO_PUPDR: set the GPIO peripheral port pin Pu or Pd
  *         This parameter can be the following values:
        @arg GPIO_PuPd_UP     0x01
        @GPIO_PuPd_DOWN       0x02
  * @retval None
  */
void HAL_GPIO_PinPuPdCmd(GPIO_TypeDef *GPIOx, uint16_t Pin, uint8_t GPIO_PUPDR)
{
    uint8_t pos = 0x00;
    /*Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_GET_GPIO_PIN(Pin));
    assert_param(IS_GPIO_PUPDR(GPIO_PUPDR));

    /*set the GPIO peripheral port pin Pu or Pd*/
    // GPIOx->PUPDR &= ~(GPIO_PuPd_ALL << (Pin * 2));   //Error
    // GPIOx->PUPDR |= (GPIO_PUPDR << (Pin * 2));    //Error

    switch (Pin)
    {
    case GPIO_PIN_0:
        pos = 0;
        break;
    case GPIO_PIN_1:
        pos = 2;
        break;
    case GPIO_PIN_2:
        pos = 4;
        break;
    case GPIO_PIN_3:
        pos = 6;
        break;
    case GPIO_PIN_4:
        pos = 8;
        break;
    case GPIO_PIN_5:
        pos = 10;
        break;
    case GPIO_PIN_6:
        pos = 12;
        break;
    case GPIO_PIN_7:
        pos = 14;
        break;
    default:
        break;
    }

    GPIOx->PUPDR &= ~(GPIO_PuPd_ALL << pos);
    GPIOx->PUPDR |= (GPIO_PUPDR << pos);
}

/**
  * @brief  Enables or disables port DRVCR/端口驱动强度使能
  * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral
  * @param  Pin: specifies the port bit to be written.
  *         This parameter can be one of GPIO_PIN_x where x can be (0..7).
  * @param  NewState: new state of Port DRIVER enable.
  *         This parameter can be: ENABLE or DISABLE.
        ENABLE  : 高驱动强度(复位默认)
        DISABLE : 低驱动强度
  * @retval None
  */
void HAL_GPIO_PortDRCmd(GPIO_TypeDef *GPIOx, uint8_t Pin, FunctionalState NewState)
{
    uint8_t pos = 0x00;
    /*Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_GET_GPIO_PIN(Pin));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    switch (Pin)
    {
    case GPIO_PIN_0:
        pos = 0;
        break;
    case GPIO_PIN_1:
        pos = 1;
        break;
    case GPIO_PIN_2:
        pos = 2;
        break;
    case GPIO_PIN_3:
        pos = 3;
        break;
    case GPIO_PIN_4:
        pos = 4;
        break;
    case GPIO_PIN_5:
        pos = 5;
        break;
    case GPIO_PIN_6:
        pos = 6;
        break;
    case GPIO_PIN_7:
        pos = 7;
        break;
    default:
        break;
    }

    if (NewState == DISABLE)
    {
        GPIOx->DRVCR |= ((uint32_t)0x01 << pos);
    }
    else
    {
        GPIOx->DRVCR &= ~((uint32_t)0x01 << pos);
    }
}

/**
 * @brief  Changes the mapping of the specified pin.
 * @param  GPIOx: where x can be (A..D) to select the GPIO peripheral
 * @param  GPIO_AFR: new state of the port pin remapping.
 * @param  Pin: specifies the port bit to be written.
 *         This parameter can be one of GPIO_PIN_x where x can be (0..7).
 * @retval None
 */
void HAL_GPIO_PinRemapConfig(GPIO_TypeDef *GPIOx, uint8_t GPIO_AFR, uint8_t Pin)
{
    uint32_t temp = 0x00, tmp = 0x00;
    uint8_t pos = 0x00;
    /* Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_GPIO_AF(GPIO_AFR));
    assert_param(IS_GET_GPIO_PIN(Pin));

    /*Changes the mapping of the specified pin*/
    /*
    temp = GPIOx->AFR & (~((uint32_t)0x0f << Pin));
    tmp  = temp | (GPIO_AFR << (Pin*4));
      GPIOx->AFR = tmp;
    */

    switch (Pin)
    {
    case GPIO_PIN_0:
        pos = 0;
        break;
    case GPIO_PIN_1:
        pos = 4;
        break;
    case GPIO_PIN_2:
        pos = 8;
        break;
    case GPIO_PIN_3:
        pos = 12;
        break;
    case GPIO_PIN_4:
        pos = 16;
        break;
    case GPIO_PIN_5:
        pos = 20;
        break;
    case GPIO_PIN_6:
        pos = 24;
        break;
    case GPIO_PIN_7:
        pos = 28;
        break;
    default:
        break;
    }

    temp = GPIOx->AFR & (~((uint32_t)0x0f << pos));
    tmp = temp | (GPIO_AFR << pos);
    GPIOx->AFR = tmp;
}

/**
 * @brief   Changes the mapping of the specified pin.
 * @param   GPIOx: where x can be (A..D) to select the GPIO peripheral
 * @param   GPIO_PinSource: specifies the pin for the Alternate function.
 *          This parameter can be GPIO_PinSourcex where x can be (0..7).
 * @param   GPIO_AF: selects the pin to used as Alternate function.
 *          This parameter can be can be (0..7,0x0F):
 * @retval  None
 */
void HAL_GPIO_PinAFConfig(GPIO_TypeDef *GPIOx, uint16_t GPIO_PinSource, uint8_t GPIO_AF)
{
    uint32_t temp = 0x00;

    /*Check the parameters*/
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_GPIO_PIN_SOURCE(GPIO_PinSource));
    assert_param(IS_GPIO_AF(GPIO_AF));

    temp = ((uint32_t)(GPIO_AF) << ((uint32_t)((uint32_t)GPIO_PinSource & (uint32_t)0x07) * 4));
    GPIOx->AFR &= ~((uint32_t)0xF << ((uint32_t)((uint32_t)GPIO_PinSource & (uint32_t)0x07) * 4));
    temp |= (GPIOx->AFR);
    GPIOx->AFR = temp;
}

void HAL_GPIO_PinAFConfig1(GPIO_TypeDef *GPIOx, uint32_t Pin, uint8_t GPIO_AF)
{
    uint32_t temp = 0x00;

    uint32_t index = 0;

    /*Check the parameters*/
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_GPIO_PIN_SOURCE(GPIO_PinSource));
    assert_param(IS_GPIO_AF(GPIO_AF));

    for (index = 0; index < 8; index++)
    {
        if ((Pin >> index) & 1)
        {
            break;
        }
    }

    if (index >= 8)
    {
        return;
    }

    temp = ((uint32_t)(GPIO_AF) << (index * 4));
    GPIOx->AFR &= ~((uint32_t)0xF << (index * 4));
    GPIOx->AFR |= temp;
}

const GPIO_UnitTypeDef *HAL_GPIO_Get_Pin(uint32_t num)
{
    //  if (PINFULL < num)
    //  {
    //    return null;
    //  }

    return &pinmap[num];
}

#endif
