#include "config.h"

#if (PRJ_NAME == IS31)
#include "IS31V04.c"
#elif (PRJ_NAME == B41V)
#include "B41V.c"
#elif (PRJ_NAME == B41V_LIN)
#include "B41V_LIN.c"
#elif (PRJ_NAME == P3011)
#include "P3011.c"
#elif (PRJ_NAME == LCN07)
#include "LCN07.c"
#elif (PRJ_NAME == P4)
#include "P4.c"
#elif (PRJ_NAME == C201)
#include "C201.c"
#elif (PRJ_NAME == ES33)
#include "ES33.c"
#elif (PRJ_NAME == LKZYB)
#include "LKZYB.c"
#elif (PRJ_NAME == AY5)
#include "AY5.c"
#elif (PRJ_NAME == EP35)
#include "EP35.c"
#elif (PRJ_NAME == MK04 || PRJ_NAME == B13)
#include "MK04.c"
#elif (PRJ_NAME == ES33_V01)
#include "ES33_V01.c"
#elif (PRJ_NAME == D21)
#include "D21.c"
#else
#error "Pls check PRJ_NAME"
#endif
