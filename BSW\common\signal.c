/*
  Copyright (C), 2018-2019, VCAE. Co., Ltd.
  FileName:    signal.c
  Description:  ���׵��źŻ���
  Version: 0.1
  Function List: 
	1. 
  History:    
	  <author>   <time>      <version >                <desc>
		benny      19/11/1        0.1                  �´���ģ��  
*/

/**********************************************************************************************
* External objects
**********************************************************************************************/

#include "drv.h"
#include "config.h"
#include "signal.h"

void UART_TAT_RCV_func(void);
void TASK_1S_func(void);
void TASK_10MS_func(void);
void KEY_func(void);

/**********************************************************************************************
* Global variables
**********************************************************************************************/

/**********************************************************************************************
* Constants and macros
**********************************************************************************************/

/**********************************************************************************************
* Local types
**********************************************************************************************/
typedef void (*void_fn_t)(void);

/**********************************************************************************************
* Local function prototypes
*********************************************************************************************/
static void dummy_cb_func(void);

/**********************************************************************************************
* Local variables
**********************************************************************************************/
static uint32_t sys_signal;

void_fn_t signal_func_list[] =
	{
#define SIGNAL_X(name) name##_func,
		SIGNAL_LIST
#undef SIGNAL_X

};
/**********************************************************************************************
* Local functions
**********************************************************************************************/
static void dummy_cb_func(void)
{
}

/**********************************************************************************************
* Global functions
**********************************************************************************************/

void signal_set(uint32_t signal)
{
	if (signal)
	{
		sys_signal |= signal;
	}
}

void signal_init(void)
{
	sys_signal = 0;
}

void signal_process(void)
{
	uint32_t i, mask;

	if (sys_signal)
	{
		mask = 1;
		for (i = 0; i < SIGNAL_NUM; i++)
		{
			if (sys_signal & mask)
			{
				signal_func_list[i]();
				sys_signal &= ~mask;
			}
			mask <<= 1;
		}
	}
}
