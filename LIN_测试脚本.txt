# LIN 协议测试脚本
# 基于 LIN_ZCUL_RLS_FRONTWIPER_IRMM_Protocol.ldf
# 测试日期: 2025-01-24

# ========================================
# 1. 基础通信验证
# ========================================

# 1.1 主节点发送 ZCUL_01 帧
SEND_FRAME ZCUL_01 0x31 [00 00 00 00 00 00 00 00]
VERIFY_FRAME_ID 0x31
VERIFY_FRAME_LENGTH 8
WAIT 10

# 1.2 主节点发送 ZCUL_02 帧
SEND_FRAME ZCUL_02 0x32 [00 00 00 00 00 00 00 00]
VERIFY_FRAME_ID 0x32
VERIFY_FRAME_LENGTH 8
WAIT 10

# 1.3 主节点发送 ZCUL_03 帧（IRMM 命令）
SEND_FRAME ZCUL_03 0x33 [01 00 00 00 00 00 00 00]  # 启用内部后视镜
VERIFY_FRAME_ID 0x33
VERIFY_FRAME_LENGTH 8
WAIT 10

# 1.4 验证 RLS 节点响应
REQUEST_FRAME RLS_01 0x23
EXPECT_RESPONSE 0x23 8
VERIFY_NAD 0x10
WAIT 10

# 1.5 验证 FRONTWIPER 节点响应
REQUEST_FRAME FRONTWIPER_01 0x25
EXPECT_RESPONSE 0x25 8
VERIFY_NAD 0x11
WAIT 10

# 1.6 验证 IRMM 节点响应
REQUEST_FRAME IRMM_01 0x26
EXPECT_RESPONSE 0x26 8
VERIFY_NAD 0x12
WAIT 10

# ========================================
# 2. 信号功能测试
# ========================================

# 2.1 测试雨刷控制信号
SEND_FRAME ZCUL_01 0x31 [02 00 00 00 00 00 00 00]  # ZCUL_CMD_AutoWiper = 1
WAIT 10
REQUEST_FRAME RLS_01 0x23
VERIFY_SIGNAL RLS_RQ_WiperSPD > 0  # 期望雨刷速度请求
WAIT 10

# 2.2 测试雨量敏感度设置
SEND_FRAME ZCUL_01 0x31 [10 00 00 00 00 00 00 00]  # ZCUL_RainSensitivity = 1
WAIT 10
REQUEST_FRAME RLS_01 0x23
VERIFY_SIGNAL RLS_RQ_RainLevel CHANGED
WAIT 10

# 2.3 测试内部后视镜防眩功能
SEND_FRAME ZCUL_03 0x33 [20 00 00 00 00 00 00 00]  # IntrCmdIntrMirrEna = 1
WAIT 10
REQUEST_FRAME IRMM_01 0x26
VERIFY_SIGNAL IntrMirrDimRespIntrMirrDimPerc >= 0
WAIT 10

# 2.4 测试前雨刷控制
SEND_FRAME ZCUL_02 0x32 [08 00 00 00 00 00 00 00]  # ZCUL_WSMLowSpdWipeReq_l = 1
WAIT 10
REQUEST_FRAME FRONTWIPER_01 0x25
VERIFY_SIGNAL WSMWipeSpd_l > 0
WAIT 10

# ========================================
# 3. 诊断功能测试
# ========================================

# 3.1 发送诊断请求到 RLS
SEND_DIAGNOSTIC_FRAME MasterReq 0x3C [10 22 F1 86 00 00 00 00]  # NAD=0x10, SID=0x22, PCI=0xF1
WAIT 50
EXPECT_DIAGNOSTIC_RESPONSE SlaveResp 0x3D
VERIFY_DIAGNOSTIC_NAD 0x10
WAIT 10

# 3.2 发送诊断请求到 FRONTWIPER
SEND_DIAGNOSTIC_FRAME MasterReq 0x3C [11 22 F1 86 00 00 00 00]  # NAD=0x11
WAIT 50
EXPECT_DIAGNOSTIC_RESPONSE SlaveResp 0x3D
VERIFY_DIAGNOSTIC_NAD 0x11
WAIT 10

# 3.3 发送诊断请求到 IRMM
SEND_DIAGNOSTIC_FRAME MasterReq 0x3C [12 22 F1 86 00 00 00 00]  # NAD=0x12
WAIT 50
EXPECT_DIAGNOSTIC_RESPONSE SlaveResp 0x3D
VERIFY_DIAGNOSTIC_NAD 0x12
WAIT 10

# ========================================
# 4. 调度表测试
# ========================================

# 4.1 执行完整的 Dynamic 调度表
START_SCHEDULE Dynamic
WAIT 100  # 等待一个完整的调度周期
STOP_SCHEDULE

# 4.2 验证调度表执行
VERIFY_SCHEDULE_EXECUTION Dynamic
VERIFY_FRAME_SEQUENCE [0x31, 0x32, 0x33, 0x23, 0x24, 0x31, 0x25, 0x26, 0x23, 0x24]
VERIFY_TIMING_CONSTRAINTS 10ms

# ========================================
# 5. 错误处理测试
# ========================================

# 5.1 测试无效帧ID
SEND_FRAME INVALID_FRAME 0xFF [00 00 00 00 00 00 00 00]
EXPECT_NO_RESPONSE
WAIT 10

# 5.2 测试校验和错误
SEND_FRAME_WITH_BAD_CHECKSUM ZCUL_01 0x31 [00 00 00 00 00 00 00 00]
EXPECT_NO_RESPONSE
WAIT 10

# 5.3 测试从节点错误响应
REQUEST_FRAME RLS_01 0x23
VERIFY_ERROR_SIGNAL RLS_REP_Error == 0  # 无错误
WAIT 10

REQUEST_FRAME FRONTWIPER_01 0x25
VERIFY_ERROR_SIGNAL WSMLINComEr == 0  # 无通信错误
WAIT 10

REQUEST_FRAME IRMM_01 0x26
VERIFY_ERROR_SIGNAL ErrRespIRMM == 0  # 无错误响应
WAIT 10

# ========================================
# 6. 性能测试
# ========================================

# 6.1 测试最大帧频率
START_PERFORMANCE_TEST
FOR i = 1 TO 100
    SEND_FRAME ZCUL_01 0x31 [00 00 00 00 00 00 00 00]
    WAIT 5  # 最小间隔
END_FOR
STOP_PERFORMANCE_TEST
VERIFY_NO_FRAME_LOSS

# 6.2 测试总线负载
START_BUS_LOAD_TEST
START_SCHEDULE Dynamic
WAIT 1000  # 运行1秒
STOP_SCHEDULE
STOP_BUS_LOAD_TEST
VERIFY_BUS_LOAD < 50%  # 总线负载应小于50%

# ========================================
# 7. 信号编码测试
# ========================================

# 7.1 测试内部后视镜防眩敏感度编码
SEND_FRAME ZCUL_03 0x33 [00 00 00 00 00 00 00 00]  # DimSnvty = 0 (Normal)
WAIT 10
SEND_FRAME ZCUL_03 0x33 [01 00 00 00 00 00 00 00]  # DimSnvty = 1 (Dark)
WAIT 10
SEND_FRAME ZCUL_03 0x33 [02 00 00 00 00 00 00 00]  # DimSnvty = 2 (Light)
WAIT 10
SEND_FRAME ZCUL_03 0x33 [03 00 00 00 00 00 00 00]  # DimSnvty = 3 (Inhibit)
WAIT 10

# 7.2 测试百分比编码
REQUEST_FRAME IRMM_01 0x26
VERIFY_SIGNAL_RANGE IntrMirrDimRespIntrMirrDimPerc 0 255
WAIT 10

# 7.3 测试速度编码
REQUEST_FRAME FRONTWIPER_01 0x25
VERIFY_SIGNAL_RANGE WSMWipeSpd_l 0 255
VERIFY_SIGNAL_UNIT WSMWipeSpd_l "times/min"
WAIT 10

# ========================================
# 8. 测试结果验证
# ========================================

PRINT "=== LIN 协议测试完成 ==="
PRINT "测试项目: 基础通信、信号功能、诊断、调度表、错误处理、性能、信号编码"
PRINT "测试结果: 请查看上述各项验证结果"
PRINT "如有失败项目，请检查对应的协议定义和节点实现"

# 生成测试报告
GENERATE_TEST_REPORT "LIN_Protocol_Test_Report.html"

# 测试脚本结束
END_TEST