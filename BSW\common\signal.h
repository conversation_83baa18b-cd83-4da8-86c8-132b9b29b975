#ifndef _SIGNAL_H_
#define _SIGNAL_H_

#define SIGNAL_LIST \
    SIGNAL_X(TASK_10MS)     \
    SIGNAL_X(TASK_1S)       \
    SIGNAL_X(UART_TAT_RCV) \
    SIGNAL_X(KEY)

enum SIGNAL_ID
{
    #define SIGNAL_X(name) name,
    SIGNAL_LIST
    #undef SIGNAL_X
    SIGNAL_NUM
};

enum SIGNAL_BITMASK
{
    #define SIGNAL_X(name) name##_SIGNAL = 1<<(name),
    SIGNAL_LIST
    #undef SIGNAL_X
    SIGNAL_ALL_BITMASK =(1 << SIGNAL_NUM) - 1
};


void signal_init(void);
void signal_process(void);
void signal_set(uint32_t signal);

#endif
