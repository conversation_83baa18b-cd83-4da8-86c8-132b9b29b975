#include "asm31ax_hal.h"

#ifdef HAL_LPTIMER_MODULE_ENABLED

/* Includes ------------------------------------------------------------------*/

/**
 * @brief  read cnt register
 * @param  None
 * @retval the cnt register value
 */
uint32_t HAL_LPTIMER_ReadCnt(void)
{
    return ((uint32_t)(LPTIMER->CNTVAL));
}

/**
 * @brief  Initializes the LPTIMERx Time Base Unit peripheral according to
 *         the specified parameters in the LPTIMER_InitStruct.
 * @param  LPTIMER_InitStruct: pointer to a LPTIMER_InitTypeDef structure that contains
 *         the configuration information for the RTC peripheral.
 * @retval None
 */
void HAL_LPTIMER_Init(LPTIMER_InitTypeDef *LPTIMER_InitStruct)
{
    uint32_t tmpreg = 0;
    /* Check the parameters */
    assert_param(IS_LPTIMER_ALL_PERIPH(LPTIMERx));
    assert_param(IS_LPTIMER_MODE(LPTIMER_InitStruct->Mode));
    assert_param(IS_LPTIMER_CT(LPTIMER_InitStruct->CTEN));
    assert_param(IS_LPTIMER_TCLK(LPTIMER_InitStruct->TCLK));
    assert_param(IS_LPTIMER_GATE(LPTIMER_InitStruct->GATEEN));
    assert_param(IS_LPTIMER_GATEPOLE(LPTIMER_InitStruct->GATEPOLE));
    assert_param(IS_LPTIMER_CUT(LPTIMER_InitStruct->TCLKCUTEN));

    /*read cr register*/
    tmpreg = LPTIMER_InitStruct->Instance->CR;

    /*set mode*/
    tmpreg &= ~LPTIMER_MODE_MASK;
    tmpreg |= LPTIMER_InitStruct->Mode;

    /*set CT*/
    tmpreg &= ~LPTIMER_CT_MASK;
    tmpreg |= LPTIMER_InitStruct->CTEN;

    /*set TCKL*/
    tmpreg &= ~LPTIMER_TCLK_MASK;
    tmpreg |= LPTIMER_InitStruct->TCLK;

    /*set GATE*/
    tmpreg &= ~LPTIMER_GATE_MASK;
    tmpreg |= LPTIMER_InitStruct->GATEEN;

    /*set GATEPOLE*/
    tmpreg &= ~LPTIMER_GATEPOLE_MASK;
    tmpreg |= LPTIMER_InitStruct->GATEPOLE;

    /*set TCLKCUT*/
    tmpreg &= ~LPTIMER_TCLKCUT_MASK;
    tmpreg |= LPTIMER_InitStruct->TCLKCUTEN;

    /*set TOGEN*/
    tmpreg &= ~LPTIMER_TOG_MASK;
    tmpreg |= LPTIMER_InitStruct->TogEn;

    LPTIMER_InitStruct->Instance->CR = tmpreg;

    do
    {
        tmpreg = LPTIMER_InitStruct->Instance->CR & LPTIMER_WT_FLAG;
    } while (tmpreg == LPTIMER_WT_FLAG);

    LPTIMER_InitStruct->Instance->LOAD = LPTIMER_InitStruct->Period;
    do
    {
        tmpreg = LPTIMER_InitStruct->Instance->CR & LPTIMER_WT_FLAG;
    } while (tmpreg == LPTIMER_WT_FLAG);
    LPTIMER_InitStruct->Instance->BGLOAD = LPTIMER_InitStruct->Period;
}

/**
 * @brief  Enable LPTIMER function
 * @param  LPTIMERx:Selects the LPTIMER peripheral
 *         This parameter can be one of the following values: LPTIMER
 * @param  NewState: new state of the LPTIMER peripheral.
 * This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_LPTIMER_Cmd(LPTIMER_TypeDef *LPTIMERx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_LPTIMER_ALL_PERIPH(LPTIMERx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*set lptimer run enable*/
        LPTIMERx->CR |= LPTIMER_RUN_ENABLE; // 定时器运行
    }
    else
    {
        /*set lptimer run disable*/
        LPTIMERx->CR &= ~LPTIMER_RUN_ENABLE; // 定时器禁止
    }
}

/**
 * @brief  Enable LPTIMER tog function
 * @param  LPTIMERx:Selects the LPTIMER peripheral
 *         This parameter can be one of the following values: LPTIMER
 * @param  NewState: new state of the LPTIMER peripheral.
 * This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_LPTIMER_TogCmd(LPTIMER_TypeDef *LPTIMERx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_LPTIMER_ALL_PERIPH(LPTIMERx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*set lptimer run enable*/
        LPTIMERx->CR |= LPTIMER_TOG_ENABLE; // TOG,TOGN输出相位相反的信号
    }
    else
    {
        /*set lptimer run disable*/
        LPTIMERx->CR &= ~LPTIMER_TOG_ENABLE; // TOG,TOGN同时输出0
    }
}

/**
 * @brief  Config LPTIMER Interrupt
 * @param  LPTIMERx: Selects the LPTIMER peripheral
 *         This parameter can be one of the following values: LPTIMER
 * @param  NewState: new state of the LPTIMER peripheral.
 * This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_LPTIMER_ITConfig(LPTIMER_TypeDef *LPTIMERx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_LPTIMER_ALL_PERIPH(LPTIMERx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*set lptimer run enable*/
        LPTIMERx->CR |= LPTIMER_IT_ENABLE;
    }
    else
    {
        /*set lptimer run disable*/
        LPTIMERx->CR &= ~LPTIMER_IT_ENABLE;
    }
}

/**
 * @brief  Gets the WT Flags of LPTIMER
 * @param  LPTIMERx:Selects the LPTIMER peripheral
 *         This parameter can be one of the following values: LPTIMER
 * @retval The new state of FlagStatus (SET or RESET).
 */
FlagStatus HAL_LPTIMER_GetWTFlagStatus(LPTIMER_TypeDef *LPTIMERx)
{
    FlagStatus bitstatus = RESET;

    /* Check the parameters*/
    assert_param(IS_LPTIMER_ALL_PERIPH(LPTIMERx));

    if ((LPTIMERx->CR & LPTIMER_WT_FLAG) != (uint16_t)RESET)
    {
        bitstatus = SET;
    }
    else
    {
        bitstatus = RESET;
    }

    return bitstatus;
}

/**
 * @brief  Config LPTIMER bgload
 * @param  LPTIMERx:Selects the LPTIMER peripheral
 *         This parameter can be one of the following values: LPTIMER
 * @param  Value: Set value to BGLOAD register
 * @retval None
 */
void HAL_LPTIMER_BGloadConfig(LPTIMER_TypeDef *LPTIMERx, uint32_t Value)
{
    __IO uint32_t initcounter = 0x00;
    uint32_t initstatus = 0x00;
    /* Check the parameters */
    assert_param(IS_LPTIMER_ALL_PERIPH(LPTIMERx));
    assert_param(IS_LPTIMER_BGLOAD(Value));

    /* Wait till LPTIMERx WT FLAG  be cleared zero */
    do
    {
        initstatus = LPTIMERx->CR & LPTIMER_WT_FLAG;
    } while (initstatus == LPTIMER_WT_FLAG);

    /* set LPTIMERx VALUE */
    LPTIMERx->BGLOAD = Value;
}

/**
 * @brief  Config LPTIMER Load
 * @param  LPTIMERx:Selects the LPTIMER peripheral
 *         This parameter can be one of the following values: LPTIMER
 * @param  Value: Set value to LOAD register
 * @retval None
 */
void HAL_LPTIMER_LoadConfig(LPTIMER_TypeDef *LPTIMERx, uint32_t Value)
{
    __IO uint32_t initcounter = 0x00;
    uint32_t initstatus = 0x00;
    /* Check the parameters */
    assert_param(IS_LPTIMER_ALL_PERIPH(LPTIMERx));
    assert_param(IS_LPTIMER_LOAD(Value));

    /* Wait till LPTIMERx WT FLAG  be cleared zero */
    do
    {
        initstatus = LPTIMERx->CR & LPTIMER_WT_FLAG;
    } while (initstatus == LPTIMER_WT_FLAG);
    /* set LPTIMERx VALUE */
    LPTIMERx->LOAD = Value;
}

/**
 * @brief  Get LPTIMER ITStatus
 * @param  LPTIMERx:Selects the LPTIMER peripheral
 *         This parameter can be one of the following values: LPTIMER
 * @param  IntFlag: LPTIMER interruption of flag
 * This parameter can be: LPTIMER_IT_FLAG.
 * @retval None
 */
ITStatus HAL_LPTIMER_GetITStatus(LPTIMER_TypeDef *LPTIMERx, uint32_t IntFlag)
{
    ITStatus bitstatus = RESET;
    /* Check the parameters */
    assert_param(IS_LPTIMER_ALL_PERIPH(LPTIMERx));
    assert_param(IS_LPTIMER_IT(IntFlag));

    /*get the it flag*/
    if ((LPTIMERx->ISR) & IntFlag)
    {
        bitstatus = SET;
    }
    else
    {
        bitstatus = RESET;
    }
    /*return the it flag*/
    return bitstatus;
}

/**
 * @brief  Clear LPTIMER ITStatus flag
 * @param  LPTIMERx:Selects the LPTIMER peripheral
 *         This parameter can be one of the following values: LPTIMER
 * @param  IntFlag:LPTIMER interruption of flag
 * This parameter can be: LPTIMER_IT_FLAG.
 * @retval None
 */
void HAL_LPTIMER_ClearITFlag(LPTIMER_TypeDef *LPTIMERx, uint32_t IntFlag)
{
    /*Check the parameters */
    assert_param(IS_LPTIMER_ALL_PERIPH(LPTIMERx));
    assert_param(IS_LPTIMER_IT(IntFlag));

    /*Clear IT Flag*/
    LPTIMERx->ICR = IntFlag;
}

#endif
