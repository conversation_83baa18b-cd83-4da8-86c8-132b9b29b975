<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Target 1</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pArmCC>5060750::V5.06 update 6 (build 750)::ARMCC</pArmCC>
      <uAC6>8</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>ASM31AX003</Device>
          <Vendor>Sinemicro</Vendor>
          <PackID>Sinemicro.ASM31XX.1.0.1</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IROM(0x00000000,0x10000) IRAM(0x20000000,0x1000) CPUTYPE("Cortex-M0+") CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000)</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ASM31AX003$Device\Include\asm31ax.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:ASM31AX003$SVD\asm31ax003.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>ASM31AX003</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>1</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>1</RunUserProg2>
            <UserProg1Name>objcopy -Iihex -Obinary .\Objects\ASM31AX003.hex .\Objects\ASM31AX003.bin</UserProg1Name>
            <UserProg2Name>release.exe</UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0+</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0+</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M0+"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>1</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x1000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x10000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x7800</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x7800</StartAddress>
                <Size>0x8800</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x1000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>0</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath>.\INC;..\..\BSW\common;..\App;..\..\BSW\test;RTE\Drivers\StdPeriph_ASM31AX\inc</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>1</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>0</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>.\ASM31AX003.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>SRC</GroupName>
          <Files>
            <File>
              <FileName>system_click.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\SRC\system_click.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>APP</GroupName>
          <Files>
            <File>
              <FileName>anti_piracy.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\anti_piracy.c</FilePath>
            </File>
            <File>
              <FileName>db.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\db.c</FilePath>
            </File>
            <File>
              <FileName>filter.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\filter.c</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\main.c</FilePath>
            </File>
            <File>
              <FileName>PID_Ctrl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\PID_Ctrl.c</FilePath>
            </File>
            <File>
              <FileName>Tab.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\Tab.c</FilePath>
            </File>
            <File>
              <FileName>signal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\BSW\common\signal.c</FilePath>
            </File>
            <File>
              <FileName>board.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\BSW\Board\board.c</FilePath>
            </File>
            <File>
              <FileName>sw_version.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\sw_version.c</FilePath>
            </File>
            <File>
              <FileName>tuning.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\BSW\test\tuning.c</FilePath>
            </File>
            <File>
              <FileName>parm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\parm\parm.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drivers</GroupName>
          <Files>
            <File>
              <FileName>asm31ax_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_adc.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_advtim.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_advtim.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_awk.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_awk.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_beeper.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_beeper.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_clktrim.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_clktrim.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_crc.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_debug.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_debug.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_delay.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_exti.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_flash.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_i2c.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_lptimer.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_lptimer.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_lpuart.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_lpuart.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_lvd.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_lvd.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_owire.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_owire.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_pca.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_pca.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_rtc.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_spi.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_syscon.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_syscon.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_timer.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_uart.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_vc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_vc.c</FilePath>
            </File>
            <File>
              <FileName>asm31ax_hal_wwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\RTE\Drivers\StdPeriph_ASM31AX\src\asm31ax_hal_wwdg.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
        <Group>
          <GroupName>::Device</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <packages>
      <filter>
        <targetInfos/>
      </filter>
      <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.6.0">
        <targetInfos>
          <targetInfo name="Target 1" versionMatchMode="fixed"/>
        </targetInfos>
      </package>
      <package name="ASM31XX" schemaVersion="1.4" supportContact="<EMAIL>" url="http://www.keil.com/pack/" vendor="Sinemicro" version="1.0.1">
        <targetInfos>
          <targetInfo name="Target 1" versionMatchMode="fixed"/>
        </targetInfos>
      </package>
    </packages>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="5.3.0" condition="ARMv6_7_8-M Device">
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.6.0"/>
        <targetInfos>
          <targetInfo name="Target 1"/>
        </targetInfos>
      </component>
      <component Cclass="Device" Cgroup="Startup" Cvendor="Sinemicro" Cversion="1.0.0" condition="ASM31AX STDPERIPH">
        <package name="ASM31XX" schemaVersion="1.4" supportContact="<EMAIL>" url="http://www.keil.com/pack/" vendor="Sinemicro" version="1.0.1"/>
        <targetInfos>
          <targetInfo name="Target 1"/>
        </targetInfos>
      </component>
    </components>
    <files>
      <file attr="config" category="sourceAsm" condition="ARMCC" name="Device\ARM\ARMCM0plus\Source\ARM\startup_ARMCM0plus.s" version="1.0.0">
        <instance index="0" removed="1">RTE\Device\ARMCM0P\startup_ARMCM0plus.s</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="ARM" Cversion="1.2.0" condition="ARMCM0+ CMSIS"/>
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.6.0"/>
        <targetInfos/>
      </file>
      <file attr="config" category="sourceC" name="Device\ARM\ARMCM0plus\Source\system_ARMCM0plus.c" version="1.0.0">
        <instance index="0" removed="1">RTE\Device\ARMCM0P\system_ARMCM0plus.c</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="ARM" Cversion="1.2.0" condition="ARMCM0+ CMSIS"/>
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.6.0"/>
        <targetInfos/>
      </file>
      <file attr="config" category="source" name="Device\Source\asm31ax_hal_it.c" version="1.0.1">
        <instance index="0" removed="1">RTE\Device\ASM31AX003\asm31ax_hal_it.c</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Sinemicro" Cversion="1.0.0"/>
        <package name="ASM31XX" schemaVersion="1.4" supportContact="<EMAIL>" url="http://www.keil.com/pack/" vendor="Sinemicro" version="1.0.1"/>
        <targetInfos/>
      </file>
      <file attr="config" category="source" name="Device\Source\asm31ax_it.c" version="1.0.0">
        <instance index="0">RTE\Device\ASM31AX003\asm31ax_it.c</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Sinemicro" Cversion="1.0.0" condition="ASM31AX STDPERIPH"/>
        <package name="ASM31XX" schemaVersion="1.4" supportContact="<EMAIL>" url="http://www.keil.com/pack/" vendor="Sinemicro" version="1.0.1"/>
        <targetInfos>
          <targetInfo name="Target 1"/>
        </targetInfos>
      </file>
      <file attr="config" category="source" condition="Startup ARM" name="Device\Source\ARM\startup_asm31ax.s" version="1.0.0">
        <instance index="0">RTE\Device\ASM31AX003\startup_asm31ax.s</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Sinemicro" Cversion="1.0.0" condition="ASM31AX STDPERIPH"/>
        <package name="ASM31XX" schemaVersion="1.4" supportContact="<EMAIL>" url="http://www.keil.com/pack/" vendor="Sinemicro" version="1.0.1"/>
        <targetInfos>
          <targetInfo name="Target 1"/>
        </targetInfos>
      </file>
      <file attr="config" category="source" name="Device\Source\system_asm31ax.c" version="1.0.0">
        <instance index="0">RTE\Device\ASM31AX003\system_asm31ax.c</instance>
        <component Cclass="Device" Cgroup="Startup" Cvendor="Sinemicro" Cversion="1.0.0" condition="ASM31AX STDPERIPH"/>
        <package name="ASM31XX" schemaVersion="1.4" supportContact="<EMAIL>" url="http://www.keil.com/pack/" vendor="Sinemicro" version="1.0.1"/>
        <targetInfos>
          <targetInfo name="Target 1"/>
        </targetInfos>
      </file>
      <file attr="config" category="header" name="Device\Include\asm31ax_hal_conf.h" version="1.0.0">
        <instance index="0" removed="1">RTE\Drivers\ASM31AX003\asm31ax_hal_conf.h</instance>
        <component Cclass="Drivers" Cgroup="StdPeriph Drivers" Cvendor="Sinemicro" Cversion="1.0.0" condition="ASM31AX STDPERIPH"/>
        <package name="ASM31XX" schemaVersion="1.4" supportContact="<EMAIL>" url="http://www.keil.com/pack/" vendor="Sinemicro" version="1.0.1"/>
        <targetInfos/>
      </file>
    </files>
  </RTE>

</Project>
