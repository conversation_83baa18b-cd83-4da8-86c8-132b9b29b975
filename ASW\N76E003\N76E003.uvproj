<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_proj.xsd">

  <SchemaVersion>1.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Target 1</TargetName>
      <ToolsetNumber>0x0</ToolsetNumber>
      <ToolsetName>MCS-51</ToolsetName>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>N76E003</Device>
          <Vendor>Nuvoton</Vendor>
          <Cpu>IRAM(0 - 0xFF) IROM(0 - 0x47FF)  XRAM(0 - 0x2FF) CLOCK(16000000)</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile>"LIB\STARTUP.A51" ("Standard 8051 Startup Code")</StartupFile>
          <FlashDriverDll></FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>N76E003.H</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath>Nuvoton\</RegisterFilePath>
          <DBRegisterFilePath>Nuvoton\</DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>N76E003</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>0</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>1</RunUserProg2>
            <UserProg1Name>hex2bin.exe .\Objects\N76E003.hex</UserProg1Name>
            <UserProg2Name>release.exe</UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
          <BankNo>65535</BankNo>
        </CommonProperty>
        <DllOption>
          <SimDllName>S8051.DLL</SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll>DP51.DLL</SimDlgDll>
          <SimDlgDllArguments></SimDlgDllArguments>
          <TargetDllName>S8051.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TP51.DLL</TargetDlgDll>
          <TargetDlgDllArguments></TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>0</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>1</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>1</RestoreTracepoints>
            <RestoreSysVw>1</RestoreSysVw>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>11</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>BIN\Nuvoton_8051_Keil_uVision_Driver.dll</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4101</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\Nuvoton_8051_Keil_uVision_Driver.dll</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <Target51>
          <Target51Misc>
            <MemoryModel>2</MemoryModel>
            <RTOS>0</RTOS>
            <RomSize>2</RomSize>
            <DataHold>0</DataHold>
            <XDataHold>0</XDataHold>
            <UseOnchipRom>0</UseOnchipRom>
            <UseOnchipArithmetic>0</UseOnchipArithmetic>
            <UseMultipleDPTR>0</UseMultipleDPTR>
            <UseOnchipXram>0</UseOnchipXram>
            <HadIRAM>1</HadIRAM>
            <HadXRAM>1</HadXRAM>
            <HadIROM>1</HadIROM>
            <Moda2>0</Moda2>
            <Moddp2>0</Moddp2>
            <Modp2>0</Modp2>
            <Mod517dp>0</Mod517dp>
            <Mod517au>0</Mod517au>
            <Mode2>0</Mode2>
            <useCB>0</useCB>
            <useXB>0</useXB>
            <useL251>0</useL251>
            <useA251>0</useA251>
            <Mx51>0</Mx51>
            <ModC812>0</ModC812>
            <ModCont>0</ModCont>
            <Lp51>0</Lp51>
            <useXBS>0</useXBS>
            <ModDA>0</ModDA>
            <ModAB2>0</ModAB2>
            <Mx51P>0</Mx51P>
            <hadXRAM2>0</hadXRAM2>
            <uocXram2>0</uocXram2>
            <hadXRAM3>0</hadXRAM3>
            <ModC2>0</ModC2>
            <ModH2>0</ModH2>
            <Mdu_R515>0</Mdu_R515>
            <Mdu_F120>0</Mdu_F120>
            <Psoc>0</Psoc>
            <hadIROM2>0</hadIROM2>
            <hadIROM3>0</hadIROM3>
            <ModSmx2>0</ModSmx2>
            <cBanks>0</cBanks>
            <xBanks>0</xBanks>
            <OnChipMemories>
              <RCB>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0xffff</Size>
              </RCB>
              <RXB>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </RXB>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocr1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocr1>
              <Ocr2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocr2>
              <Ocr3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocr3>
              <IRO>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x4800</Size>
              </IRO>
              <IRA>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x100</Size>
              </IRA>
              <XRA>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x300</Size>
              </XRA>
              <XRA512>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRA512>
              <IROM512>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM512>
              <XRA513>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRA513>
              <IROM513>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM513>
            </OnChipMemories>
          </Target51Misc>
          <C51>
            <RegisterColoring>0</RegisterColoring>
            <VariablesInOrder>0</VariablesInOrder>
            <IntegerPromotion>1</IntegerPromotion>
            <uAregs>1</uAregs>
            <UseInterruptVector>1</UseInterruptVector>
            <Fuzzy>3</Fuzzy>
            <Optimize>8</Optimize>
            <WarningLevel>2</WarningLevel>
            <SizeSpeed>1</SizeSpeed>
            <ObjectExtend>1</ObjectExtend>
            <ACallAJmp>0</ACallAJmp>
            <InterruptVectorAddress>0</InterruptVectorAddress>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>FOSC_160000  ASW_ENABLED</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\BSW\N76E003\lib\inc;..\..\BSW\common;..\..\BSW\test;..\App</IncludePath>
            </VariousControls>
          </C51>
          <Ax51>
            <UseMpl>0</UseMpl>
            <UseStandard>1</UseStandard>
            <UseCase>0</UseCase>
            <UseMod51>0</UseMod51>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Ax51>
          <Lx51>
            <useFile>0</useFile>
            <linkonly>0</linkonly>
            <UseMemoryFromTarget>1</UseMemoryFromTarget>
            <CaseSensitiveSymbols>0</CaseSensitiveSymbols>
            <WarningLevel>2</WarningLevel>
            <DataOverlaying>1</DataOverlaying>
            <OverlayString></OverlayString>
            <MiscControls></MiscControls>
            <DisableWarningNumbers>16</DisableWarningNumbers>
            <LinkerCmdFile></LinkerCmdFile>
            <Assign></Assign>
            <ReserveString></ReserveString>
            <CClasses></CClasses>
            <UserClasses></UserClasses>
            <CSection></CSection>
            <UserSection></UserSection>
            <CodeBaseAddress></CodeBaseAddress>
            <XDataBaseAddress></XDataBaseAddress>
            <PDataBaseAddress></PDataBaseAddress>
            <BitBaseAddress></BitBaseAddress>
            <DataBaseAddress></DataBaseAddress>
            <IDataBaseAddress></IDataBaseAddress>
            <Precede></Precede>
            <Stack></Stack>
            <CodeSegmentName>?CO?SW_VERSION(0x100),?CO?PARM(0x3800)</CodeSegmentName>
            <XDataSegmentName></XDataSegmentName>
            <BitSegmentName></BitSegmentName>
            <DataSegmentName></DataSegmentName>
            <IDataSegmentName></IDataSegmentName>
          </Lx51>
        </Target51>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>DRV</GroupName>
          <Files>
            <File>
              <FileName>vcae_c51adapt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\BSW\N76E003\lib\src\vcae_c51adapt.c</FilePath>
            </File>
            <File>
              <FileName>STARTUP.A51</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\BSW\N76E003\lib\src\STARTUP.A51</FilePath>
            </File>
            <File>
              <FileName>ADC.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\BSW\N76E003\lib\src\ADC.c</FilePath>
            </File>
            <File>
              <FileName>flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\BSW\N76E003\lib\src\flash.c</FilePath>
            </File>
            <File>
              <FileName>PWM.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\BSW\N76E003\lib\src\PWM.c</FilePath>
            </File>
            <File>
              <FileName>sys.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\BSW\N76E003\lib\src\sys.c</FilePath>
            </File>
            <File>
              <FileName>TIMER.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\BSW\N76E003\lib\src\TIMER.c</FilePath>
            </File>
            <File>
              <FileName>UART.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\BSW\N76E003\lib\src\UART.c</FilePath>
            </File>
            <File>
              <FileName>WDOG.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\BSW\N76E003\lib\src\WDOG.c</FilePath>
            </File>
            <File>
              <FileName>board.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\BSW\Board\board.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>APP</GroupName>
          <Files>
            <File>
              <FileName>anti_piracy.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\anti_piracy.c</FilePath>
            </File>
            <File>
              <FileName>db.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\db.c</FilePath>
            </File>
            <File>
              <FileName>filter.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\filter.c</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\main.c</FilePath>
            </File>
            <File>
              <FileName>PID_Ctrl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\PID_Ctrl.c</FilePath>
            </File>
            <File>
              <FileName>Tab.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\Tab.c</FilePath>
            </File>
            <File>
              <FileName>signal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\BSW\common\signal.c</FilePath>
            </File>
            <File>
              <FileName>tuning.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\BSW\test\tuning.c</FilePath>
            </File>
            <File>
              <FileName>sw_version.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\App\sw_version.c</FilePath>
            </File>
            <File>
              <FileName>parm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\parm\parm.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

</Project>
