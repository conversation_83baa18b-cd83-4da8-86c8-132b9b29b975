LIN_description_file;
LIN_protocol_version = "2.1";
LIN_language_version = "2.1";
LIN_speed = 19200;

Nodes {
  Master: ZCUL, 5 ms, 0.1 ms ;
  Slaves: RLS, FRONTWIPER, IRMM ;
}

Signals {
  /* ZCUL_01 Signals */
  ZCUL_Status_IGN: 1, 0, ZCU<PERSON>, RLS, FRONTWIPER, IRMM ;
  ZCUL_CMD_AutoWiper: 1, 0, ZCU<PERSON>, RLS, FRONTWIPER, IRMM ;
  ZCUL_RQ_FrontWash: 1, 0, ZCUL, RLS, FRONTWIPER, IRMM ;
  ZCUL_ParkPosition: 1, 0, ZCU<PERSON>, RLS, FRONTWIPER, IRMM ;
  ZCUL_RainSensitivity: 3, 0, <PERSON>CU<PERSON>, R<PERSON>, <PERSON>RONTWIPER, IRMM ;
  ZCUL_Turnofflightgear: 1, 0, ZCU<PERSON>, RLS, FRONTWIPER, IRMM ;
  ZCUL_RoofStatus: 1, 0, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>RONT<PERSON><PERSON><PERSON>, IRMM ;
  ZCUL_ScreenType: 1, 0, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, FRO<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ;
  <PERSON><PERSON><PERSON>_SPD_Vehicle: 8, 0, <PERSON>CU<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON> ;
  ZCUL_LightSensitivity: 3, 0, ZCUL, RLS, FRONTWIPER, IRMM ;
  ZCUL_OutsideTemp: 8, 0, ZCUL, RLS, FRONTWIPER, IRMM ;
  ZCUL_WiperType: 1, 0, ZCUL, RLS, FRONTWIPER, IRMM ;
  ZCUL_Vehicle_Type: 2, 0, ZCUL, RLS, FRONTWIPER, IRMM ;
  ZCUL_SteeringWheelPosition: 1, 0, ZCUL, RLS, FRONTWIPER, IRMM ;
  ZCUL_HUDConfiguration: 1, 0, ZCUL, RLS, FRONTWIPER, IRMM ;
  ZCUL_AutoCloseSR: 1, 0, ZCUL, RLS, FRONTWIPER, IRMM ;
  ZCUL_Rain_Light: 1, 0, ZCUL, RLS, FRONTWIPER, IRMM ;
  ZCUL_Speed_Light: 1, 0, ZCUL, RLS, FRONTWIPER, IRMM ;
  ZCUL_ActivationWipe: 1, 0, ZCUL, RLS, FRONTWIPER, IRMM ;
  ZCUL_HighSensetiveWipe: 1, 0, ZCUL, RLS, FRONTWIPER, IRMM ;
  ZCUL_Ice_InhibitWiping: 1, 0, ZCUL, RLS, FRONTWIPER, IRMM ;
  ZCUL_Speed_Depency: 1, 0, ZCUL, RLS, FRONTWIPER, IRMM ;
  
  /* RLS_01 Signals */
  RLS_RQ_WiperSPD: 8, 0, RLS, ZCUL ;
  RLS_RQ_RainLevel: 8, 0, RLS, ZCUL ;
  RLS_RQ_LowBeam: 1, 0, RLS, ZCUL ;
  RLS_RQ_PositionLamp: 1, 0, RLS, ZCUL ;
  RLS_Fault_Rain: 1, 0, RLS, ZCUL ;
  RLS_Fault_Light: 1, 0, RLS, ZCUL ;
  RLS_REP_Error: 1, 0, RLS, ZCUL ;
  RLS_VOLT_Error: 1, 0, RLS, ZCUL ;
  RLS_Win_Close_Cmd: 1, 0, RLS, ZCUL ;
  RLS_Solar_Driver: 8, 0, RLS, ZCUL ;
  RLS_Solar_Passenger: 8, 0, RLS, ZCUL ;
  RLS_TopBrightnessRawValue: 8, 0, RLS, ZCUL ;
  RLS_TopBrightnessUnit: 8, 0, RLS, ZCUL ;
  RLS_ADC_B: 8, 0, RLS, ZCUL ;
  
  /* RLS_02 Signals */
  RLS_HUDBrightnessRawValue: 8, 0, RLS, ZCUL ;
  RLS_HUDBrightnessUnit: 8, 0, RLS, ZCUL ;
  RLS_ADC_A: 8, 0, RLS, ZCUL ;
  RLS_Humid: 8, 0, RLS, ZCUL ;
  RLS_Temperature: 8, 0, RLS, ZCUL ;
  RLS_TempDewPoint: 8, 0, RLS, ZCUL ;
  RLS_Humid_Temp_Error: 1, 0, RLS, ZCUL ;
  
  /* ZCUL_02 Signals */
  ZCUL_WSMSrvcPosReq_l: 1, 0, ZCUL, FRONTWIPER ;
  ZCUL_WSMSglWipeReq_l: 1, 0, ZCUL, FRONTWIPER ;
  ZCUL_WSMRLSEnb_l: 1, 0, ZCUL, FRONTWIPER ;
  ZCUL_WSMLowSpdWipeReq_l: 1, 0, ZCUL, FRONTWIPER ;
  ZCUL_WSMHiSpdWipeReq_l: 1, 0, ZCUL, FRONTWIPER ;
  ZCUL_WSMFnEnb_l: 1, 0, ZCUL, FRONTWIPER ;
  ZCUL_BCMSysPwrMd_l: 3, 0, ZCUL, FRONTWIPER ;
  ZCUL_LowAcurcVehSpdAvgNonD_l: 8, 0, ZCUL, FRONTWIPER ;
  ZCUL_WSMRLSWipeReq_I: 8, 0, ZCUL, FRONTWIPER ;
  
  /* FRONTWIPER_01 Signals */
  WSMWipeAng_l: 8, 0, FRONTWIPER, ZCUL ;
  WSMWipeSpd_l: 8, 0, FRONTWIPER, ZCUL ;
  WSMLckdRtoSta_l: 1, 0, FRONTWIPER, ZCUL ;
  WSMLowVolFdbk_l: 1, 0, FRONTWIPER, ZCUL ;
  WSMOverTemFdbk_l: 1, 0, FRONTWIPER, ZCUL ;
  WSMOverVolFdbk_l: 1, 0, FRONTWIPER, ZCUL ;
  WSMWipeArmTrnng_l: 1, 0, FRONTWIPER, ZCUL ;
  WSMWipeInParkPos_l: 1, 0, FRONTWIPER, ZCUL ;
  WSMWipeInSrvcPos_l: 1, 0, FRONTWIPER, ZCUL ;
  WSMWipngA_l: 1, 0, FRONTWIPER, ZCUL ;
  WSMWorkngErFdbk_l: 1, 0, FRONTWIPER, ZCUL ;
  WSMLINComEr: 1, 0, FRONTWIPER, ZCUL ;
  
  /* ZCUL_03 Signals */
  IntrCmdIntrMirrDimSnvty: 2, 0, ZCUL, IRMM ;
  IntrCmdDrvrSide: 1, 0, ZCUL, IRMM ;
  IntrCmdMirrAsyFanCmpMag: 1, 0, ZCUL, IRMM ;
  IntrCmdIntrMirrDiagcRst: 1, 0, ZCUL, IRMM ;
  IntrCmdIntrMirrEna: 1, 1, ZCUL, IRMM ;
  IntrCmdIntrMirrInhbDim: 1, 0, ZCUL, IRMM ;
  IntrCmdIntrMirrWindHeatMag: 9, 0, ZCUL, IRMM ;
  
  /* IRMM_01 Signals */
  IntrMirrDimRespIntrMirrDimPerc: 8, 0, IRMM, ZCUL ;
  IntrMirrDimRespIntrMirrIntFailr: 1, 0, IRMM, ZCUL ;
  IntrMirrDimRespResdBoolean: 1, 1, IRMM, ZCUL ;
  IntrMirrDimRespResdUInt6: 5, 31, IRMM, ZCUL ;
  ErrRespIRMM: 1, 0, IRMM, ZCUL ;
}

Diagnostic_signals {
  MasterReqB0: 8, 0 ;
  MasterReqB1: 8, 0 ;
  MasterReqB2: 8, 0 ;
  MasterReqB3: 8, 0 ;
  MasterReqB4: 8, 0 ;
  MasterReqB5: 8, 0 ;
  MasterReqB6: 8, 0 ;
  MasterReqB7: 8, 0 ;
  SlaveRespB0: 8, 0 ;
  SlaveRespB1: 8, 0 ;
  SlaveRespB2: 8, 0 ;
  SlaveRespB3: 8, 0 ;
  SlaveRespB4: 8, 0 ;
  SlaveRespB5: 8, 0 ;
  SlaveRespB6: 8, 0 ;
  SlaveRespB7: 8, 0 ;
}

Frames {
  ZCUL_01: 0x31, ZCUL, 8 {
    ZCUL_Status_IGN, 0 ;
    ZCUL_CMD_AutoWiper, 1 ;
    ZCUL_RQ_FrontWash, 2 ;
    ZCUL_ParkPosition, 3 ;
    ZCUL_RainSensitivity, 4 ;
    ZCUL_Turnofflightgear, 7 ;
    ZCUL_RoofStatus, 8 ;
    ZCUL_ScreenType, 9 ;
    ZCUL_SPD_Vehicle, 16 ;
    ZCUL_LightSensitivity, 24 ;
    ZCUL_OutsideTemp, 32 ;
    ZCUL_WiperType, 40 ;
    ZCUL_Vehicle_Type, 41 ;
    ZCUL_SteeringWheelPosition, 43 ;
    ZCUL_HUDConfiguration, 44 ;
    ZCUL_AutoCloseSR, 45 ;
    ZCUL_Rain_Light, 46 ;
    ZCUL_Speed_Light, 47 ;
    ZCUL_ActivationWipe, 48 ;
    ZCUL_HighSensetiveWipe, 49 ;
    ZCUL_Ice_InhibitWiping, 50 ;
    ZCUL_Speed_Depency, 51 ;
  }
  
  RLS_01: 0x23, RLS, 8 {
    RLS_RQ_WiperSPD, 0 ;
    RLS_RQ_RainLevel, 8 ;
    RLS_RQ_LowBeam, 16 ;
    RLS_RQ_PositionLamp, 17 ;
    RLS_Fault_Rain, 18 ;
    RLS_Fault_Light, 19 ;
    RLS_REP_Error, 20 ;
    RLS_VOLT_Error, 21 ;
    RLS_Win_Close_Cmd, 22 ;
    RLS_Solar_Driver, 24 ;
    RLS_Solar_Passenger, 32 ;
    RLS_TopBrightnessRawValue, 40 ;
    RLS_TopBrightnessUnit, 48 ;
    RLS_ADC_B, 56 ;
  }
  
  RLS_02: 0x24, RLS, 8 {
    RLS_HUDBrightnessRawValue, 0 ;
    RLS_HUDBrightnessUnit, 8 ;
    RLS_ADC_A, 16 ;
    RLS_Humid, 24 ;
    RLS_Temperature, 32 ;
    RLS_TempDewPoint, 40 ;
    RLS_Humid_Temp_Error, 48 ;
  }
  
  ZCUL_02: 0x32, ZCUL, 8 {
    ZCUL_WSMSrvcPosReq_l, 0 ;
    ZCUL_WSMSglWipeReq_l, 1 ;
    ZCUL_WSMRLSEnb_l, 2 ;
    ZCUL_WSMLowSpdWipeReq_l, 3 ;
    ZCUL_WSMHiSpdWipeReq_l, 4 ;
    ZCUL_WSMFnEnb_l, 5 ;
    ZCUL_BCMSysPwrMd_l, 8 ;
    ZCUL_LowAcurcVehSpdAvgNonD_l, 16 ;
    ZCUL_WSMRLSWipeReq_I, 24 ;
  }
  
  FRONTWIPER_01: 0x25, FRONTWIPER, 8 {
    WSMWipeAng_l, 0 ;
    WSMWipeSpd_l, 8 ;
    WSMLckdRtoSta_l, 16 ;
    WSMLowVolFdbk_l, 17 ;
    WSMOverTemFdbk_l, 18 ;
    WSMOverVolFdbk_l, 19 ;
    WSMWipeArmTrnng_l, 20 ;
    WSMWipeInParkPos_l, 21 ;
    WSMWipeInSrvcPos_l, 22 ;
    WSMWipngA_l, 23 ;
    WSMWorkngErFdbk_l, 24 ;
    WSMLINComEr, 25 ;
  }
  
  ZCUL_03: 0x33, ZCUL, 8 {
    IntrCmdIntrMirrDimSnvty, 0 ;
    IntrCmdDrvrSide, 2 ;
    IntrCmdMirrAsyFanCmpMag, 3 ;
    IntrCmdIntrMirrDiagcRst, 4 ;
    IntrCmdIntrMirrEna, 5 ;
    IntrCmdIntrMirrInhbDim, 6 ;
    IntrCmdIntrMirrWindHeatMag, 7 ;
  }
  
  IRMM_01: 0x26, IRMM, 8 {
    IntrMirrDimRespIntrMirrDimPerc, 0 ;
    IntrMirrDimRespIntrMirrIntFailr, 8 ;
    IntrMirrDimRespResdBoolean, 9 ;
    IntrMirrDimRespResdUInt6, 10 ;
    ErrRespIRMM, 15 ;
  }
}

Diagnostic_frames {
  MasterReq: 0x3C {
    MasterReqB0, 0 ;
    MasterReqB1, 8 ;
    MasterReqB2, 16 ;
    MasterReqB3, 24 ;
    MasterReqB4, 32 ;
    MasterReqB5, 40 ;
    MasterReqB6, 48 ;
    MasterReqB7, 56 ;
  }
  SlaveResp: 0x3D {
    SlaveRespB0, 0 ;
    SlaveRespB1, 8 ;
    SlaveRespB2, 16 ;
    SlaveRespB3, 24 ;
    SlaveRespB4, 32 ;
    SlaveRespB5, 40 ;
    SlaveRespB6, 48 ;
    SlaveRespB7, 56 ;
  }
}

Node_attributes {
  RLS {
    LIN_protocol = "2.1" ;
    configured_NAD = 0x10 ;
    initial_NAD = 0x10 ;
    product_id = 0x1234, 0x5678, 0 ;
    response_error = RLS_REP_Error ;
    P2_min = 50 ms ;
    ST_min = 0 ms ;
    N_As_timeout = 1000 ms ;
    N_Cr_timeout = 1000 ms ;
    configurable_frames {
      RLS_01 ;
      RLS_02 ;
    }
  }
  
  FRONTWIPER {
    LIN_protocol = "2.1" ;
    configured_NAD = 0x11 ;
    initial_NAD = 0x11 ;
    product_id = 0x1234, 0x5679, 0 ;
    response_error = WSMLINComEr ;
    P2_min = 50 ms ;
    ST_min = 0 ms ;
    N_As_timeout = 1000 ms ;
    N_Cr_timeout = 1000 ms ;
    configurable_frames {
      FRONTWIPER_01 ;
    }
  }
  
  IRMM {
    LIN_protocol = "2.1" ;
    configured_NAD = 0x12 ;
    initial_NAD = 0x12 ;
    product_id = 0x1234, 0x567A, 0 ;
    response_error = ErrRespIRMM ;
    P2_min = 50 ms ;
    ST_min = 0 ms ;
    N_As_timeout = 1000 ms ;
    N_Cr_timeout = 1000 ms ;
    configurable_frames {
      IRMM_01 ;
    }
  }
}

Schedule_tables {
  Dynamic {
    ZCUL_01 delay 10 ms ;
    ZCUL_02 delay 10 ms ;
    ZCUL_03 delay 10 ms ;
    RLS_01 delay 10 ms ;
    RLS_02 delay 10 ms ;
    ZCUL_01 delay 10 ms ;
    FRONTWIPER_01 delay 10 ms ;
    IRMM_01 delay 10 ms ;
    RLS_01 delay 10 ms ;
    RLS_02 delay 10 ms ;
  }
}

Signal_encoding_types {
  DimSnvty_Encoding {
    logical_value, 0, "Normal" ;
    logical_value, 1, "Dark" ;
    logical_value, 2, "Light" ;
    logical_value, 3, "Inhibit" ;
  }
  
  Boolean_Encoding {
    logical_value, 0, "CLOSE" ;
    logical_value, 1, "OPEN" ;
  }
  
  Percentage_Encoding {
    physical_value, 0, 255, 1, 0, "percent" ;
  }
  
  Speed_Encoding {
    physical_value, 0, 254, 1, 0, "times/min" ;
    logical_value, 255, "Unknown" ;
  }
  
  Temperature_Encoding {
    physical_value, 0, 255, 1, -40, "degC" ;
  }
  
  Brightness_Encoding {
    physical_value, 0, 255, 1, 0, "lux" ;
  }
}

Signal_representation {
  DimSnvty_Encoding: IntrCmdIntrMirrDimSnvty ;
  Boolean_Encoding: ZCUL_Status_IGN, ZCUL_CMD_AutoWiper, ZCUL_RQ_FrontWash, ZCUL_ParkPosition, IntrCmdDrvrSide, IntrCmdIntrMirrEna, IntrCmdIntrMirrInhbDim ;
  Percentage_Encoding: IntrMirrDimRespIntrMirrDimPerc ;
  Speed_Encoding: WSMWipeSpd_l ;
  Temperature_Encoding: ZCUL_OutsideTemp, RLS_Temperature ;
  Brightness_Encoding: RLS_TopBrightnessRawValue, RLS_HUDBrightnessRawValue ;
}