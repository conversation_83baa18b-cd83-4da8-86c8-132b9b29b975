#include "drv.h"
#include "pid.h"
#include "config.h"
#include "config_asw.h"

uint16_t PID_Ctrl(PID_Data_t *pid_data, uint16_t input)
{

    int16_t out_temp;

    pid_data->err = input - pid_data->target;

    pid_data->integral += (int32_t)pid_data->Ki * (int32_t)pid_data->err;
    if (pid_data->integral >= (int32_t)pid_data->IMax)
    {
        pid_data->integral = (int32_t)pid_data->IMax;
    }
    else if (pid_data->integral <= 0)
    {
        pid_data->integral = 0;
    }
    else
    {
        /* do nothing */
    }
    // out_temp = ((int16_t)pid_data->Kp*(int16_t)pid_data->err + pid_data->integral +
    //            (int16_t)pid_data->Kd*(int16_t)(pid_data->err-pid_data->err_k_1))/100;

    out_temp = ((int32_t)pid_data->Kp * (int16_t)pid_data->err + (int16_t)pid_data->integral) / 100;

    // pid_data->err_k_1 = pid_data->err;

    if (out_temp <= (int16_t)pid_data->min)
    {
        out_temp = pid_data->min;
    }
    else if (out_temp >= (int16_t)pid_data->max)
    {
        out_temp = pid_data->max;
    }
    else
    {
        // do nothing
    }

    return out_temp;
}

#ifdef EC_VOLTAGE_LOOP
uint8_t Voltage_PID_Ctrl(PID_Data_t *pid_data, uint16_t input)
{
    static int16_t out_temp = 0;

    pid_data->err = (int16_t)pid_data->target - (int16_t)input;

    pid_data->integral += pid_data->Ki * pid_data->err;
    if (pid_data->integral >= pid_data->IMax)
    {
        pid_data->integral = pid_data->IMax;
    }
    else if (pid_data->integral <= 0)
    {
        pid_data->integral = 0;
    }
    else
    {
        /* do nothing */
    }
#if 1
    out_temp = ((int32_t)pid_data->Kp * (int16_t)pid_data->err + (int16_t)pid_data->integral) / 100;
#else
    out_temp = (pid_data->Kp * pid_data->err + pid_data->integral +
                pid_data->Kd * (pid_data->err - pid_data->err_k_1)) /
               100;
    pid_data->err_k_1 = pid_data->err;
#endif
    if (out_temp <= pid_data->min)
    {
        out_temp = pid_data->min;
    }
    else if (out_temp >= pid_data->max)
    {
        out_temp = pid_data->max;
    }
    else
    {
        // do nothing
    }

    return (uint8_t)out_temp;
}

#endif
