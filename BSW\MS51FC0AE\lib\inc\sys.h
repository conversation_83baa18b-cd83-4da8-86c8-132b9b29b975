#ifndef __SYS_H
#define __SYS_H	

#include "MS51_16K.h"
#include "Function_Define.h"
#include "SFR_Macro.h"
#include "Common.h"
#include "Delay.h"


typedef bit                   BIT;
typedef unsigned char         uint8_t;
typedef unsigned int          uint16_t;
typedef unsigned long         uint32_t;

typedef char         int8_t;
typedef int          int16_t;
typedef long         int32_t;

//#define INT8   int8_t           
//#define INT16  int16_t           
//#define INT32  int32_t          
	

//typedef unsigned char         u8;
//typedef unsigned int          u16;
//typedef unsigned long         u32;

typedef void(*VoidFuncVoid)(void);

//#undef NULL
//#define NULL 0                 



extern bit BIT_TMP;
#define  FSYS_HXT   0
#define  FSYS_LXT   1
#define  FSYS_HIRC  2
#define  FSYS_LIRC  3
#define  FSYS_OSCIN_P30  4
#define  FSYS_HXTIN_P00  5
#define  HIRC_24        6
#define  HIRC_16        7
#define  HIRC_166       8


extern unsigned char data  TA_REG_TMP, BYTE_TMP;

void FsysSelect(unsigned char u8FsysMode);
void ClockEnable(unsigned char u8FsysMode);
void ClockDisable(unsigned char u8FsysMode);
void ClockSwitch(unsigned char u8FsysMode);
void MODIFY_HIRC(unsigned char u8HIRCSEL);



#endif
