#include "asm31ax_hal.h"

#ifdef HAL_VC_MODULE_ENABLED

/** @brief  Config vc gpio pin/配置同相端待监测的电压来源
 * @param  VCx: Selects the vc peripheral
 * @param  PIN: Selects gpio pin
 * @retval None
 */
void VC_SetPin(VC_TypeDef *VCx, uint32_t PIN)
{
    /*Check the parameters*/
    assert_param(IS_VC_ALL_PERIPH(VCx));
    assert_param(VC_PIN_SEL(PIN));

    /*Set Pin Mask*/
    VCx->CR0 &= ~VC_CR0_PINSEL_Msk;
    /*Set Pin*/
    VCx->CR0 |= PIN << VC_CR0_PINSEL_Pos;
}

/** @brief  Config vc gpio  pin of NA/配置反相端待监测的电压来源
 * @param  VCx:Selects the vc peripheral
 * @param  PIN:Selects gpio pin
 * @retval None
 */
void VC_SetNin(VC_TypeDef *VCx, uint32_t NIN)
{
    /*Check the parameters*/
    assert_param(IS_VC_ALL_PERIPH(VCx));
    assert_param(VC_NIN_SEL(NIN));

    /*Set Nin Mask*/
    VCx->CR0 &= ~VC_CR0_NINSEL_Msk;
    /*Set Nin*/
    VCx->CR0 |= NIN << VC_CR0_NINSEL_Pos;
}

/** @brief  Config vc divsion / 配置VC电压分压控制
 * @param  VCx:Selects the vc peripheral
 * @param  V25DIV:config parameters
 * @retval None
 */
void VC_SetV25Div(VC_TypeDef *VCx, uint32_t V25DIV)
{
    /*Check the parameters*/
    assert_param(IS_VC_ALL_PERIPH(VCx));
    assert_param(VC_V25DIV_SEL(V25DIV));

    /*Set Nin Mask*/
    VCx->CR0 &= ~VC_V25DIV_MASK;
    /*Set Nin*/
    VCx->CR0 |= V25DIV;
}

/** @brief  enable vc25 divsion/V25分频控制使能/禁止
 * @param  VCx:Selects the vc peripheral
 * @param  NewState: new state of the V25DIV _EN(VC_CR0.bit6).
 * This parameter can be: ENABLE or DISABLE.
 *         ENABLE  : V25分频控制使能
 *         DISABLE : V25分频控制禁止
 * @retval None
 */
void VC_V25DivCmd(VC_TypeDef *VCx, FunctionalState NewState)
{
    /*Check the parameters */
    assert_param(IS_VC_ALL_PERIPH(VCx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*open v25div */
        VCx->CR0 |= V25DIV_ENABLE;
    }
    else
    {
        /*close v25div */
        VCx->CR0 &= ~V25DIV_ENABLE;
    }
}

/** @brief  enable vc function/VC比较器模块使能/禁止
 * @param  VCx:Selects the vc peripheral
 * @param  NewState: new state of the VCEN(VC_CR1.bit0).
 * This parameter can be: ENABLE or DISABLE.
 *         ENABLE  : 使能VC比较器模块
 *         DISABLE : 禁止VC比较器模块
 * @retval None
 */
void VC_Cmd(VC_TypeDef *VCx, FunctionalState NewState)
{
    /*Check the parameters */
    assert_param(IS_VC_ALL_PERIPH(VCx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*enable vc function */
        VCx->CR1 |= VC_ENABLE;
    }
    else
    {
        /*disable vc function */
        VCx->CR1 &= ~VC_ENABLE;
    }
}

/** @brief  Set vc flt clock/配置VC数字滤波时钟
 * @param  VCx:Selects the vc peripheral
 * @param  FLTCLK: the flt clock
 * @retval None
 */
void VC_SetFltClk(VC_TypeDef *VCx, uint32_t FLTCLK)
{
    /*Check the parameters */
    assert_param(IS_VC_ALL_PERIPH(VCx));
    assert_param(VC_FLTCLK_SEL(FLTCLK));

    /*Set flt clock */
    VCx->CR1 &= ~VC_CR1_VC_FLTCLK_SEL_Msk;
    VCx->CR1 |= FLTCLK << VC_CR1_VC_FLTCLK_SEL_Pos;
}

/** @brief  Enable flt function/使能VC数字滤波功能
 * @param  VCx:Selects the vc peripheral
 * @param  NewState: new state of the FLTEN(VC_CR1.bit8).
 * This parameter can be: ENABLE or DISABLE.
 *         ENABLE  : 使能数字滤波
 *         DISABLE : 禁止数字滤波
 * @retval None
 */
void VC_FLTCmd(VC_TypeDef *VCx, FunctionalState NewState)
{
    /*Check the parameters */
    assert_param(IS_VC_ALL_PERIPH(VCx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*enable flt function */
        VCx->CR1 |= VC_FLT_ENABLE;
    }
    else
    {
        /*disable flt function */
        VCx->CR1 &= ~VC_FLT_ENABLE;
    }
}

/** @brief  下降沿触发使能命令 (被监测电压从低于阈值电压变为高于阈值电压)
 * @param  VCx:Selects the vc peripheral
 * @param  NewState: new state of the FALLINTEN(VC_CR1.bit12).
 * This parameter can be: ENABLE or DISABLE.
 *         ENABLE  : 下降沿触发使能
 *         DISABLE : 下降沿触发禁止
 * @retval None
 */
void VC_FallITCmd(VC_TypeDef *VCx, FunctionalState NewState)
{
    /*Check the parameters */
    assert_param(IS_VC_ALL_PERIPH(VCx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*Enable fall interrupt function*/
        VCx->CR1 |= VC_FALLINT_ENABLE;
    }
    else
    {
        /*Disable fall interrupt function*/
        VCx->CR1 &= ~VC_FALLINT_ENABLE;
    }
}

/** @brief  上升沿触发使能命令 (被监测电压从高于阈值电压变为低于阈值电压)
 * @param  VCx:Selects the vc peripheral
 * @param  NewState: new state of the RISEINTEN(VC_CR1.bit13).
 *         ENABLE  : 上升沿触发使能
 *         DISABLE : 上升沿触发禁止
 * @retval None
 */
void VC_RiseITCmd(VC_TypeDef *VCx, FunctionalState NewState)
{
    /*Check the parameters */
    assert_param(IS_VC_ALL_PERIPH(VCx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*Enable the rise interrupt function*/
        VCx->CR1 |= VC_RISEINT_ENABLE;
    }
    else
    {
        /*Disable the rise interrupt function*/
        VCx->CR1 &= ~VC_RISEINT_ENABLE;
    }
}

/** @brief  高电平触发使能命令 (被监测电压低于阈值电压)
 * @param  VCx:Selects the vc peripheral
 * @param  NewState: new state of the HIGHINTEN(VC_CR1.bit14).
 *         ENABLE  : 高电平触发使能
 *         DISABLE : 高电平触发禁止
 * @retval None
 */
void VC_HighITCmd(VC_TypeDef *VCx, FunctionalState NewState)
{
    /*Check the parameters */
    assert_param(IS_VC_ALL_PERIPH(VCx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*Enable the high interrupt function*/
        VCx->CR1 |= VC_HIGHINT_ENABLE;
    }
    else
    {
        /*Disable the high interrupt function*/
        VCx->CR1 &= ~VC_HIGHINT_ENABLE;
    }
}

/** @brief  设置VC采样滤波次数
 * @param  VCx:Selects the vc peripheral
 * @param  FltNum: 要设置的采样滤滤次数
 * @retval None
 */
void VC_SetFltNum(VC_TypeDef *VCx, uint32_t FltNum)
{
    /*Check the parameters */
    assert_param(IS_VC_ALL_PERIPH(VCx));
    assert_param(IS_VC_FLTNUM(FltNum));

    /*Set the flt parameters */
    VCx->CR1 &= ~VC_FLTNUM_MASK;
    VCx->CR1 |= FltNum;
}

/** @brief  Enable out config function
 * @param  VCx:Selects the vc peripheral
 * @param  OutCfgStatus:the value of out config
 * @param  NewState: new state of the LVDx peripheral.
 * This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void VC_OutConfigCmd(VC_TypeDef *VCx, uint32_t OutCfgStatus, FunctionalState NewState)
{
    /*Check the parameters */
    assert_param(IS_VC_ALL_PERIPH(VCx));
    assert_param(IS_VC2_Status(OutCfgStatus));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*Enable the out  config */
        VCx->OUTCFG |= OutCfgStatus;
    }
    else
    {
        /*Disable the out  config */
        VCx->OUTCFG &= ~OutCfgStatus;
    }
}

/** @brief  Enable vc interrupt function
 * @param  VCx:Selects the vc peripheral
 * @param  NewState: new state of the INT_EN(VC_CR1.bit15).
 * This parameter can be: ENABLE or DISABLE.
 *         ENABLE  : VC中断使能
 *         DISABLE : VC中断禁止
 * @retval None
 */
void VC_ITConfig(VC_TypeDef *VCx, FunctionalState NewState)
{
    /*Check the parameters */
    assert_param(IS_VC_ALL_PERIPH(VCx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*Enable the  interrupt*/
        VCx->CR1 |= VC_TI_ENABLE;
    }
    else
    {
        /*Disable the  interrupt*/
        VCx->CR1 &= ~VC_TI_ENABLE;
    }
}

/**
 * @brief  Checks whether the interrupt flag is set or not.
 * @param  VCx:Selects the vc peripheral
 * @param  VC_FLAG: VC interrupt flag
 * @retval The new state of the Early Wakeup interrupt flag (SET or RESET)
 */
FlagStatus VC_GetFlagStatus(VC_TypeDef *VCx, uint16_t VC_FLAG)
{
    FlagStatus bitstatus = RESET;
    /*Check the parameters */
    assert_param(IS_VC_ALL_PERIPH(VCx));

    if (((VCx->SR) & VC_FLAG) != (uint32_t)RESET)
    {
        bitstatus = SET;
    }
    else
    {
        bitstatus = RESET;
    }

    return bitstatus;
}

/**
 * @brief  Clear the interrupt flag
 * @param  VCx:Selects the vc peripheral
 * @retval None
 */
void VC_ClearITFlag(VC_TypeDef *VCx)
{
    /*Check the parameters */
    assert_param(IS_VC_ALL_PERIPH(VCx));
    /*Clear the vc interrupt flag */
    VCx->SR = 0x00;
}

#endif
