/*Define to prevent recursive inclusion ----------------------*/
#ifndef __ASM31AX_SYSCTRL_H
#define __ASM31AX_SYSCTRL_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ----------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

    /** @addtogroup SYSCFG
     * @{
     */

#define SYSCTRL_UNLOCKKEY (uint32_t)0x55aa6699
#define SYSCTRL_LOCKKEY (uint32_t)0x55aa6698
#define SYSCTRL_LPUARTKEY (uint32_t)0x5a690000
#define SYSCTRL_LPUARTKEY_MASK (uint32_t)0x0000ffff

/* Exported types ------------------------------------------------------------*/
#define IS_SYSCTRL_ALL_PERIPH(PERIPH)    (PERIPH) == SYSCTRL)

/** @defgroup SYSCFG_Exported_Constants
 * @{
 */
#define SYSCTRL_LOCKUP_EN ((uint32_t)0x00000001);
#define SYSCTRL_DEEPSLEEPPORTINT_EN ((uint32_t)0xffffffff);
#define SYSCTRL_ACTIVESLEEPPORTINT_EN ((uint32_t)0xfffffffe);

/** @defgroup SYSCTRL MASK
 * @{
 */
#define SPINSS_SEL_MASK ((uint32_t)0x0000000f)
#define TIM10_GATE_MASK ((uint32_t)0x00000003 << 4)
#define TIM11_GATE_MASK ((uint32_t)0x00000003 << 6)
#define IPTIM_GATE_MASK ((uint32_t)0x00000003 << 8)

#define PCA_CH_MASK ((uint32_t)0x00000003)
#define TIM_ETR_MASK ((uint32_t)0x0000000f << 16)
#define TIM_CH_MASK ((uint32_t)0x00000007)

#define TIM1_BREAK_STM ((uint32_t)0x00100000)
#define TIM1_BREAK_ZERO ((uint32_t)0xFFEFFFFF)

#define LPUARTCLK_MASK ((uint32_t)0x00000003)
#define LPUARTCLK_EN ((uint32_t)0x00000010)

/** @defgroup SPINSS_SEL
 * @{
 */
#define SPINSS_HighLevel ((uint32_t)0x00000000)
#define SPINSS_Pin1GPIOA ((uint32_t)0x00000001)
#define SPINSS_Pin2GPIOA ((uint32_t)0x00000002)
#define SPINSS_Pin3GPIOA ((uint32_t)0x00000003)
#define SPINSS_Pin4GPIOB ((uint32_t)0x00000004)
#define SPINSS_Pin5GPIOB ((uint32_t)0x00000005)
#define SPINSS_Pin3GPIOC ((uint32_t)0x00000006)
#define SPINSS_Pin4GPIOC ((uint32_t)0x00000007)
#define SPINSS_Pin5GPIOC ((uint32_t)0x00000008)
#define SPINSS_Pin6GPIOC ((uint32_t)0x00000009)
#define SPINSS_Pin7GPIOC ((uint32_t)0x0000000A)
#define SPINSS_Pin1GPIOD ((uint32_t)0x0000000B)
#define SPINSS_Pin2GPIOD ((uint32_t)0x0000000C)
#define SPINSS_Pin3GPIOD ((uint32_t)0x0000000D)
#define SPINSS_Pin4GPIOD ((uint32_t)0x0000000E)
#define SPINSS_Pin6GPIOD ((uint32_t)0x0000000F)
#define IS_SPINSS_PORT_PIN(PIN) ((PIN >= 0x00000000) && (PIN <= 0x0000000f))

/**
 * @}TIM0_GATE_SEL
 */
#define TIM0_GATE_TIM0GATE ((uint32_t)0x00000000 << 4)
#define TIM0_UART0_RXD ((uint32_t)0x00000001 << 4)
#define TIM0_UART1_RXD ((uint32_t)0x00000002 << 4)
#define TIM0_LPUART_RXD ((uint32_t)0x00000003 << 4)
#define IS_TIM0_GATE_SEL(PIN) ((PIN & 0xffffffcf == 0x00000000) && (PIN >= 0x00000000))

/**
 * @}TIM1_GATE_SEL
 */
#define TIM1_GATE_TIM0GATE ((uint32_t)0x00000000 << 6)
#define TIM1_UART0_RXD ((uint32_t)0x00000001 << 6)
#define TIM1_UART1_RXD ((uint32_t)0x00000002 << 6)
#define TIM1_LPUART_RXD ((uint32_t)0x00000003 << 6)
#define IS_TIM1_GATE_SEL(PIN) ((PIN & 0xffffff3f == 0x00000000) && (PIN >= 0x00000000))

/**
 * @}IPTIM_GATE_SEL
 */
#define IPTIM_GATE_TIM1GATE ((uint32_t)0x00000000 << 8)
#define IPTIM_UART0_RXD ((uint32_t)0x00000001 << 8)
#define IPTIM_UART1_RXD ((uint32_t)0x00000002 << 8)
#define IPTIM_LPUART_RXD ((uint32_t)0x00000003 << 8)
#define IS_IPTIM_GATE_SEL(PIN) ((PIN & 0xfffffcff == 0x00000000) && (PIN >= 0x00000000))

/** @defgroup SYSCFG_EXTI_Pin_Sources
 * @{
 */
#define PCA_CH ((uint32_t)0x00000000)
#define PCA_UART0RXD ((uint32_t)0x00000001)
#define PCA_UART1RXD ((uint32_t)0x00000002)
#define PCA_LIRC ((uint32_t)0x00000003)
#define IS_CAP_CHANNEL_SEL(CHANNEL) ((CHANNEL & 0xfffffffc == 0x00000000) && (CHANNEL >= 0x00000000))

#define PCA_CHANNEL0 ((uint32_t)0x00000000)
#define PCA_CHANNEL1 ((uint32_t)0x00000001)
#define PCA_CHANNEL2 ((uint32_t)0x00000002)
#define PCA_CHANNEL3 ((uint32_t)0x00000003)
#define PCA_CHANNEL4 ((uint32_t)0x00000004)
#define IS_PCA_CHANNEL_SET(CHANNEL) ((CHANNEL >= 0x00000000) && (CHANNEL <= 0x00000004))

#define TIMETR_LowLevel ((uint32_t)0x00000000 << 16)
#define TIMETR_Pin1GPIOA ((uint32_t)0x00000001 << 16)
#define TIMETR_Pin2GPIOA ((uint32_t)0x00000002 << 16)
#define TIMETR_Pin3GPIOA ((uint32_t)0x00000003 << 16)
#define TIMETR_Pin4GPIOB ((uint32_t)0x00000004 << 16)
#define TIMETR_Pin5GPIOB ((uint32_t)0x00000005 << 16)
#define TIMETR_Pin3GPIOC ((uint32_t)0x00000006 << 16)
#define TIMETR_Pin4GPIOC ((uint32_t)0x00000007 << 16)
#define TIMETR_Pin5GPIOC ((uint32_t)0x00000008 << 16)
#define TIMETR_Pin6GPIOC ((uint32_t)0x00000009 << 16)
#define TIMETR_Pin7GPIOC ((uint32_t)0x0000000A << 16)
#define TIMETR_Pin1GPIOD ((uint32_t)0x0000000B << 16)
#define TIMETR_Pin2GPIOD ((uint32_t)0x0000000C << 16)
#define TIMETR_Pin3GPIOD ((uint32_t)0x0000000D << 16)
#define TIMETR_Pin4GPIOD ((uint32_t)0x0000000E << 16)
#define TIMETR_Pin6GPIOD ((uint32_t)0x0000000F << 16)
#define IS_TIMETR_ETR_SET(CHANNEL) ((CHANNEL >= 0x00000000) && ((CHANNEL && 0xfff0ffff) == (uint32_t)0x00000000))

/** @defgroup TIM Input Channel Signal source
 * @{
 */
#define TIM_CH (uint32_t)0x00000000        /*!< TIM输入通道信号来源于: TIM_CHx */
#define TIM_UART0RXD (uint32_t)0x00000001  /*!< TIM输入通道信号来源于:    UART0_RXD */
#define TIM_UART1RXD (uint32_t)0x00000002  /*!< TIM输入通道信号来源于: UART1_RXD */
#define TIM_LPUARTRXD (uint32_t)0x00000003 /*!< TIM输入通道信号来源于:  LPUART_RXD */
#define TIM_LIRC (uint32_t)0x00000004      /*!< TIM输入通道信号来源于: LIRC*/
#define IS_TIM_INPUTSIGNAL_SEL(CHANNEL) ((CHANNEL >= 0x00000000) && (CHANNEL <= 0x00000004))

/** @defgroup TIM Channel
 * @{
 */
#define TIM_CHANNEL1 (uint32_t)0x00000000 /*!< TIM通道1 */
#define TIM_CHANNEL2 (uint32_t)0x00000001 /*!< TIM通道2 */
#define TIM_CHANNEL3 (uint32_t)0x00000002 /*!< TIM通道3 */
#define TIM_CHANNEL4 (uint32_t)0x00000003 /*!< TIM通道4 */
#define IS_TIM_CHANNEL_SET(CHANNEL) ((CHANNEL >= 0x00000000) && (CHANNEL <= 0x00000003))

#define LPUARTCLK_PCLK ((uint32_t)0x00000000)
#define LPUARTCLK_LXT ((uint32_t)0x00000001)
#define LPUARTCLK_LIRC ((uint32_t)0x00000002)
#define LPUARTCLK_BREAKUPPCLK ((uint32_t)0x00000003)
#define IS_LPUART_CLK_SEL(CLK) ((CLK >= 0x00000000) && (CLK <= 0x00000003))

    /** @defgroup LPUART_Exported_Functions
     * @{
     */
    void SYSCTRL_DeInit(void);
    void SYSCTRL_CortexM0LockUpCmd(SYSCFG_TypeDef *SYSCTRLx, FunctionalState NewState);
    void SYSCTRL_DeepsleepPortITCmd(SYSCFG_TypeDef *SYSCTRLx, FunctionalState NewState);
    void SYSCTRL_ActiveSleepPortITCmd(SYSCFG_TypeDef *SYSCTRLx, FunctionalState NewState);
    void SYSCTRL_SPICSConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_SPINSS_PIN);
    void SYSCTRL_TIM10_GateConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_TIM0_GATE);
    void SYSCTRL_TIM11_GateConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_TIM1_GATE);
    void SYSCTRL_IPTIM_GateConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_IPTIM_GATE);
    void SYSCTRL_PCACapMuxConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_CAP_CHANNEL, uint32_t SYSCTRL_CAP_SINGNAL);
    void SYSCTRL_TIM1_InputSignalConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_INPUT_CHANNEL, uint32_t SYSCTRL_INPUT_SINGNAL);
    void SYSCTRL_TIM1_ETRSignalConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_INPUT_SINGNAL);
    void SYSCTRL_TIM1_SetBreakSTM(SYSCFG_TypeDef *SYSCTRLx);
    void SYSCTRL_TIM1_SetBreakZERO(SYSCFG_TypeDef *SYSCTRLx);
    void SYSCTRL_TIM2_InputSignalConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_INPUT_CHANNEL, uint32_t SYSCTRL_INPUT_SINGNAL);
    void SYSCTRL_TIM2_ETRSignalConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_INPUT_SINGNAL);
    void SYSCTRL_LPUART_CLKConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_LPUART_CLK);
    void SYSCTRL_LPUART_CLKCmd(SYSCFG_TypeDef *SYSCTRLx, FunctionalState NewState);

#ifdef __cplusplus
}
#endif

#endif /*__ASM31X_SYSCTRL_H */
