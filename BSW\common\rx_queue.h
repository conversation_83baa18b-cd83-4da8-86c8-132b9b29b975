#ifndef _RX_QUEUE_H_
#define _RX_QUEUE_H_
#include "UART.h"

#include "config.h"

#define QUEUE_SIZE    20 //50

#if LIN_ERROR_RESPONSE
extern unsigned char tx_buf[QUEUE_SIZE];
unsigned char get_tx_queue_used_len(void);
void set_tx_queue_used_len(unsigned char len);
unsigned char get_tx_queue_pos(void);
void set_tx_queue_pos(unsigned char pos);
void clear_tx_queue(void);
void set_tx_rev_len(unsigned char len);
#endif

extern unsigned char rx_buf[QUEUE_SIZE];
void rx_queue_in(unsigned char buf);
//unsigned char rx_queue_out(unsigned char lenth,unsigned char *buf);
unsigned char get_rx_queue_used_len(void);
unsigned char get_rx_queue_left_len(void);
void clear_rx_queue(void);
void print_rx_buffer(void);
#endif