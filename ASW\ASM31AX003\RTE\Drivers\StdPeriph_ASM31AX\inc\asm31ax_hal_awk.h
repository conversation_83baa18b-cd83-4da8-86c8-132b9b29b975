/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_AWK_H
#define __ASM31AX_AWK_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

    /**
     * @} TMER RIGSTER
     */
    typedef struct
    {
        AWK_TypeDef *Instance;
        uint32_t XTLPRSC; /*!<AWK external clock frequency division*/

        uint32_t ClkSel; /*!< AWK clock source Selection*/

        uint32_t ClkDiv; /*!< AWK count clock frequency division Selection*/

        uint32_t Value;
    } AWK_InitTypeDef;

/** @defgroup TIM_Exported_constants
 * @{
 */
#define IS_AWK_ALL_PERIPH(PERIPH) ((PERIPH) == AWK)

/** @defgroup AWK_CLK_SELECTION
 * @{
 */
#define AWK_CLKMASK (uint32_t)0x00000060
#define AWK_CLKLIRC (uint32_t)0x00000020 /*!<  LIRC 时钟 */
#define AWK_CLKHXT (uint32_t)0x00000040  /*!<  HXT 分频后的时钟 */
#define AWK_CLKLXT (uint32_t)0x00000060  /*!<  LXT 分频后的时钟 */
#define IS_AWK_SELCLK(CLK) (((CLK) == AWK_CLKMASK) || \
                            ((CLK) == AWK_CLKLIRC) || \
                            ((CLK) == AWK_CLKHXT) ||  \
                            ((CLK) == AWK_CLKLXT))

/** @defgroup AWK_Enable
 * @{
 */
#define AWK_ENABLE 0x10
#define AWK_DISABLE 0x00
#define AWK_TI_CLEAR 0x01

/** @defgroup AWK_Enable
 * @{
 */
#define AWK_CR_MASK (uint32_t)0x00000010

/** @defgroup AWK 计数器用时钟源频率选择位
 * @{
 */
#define AWK_PRESCALER_DIV2 ((uint32_t)0x00000000)
#define AWK_PRESCALER_DIV4 ((uint32_t)0x00000001)
#define AWK_PRESCALER_DIV8 ((uint32_t)0x00000002)
#define AWK_PRESCALER_DIV16 ((uint32_t)0x00000003)
#define AWK_PRESCALER_DIV32 ((uint32_t)0x00000004)
#define AWK_PRESCALER_DIV64 ((uint32_t)0x00000005)
#define AWK_PRESCALER_DIV128 ((uint32_t)0x00000006)
#define AWK_PRESCALER_DIV256 ((uint32_t)0x00000007)
#define AWK_PRESCALER_DIV512 ((uint32_t)0x00000008)
#define AWK_PRESCALER_DIV1024 ((uint32_t)0x00000009)
#define AWK_PRESCALER_DIV2048 ((uint32_t)0x0000000A)
#define AWK_PRESCALER_DIV4096 ((uint32_t)0x0000000B)
#define AWK_PRESCALER_DIV8192 ((uint32_t)0x0000000C)
#define AWK_PRESCALER_DIV16384 ((uint32_t)0x0000000D)
#define AWK_PRESCALER_DIV32768 ((uint32_t)0x0000000E)
#define AWK_PRESCALER_DIV65536 ((uint32_t)0x0000000F)

    /** @defgroup AWK_Exported_Functions
     * @{
     */
    void HAL_AWK_Init(AWK_InitTypeDef *AWK_InitStruct);
    void HAL_AWK_Cmd(AWK_TypeDef *AWKx, FunctionalState NewState);
    void HAL_AWK_SetRldval(AWK_TypeDef *AWKx, uint8_t value);
    void HAL_AWK_ClearITFlag(AWK_TypeDef *AWKx);
    void HAL_AWK_SelClk(AWK_TypeDef *AWKx, uint8_t AWKClk);

#ifdef __cplusplus
}
#endif

#endif /*__ASM31X_AWK_H */
