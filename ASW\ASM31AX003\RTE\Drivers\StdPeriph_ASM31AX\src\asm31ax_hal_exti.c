#include "asm31ax_hal.h"

#ifdef HAL_EXTI_MODULE_ENABLED

/* Includes ------------------------------------------------------------------*/

#include "misc.h"

/**
 * @brief  Selects the GPIO pin used as EXTI Line.
 * @param  GPIOx: selects the GPIO port to be used as source for EXTI lines.
 * This parameter can be  GPIOx where x can be (A..D).
 * @param  GPIO_IRQ_InitStructure: pointer to a GPI0_IRQ_InitTypeDef structure which will
 *         be initialized.
 * @param  GPIO_Pin: specifies the port bit to read.
 * This parameter can be GPIO_Pin_x where x can be (0..7).
 * @retval None
 */
void GPIO_EXTILineConfig(GPIO_TypeDef *GPIOx, GPIO_IRQ_InitTypeDef *GPIO_IRQ_InitStructure, uint8_t GPIO_Pin)
{
    /* Check the parameters */
    assert_param(IS_GPIO_ALL_PERIPH(GPIOx));
    assert_param(IS_GET_GPIO_PIN(GPIO_Pin));
    assert_param(IS_FUNCTIONAL_STATE(GPIO_InitStructure->GPIO_IRQPin_Enable));

    /*!<中断类型：0：边沿触发中断类型
                  1: 电平触发中断类型*/
    if (GPIO_IRQ_InitStructure->GPIO_IRQ_Pin_Type)
    {
        GPIOx->ITYP |= GPIO_Pin; // 电平触发中断
    }
    else
    {
        GPIOx->ITYP &= ~GPIO_Pin; // 边沿触发中断
    }

    /*!<中断类型：0：低电平或下降沿触发中断
                  1: 高电平或上升沿触发中断*/
    if (GPIO_IRQ_InitStructure->GPIO_IRQ_Pin_Polarity)
    {
        GPIOx->IVAL |= GPIO_Pin; // 高电平或上升沿触发中断
    }
    else
    {
        GPIOx->IVAL &= ~GPIO_Pin; // 低电平或下降沿触发中断
    }

    /*!<端口任意边沿触发中断：0：中断触发沿由PxIVALn决定
                              1：上升沿/下降沿都触发中断*/
    if (GPIO_IRQ_InitStructure->GPIO_IRQ_Pin_Edge)
    {
        GPIOx->IANY |= GPIO_Pin; // 上升沿、下降沿都触发中断
    }
    else
    {
        GPIOx->IANY &= ~GPIO_Pin; // 中断触发沿由PxIVALn决定
    }

    /*!<中断标志位清除：0：保留对应的中断标志位
                        1: 清除对应的中断标志位*/
    if (GPIO_IRQ_InitStructure->GPIO_IRQ_Pin_Clear)
    {
        GPIOx->ICLR |= GPIO_Pin; // 清除对应的中断标志位
    }
    else
    {
        GPIOx->ICLR &= ~GPIO_Pin; // 保留中断标志位
    }

    /*!<中断使能设置：0：中断禁止/屏蔽   1: 中断使能*/
    if (GPIO_IRQ_InitStructure->GPIO_IRQ_Pin_Enable)
    {
        GPIOx->IEN |= GPIO_Pin;
    }
    else
    {
        GPIOx->IEN &= ~GPIO_Pin;
    }
}

#endif
