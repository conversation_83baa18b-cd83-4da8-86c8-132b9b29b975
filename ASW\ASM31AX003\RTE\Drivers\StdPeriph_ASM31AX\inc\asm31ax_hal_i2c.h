/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_I2C_H
#define __ASM31AX_I2C_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

    /** @addtogroup struct I2C_EveType
     * @{
     */
    typedef struct
    {
        uint16_t I2C_Master_Send;   /*!< Master send   */
        uint16_t I2C_Master_Recive; /*!<Master Recive  */
        uint16_t I2C_Slave_Send;    /*!<Slave Send     */
        uint16_t I2C_Slave_Recive;  /*!<Slave Recive   */
        uint16_t I2C_Rest_Eve;      /*!<Rest Eve       */
    } I2C_EveTypeDef;

    typedef struct
    {
        uint8_t Op;
        uint8_t *SendBuf;
        uint8_t *ReciveBuf;
        uint16_t SendCnt;
        uint16_t SendSize;
        uint16_t ReciveCnt;
        uint16_t ReciveSize;

    } I2C_OperateTypeDef;

    typedef void (*I2C_Operate_Call)(I2C_OperateTypeDef *);

    /** @addtogroup struct I2C_InitType
     * @{
     */
    typedef struct
    {
        I2C_TypeDef *Instance;
        uint32_t ClockSpeed; /*!< Specifies the clock frequency.
                                    This parameter must be set to a value lower than 400kHz */
        uint16_t Mode;       /*!< Specifies the I2C mode.
                                    This parameter can be a value of  I2C_mode */
        uint16_t OwnAddress; /*!< Specifies the first device own address.
                                    This parameter can be a 7-bit or 10-bit address. */
        uint16_t Ack;        /*!< Enables or disables the acknowledgement.
                                    This parameter can be a value of I2C_acknowledgement */
        uint16_t GCRspEn;    /*!< Enables or disables the BroadcastAddress Response .
                                                      This parameter can be a value of the BroadcastAddress Response */

         I2C_OperateTypeDef Operate;
        I2C_Operate_Call SendCall;
        I2C_Operate_Call ReciveCall;

    } I2C_InitTypeDef;

/* Exported types ------------------------*/
#define IS_I2C_ALL_PERIPH(PERIPH) (((PERIPH) == I2C)

#define _I2C_OP_IDLE (0x00)
#define _I2C_OP_MASTER_SEND (0x01)
#define _I2C_OP_MASTER_RECIVE (0x02)
#define _I2C_OP_SLAVE_SEND (0x03)
#define _I2C_OP_SLAVE_RECIVE (0x04)

#define _I2C_GC_ENABLE (0x01)
#define _I2C_GC_DISABLE (0x00)

/* I2C AA mask */
#define CR_AA_Set ((uint32_t)0x00000004)
#define CR_AA_Reset ((uint32_t)0xFFFFFFFB)

/* I2C SI mask */
#define CR_SI_Set ((uint32_t)0x00000008)
#define CR_SI_Reset ((uint32_t)0xFFFFFFF7)

/* I2C STO mask */
#define CR_STO_Set ((uint32_t)0x00000010)
#define CR_STO_Reset ((uint32_t)0xFFFFFFEF)

/* I2C STA mask */
#define CR_STA_Set ((uint32_t)0x00000020)
#define CR_STA_Reset ((uint32_t)0xFFFFFFDF)

/* I2C ENS mask */
#define CR_ENS_Set ((uint32_t)0x00000040)
#define CR_ENS_Reset ((uint32_t)0xFFFFFFBF)

/* I2C ADDR mask */
#define I2C_ADDR_ENGC_Set ((uint32_t)0x00000001)
#define I2C_ADDR_ENGC_Reset ((uint32_t)0xFFFFFFFE)

/* I2C ADDR mask */
#define I2C_ADDR_Set ((uint32_t)0x00000001)
#define I2C_ADDR_Reset ((uint32_t)0x000000FE)

/* I2C TMRUN */
#define I2C_TMRUN_TME_Set ((uint32_t)0x00000001)   /*<! 波特率计数器使能寄存器使能 */
#define I2C_TMRUN_TME_Reset ((uint32_t)0xFFFFFFFE) /*<! 波特率计数器使能寄存器禁止 */

/* I2C FLAG mask */
#define FLAG_Mask ((uint32_t)0x000000FF)

/** @defgroup I2C_mode
 * @{
 */
#define _I2C_MODE_MASTER ((uint32_t)0x40)
#define _I2C_MODE_SLAVE ((uint32_t)0x44)
#define _IS_I2C_MODE(MODE) (((MODE) == _I2C_MODE_MASTER) || \
                            ((MODE) == _I2C_MODE_SLAVE))

/** @defgroup I2C_ACK Enable/Disable
 * @{
 */
#define _I2C_ACK_ENABLE ((uint32_t)0x0004)
#define _I2C_ACK_DISABLE ((uint32_t)0x0000)
#define _IS_I2C_ACK_STATE(STATE) (((STATE) == _I2C_ACK_ENABLE) || \
                                  ((STATE) == _I2C_ACK_DISABLE))

/** @defgroup I2C_Module Enable/Disable
 * @{
 */
#define _I2C_Module_Enable ((uint32_t)0x00000040)
#define _I2C_Module_Disable ((uint32_t)0x000000BF)
#define _IS_I2C_Module_STATE(STATE) (((STATE) == _I2C_Module_Enable) || \
                                     ((STATE) == _I2C_Module_Disable))

/** @defgroup I2C_transfer_direction
 * @{
 */
#define _I2C_Direction_Transmitter ((uint32_t)0x00000000)
#define _I2C_Direction_Receiver ((uint32_t)0x00000001)
#define _IS_I2C_DIRECTION(DIRECTION) (((DIRECTION) == _I2C_Direction_Transmitter) || \
                                      ((DIRECTION) == _I2C_Direction_Receiver))

/** @defgroup I2C_acknowledged_address
 * @{
 */
#define I2C_ENBROADCAST_Bit ((uint32_t)0x00000001)

/** @defgroup I2C_registers
 * @{
 */
#define I2C_Register_CR ((uint32_t)0x00000000)
#define I2C_Register_DATA ((uint32_t)0x00000004)
#define I2C_Register_ADDR ((uint32_t)0x00000008)
#define I2C_Register_STAT ((uint32_t)0x0000000C)
#define I2C_Register_TMRUN ((uint32_t)0x00000010)
#define I2C_Register_TM ((uint32_t)0x00000014)
#define IS_I2C_REGISTER(REGISTER) (((REGISTER) == I2C_Register_CR) ||    \
                                   ((REGISTER) == I2C_Register_DATA) ||  \
                                   ((REGISTER) == I2C_Register_ADDR) ||  \
                                   ((REGISTER) == I2C_Register_STAT) ||  \
                                   ((REGISTER) == I2C_Register_TMRUN) || \
                                   ((REGISTER) == I2C_Register_TM))

/** @defgroup I2C_interrupts_definition
 * @{
 */
#define I2C_IT_ENABLE ((uint32_t)0x00000008)

/** @defgroup I2C_own_address
 * @{
 */
#define IS_I2C_OWN_ADDRESS(ADDRESS) ((ADDRESS) <= 0x7F)

/** @defgroup I2C_clock_speed
 * @{
 */
#define IS_I2C_CLOCK_SPEED(SPEED) (((SPEED) >= 0x1) && ((SPEED) <= 400000))

    /** @defgroup I2C_Exported_Functions
     * @{
     */
    void HAL_I2C_DeInit(I2C_TypeDef *I2Cx);
    void HAL_I2C_Init(I2C_InitTypeDef *I2C_InitStruct);

    void HAL_I2C_MasterSendData(I2C_InitTypeDef *I2C_InitStruct, uint8_t *sendbuf, uint16_t sendsize);
    void HAL_I2C_MasterReciveData(I2C_InitTypeDef *I2C_InitStruct, uint8_t *sendbuf, uint16_t sendsize, uint8_t *recivebuf, uint16_t recivesize);

    void HAL_I2C_StructInit(I2C_InitTypeDef *I2C_InitStruct);
    void HAL_I2C_Cmd(I2C_TypeDef *I2Cx, FunctionalState NewState);
    void HAL_I2C_GenerateSTART(I2C_TypeDef *I2Cx, FunctionalState NewState);
    void HAL_I2C_MasterSendCmd(I2C_TypeDef *I2Cx, FunctionalState NewState);
    void HAL_I2C_GenerateSTOP(I2C_TypeDef *I2Cx, FunctionalState NewState);
    void HAL_I2C_AcknowledgeConfig(I2C_TypeDef *I2Cx, FunctionalState NewState);
    void HAL_I2C_OwnAddressConfig(I2C_TypeDef *I2Cx, uint8_t Address);
    void HAL_I2C_GeneralCallCmd(I2C_TypeDef *I2Cx, FunctionalState NewState);
    void HAL_I2C_SendData(I2C_TypeDef *I2Cx, uint8_t Data);
    uint8_t HAL_I2C_ReceiveData(I2C_TypeDef *I2Cx);
    void HAL_I2C_Send7bitAddress(I2C_TypeDef *I2Cx, uint8_t Address, uint8_t I2C_Broadcast_Respone);
    void HAL_HAL_I2C_ITConfig(I2C_TypeDef *I2Cx, FunctionalState NewState);
    uint32_t I2C_ReadRegister(I2C_TypeDef *I2Cx, uint32_t I2C_Register);

    void HAL_I2C_IRQHandler(I2C_InitTypeDef *I2C_InitStruct);

#ifdef __cplusplus
}
#endif

#endif /*__ASM31X_I2C_H */
