#include "rx_queue.h"
#include "config.h"

unsigned char rx_buf[QUEUE_SIZE] = {0};   //数据缓存
volatile unsigned char used_size = 0;              //已经使用的数据长度

#if LIN_ERROR_RESPONSE
unsigned char tx_buf[QUEUE_SIZE] = {0};   //数据缓存
volatile unsigned char tx_used_size = 0;           //已经使用的数据长度
volatile unsigned char tx_used_pos = 0;

unsigned char tx_rev[QUEUE_SIZE] = {0};   //接收缓存 Pid+Data[]
volatile unsigned char tx_rev_len = 0;    //receive data+checksum length from tx
volatile unsigned char tx_rev_len_cur = 0;

void tx_queue_in(unsigned char *buf)
{
    unsigned char i;
    if(tx_used_size > QUEUE_SIZE)
    {
        return;
    }
    for (i=0; i<tx_used_size; i++){
        tx_buf[i] = *(buf++);
    }
}

unsigned char get_tx_queue_used_len(void)
{
    return tx_used_size;
}

void set_tx_queue_used_len(unsigned char len)
{
    tx_used_size = len;
}

unsigned char get_tx_queue_pos(void)
{
    return tx_used_pos;
}

void set_tx_queue_pos(unsigned char pos)
{
    tx_used_pos = pos;
}

void clear_tx_queue(void)
{
    memset(tx_buf,0,QUEUE_SIZE);
    tx_used_size = 0;
    tx_used_pos = 0;
}

void set_tx_rev_len(unsigned char len)
{
    tx_rev_len = len;
    tx_rev_len_cur = len;
}
#endif

//------------------------------------
void rx_queue_in(unsigned char buf)
{
    if(used_size == ( QUEUE_SIZE - 1 ) )
    {
        return;
    }
    rx_buf[used_size] = buf;
    used_size++;
}

void print_rx_buffer(void)
{
    lin_send(rx_buf,used_size);
}
/*
unsigned char rx_queue_out(unsigned char lenth,unsigned char *buf)
{
    unsigned char cnt = 0;
    unsigned char local_len = 0;
    if(NULL == buf)
    {
        return 0;
    }
    printf("%d",lenth);
    if(lenth == 0 || lenth >= used_size)       //为0时表示FIFO中数据全取出来
    {
        local_len = used_size;
    }
    else
    {
        local_len = lenth;
    }
    for(cnt = 0; cnt < local_len; cnt++)
    {
        buf[cnt] = rx_buf[cnt];
    }

    return local_len;
}
*/
unsigned char get_rx_queue_used_len(void)
{
    return used_size;
}

unsigned char get_rx_queue_left_len(void)
{
    return (QUEUE_SIZE - used_size);
}

void clear_rx_queue(void)
{
    used_size = 0;
}