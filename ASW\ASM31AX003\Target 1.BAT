SET PATH=E:\Keil_v5\ARM\ARMCC\Bin;E:\NXP\S32DS_ARM_v2.2\S32DS\build_tools\gcc_v4.9\gcc-arm-none-eabi-4_9\bin;E:\NXP\S32DS_ARM_v2.2\S32DS\build_tools\msys32\mingw32\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\ProgramData\Oracle\Java\javapath_;C:\Program Files (x86)\STMicroelectronics\st_toolset\asm;C:\Perl\bin;C:\Perl\site\bin;C:\Program Files (x86)\Intel\iCLS Client\;C:\Program Files\Intel\iCLS Client\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Program Files (x86)\Intel\Intel(R) Management Engine Components\DAL;C:\Program Files\Intel\Intel(R) Management Engine Components\DAL;C:\Program Files (x86)\Intel\Intel(R) Management Engine Components\IPT;C:\Program Files\Intel\Intel(R) Management Engine Components\IPT;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\ARM\Utilities\FLEXlm\********\1\win_32-pentium;C:\ARM\RVCT\Programs\3.1\569\win_32-pentium;C:\ARM\RDI\Deprecated\1.3.1\1\windows;C:\ARM\bin\win_32-pentium;C:\WINDOWS\System32\OpenSSH\;C:\Qt\Qt5.11.2\5.11.2\msvc2017_64\bin;C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Tools\MSVC\14.15.26726\bin\Hostx64\x64;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\nodejs\;C:\MinGW\msys\1.0\bin;C:\Program Files\Java\jre1.8.0_45\bin;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Programs\Python\Python39\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python39;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files (x86)\Freescale\CWS12v5.1\bin;C:\Program Files\dotnet\;E:\python38\Scripts\;E:\python38\;C:\Program Files (x86)\Microsoft Visual Studio\Common\Tools\WinNT;C:\Program Files (x86)\Microsoft Visual Studio\Common\MSDev98\Bin;C:\Program Files (x86)\Microsoft Visual Studio\Common\Tools;C:\Program Files (x86)\Microsoft Visual Studio\VC98\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files (x86)\SSH Communications Security\SSH Secure Shell;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\MinGW\bin;c:\JL\commtools;c:\JL\dv10\bin;c:\JL\dv12\bin;c:\JL\mc\bin;c:\JL\pi32\bin;C:\Users\<USER>\.dotnet\tools;F:\LenovoSoftstore\Install\QQliuxidating\QQGameTempest\Hall.57808\
SET CPU_TYPE=ASM31AX003
SET CPU_VENDOR=Sinemicro
SET UV2_TARGET=Target 1
SET CPU_CLOCK=0x00B71B00
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\system_click.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\anti_piracy.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\db.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\filter.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\main.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\pid_ctrl.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\tab.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\signal.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\board.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\sw_version.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\tuning.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\parm.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_adc.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_advtim.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_awk.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_beeper.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_clktrim.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_crc.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_debug.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_delay.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_exti.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_flash.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_gpio.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_i2c.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_iwdg.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_lptimer.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_lpuart.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_lvd.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_owire.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_pca.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_rcc.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_rtc.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_spi.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_syscon.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_timer.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_uart.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_vc.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_hal_wwdg.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\asm31ax_it.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmAsm" --Via ".\objects\startup_asm31ax._ia"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmCC" --Via ".\objects\system_asm31ax.__i"
"E:\Keil_v5\ARM\ARMCC\Bin\ArmLink" --Via ".\Objects\ASM31AX003.lnp"
"E:\Keil_v5\ARM\ARMCC\Bin\fromelf.exe" ".\Objects\ASM31AX003.axf" --i32combined --output ".\Objects\ASM31AX003.hex"
objcopy -Iihex -Obinary .\Objects\ASM31AX003.hex .\Objects\ASM31AX003.bin
release.exe
