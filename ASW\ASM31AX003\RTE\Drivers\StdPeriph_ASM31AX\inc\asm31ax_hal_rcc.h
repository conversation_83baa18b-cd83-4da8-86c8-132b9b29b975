/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_RCC_H
#define __ASM31AX_RCC_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

    /** @defgroup RCC_ClocksTypeDef
     * @{
     */
    typedef struct
    {
        uint32_t SYSCLK_Frequency; /*!< returns SYSCLK clock frequency expressed in Hz */
        uint32_t HCLK_Frequency;   /*!< returns HCLK clock frequency expressed in Hz */
        uint32_t PCLK_Frequency;   /*!< returns PCLK1 clock frequency expressed in Hz */
        uint32_t ADCCLK_Frequency; /*!< returns ADCCLK clock frequency expressed in Hz */
    } RCC_ClocksTypeDef;

    /** @defgroup RCC_ClocksTypeDef
     * @{
     */
    typedef struct
    {
        uint32_t LXPORT; /*!< X32K_IN/X32K_OUT function selection
                            1: X32K 端子模式(模拟功能)
                            0: GPIO 功能 (数字功能) */
        uint32_t LXAON;  /*!< LXT 只能使能不能禁止控制
                            1： LXT_EN 只能使能不能禁止控制。
                            0： LXT_EN 允许禁止控制 */

        uint32_t LXTBYP;     /*!< 由软件设置和清除，该位仅在外部 32KHz 振荡器关闭的情况下写值
                                1: LSE 振荡器被旁路
                                0: LSE 振荡器未被旁路 */
        uint32_t LXTEN;      /*!< 外部 32K 晶振 LXT 使能信号
                                0： 关闭
                                1： 使能 */
        uint32_t LXTSTARTUP; /*!< 外部 32.768K 晶振稳定时间选择
                                00:1024 个周期
                                01: 2048 个周期
                                10: 4096 个周期
                                11: 16384 个周期 */
        uint32_t LXTDIV;     /*!< 外部 32K 晶振驱动选择
                                1111: 最大驱动
                                0000：最小驱动 */
    } LXT_InitTypeDef;

/* Exported types ------------------------------------------------------------*/
#define IS_RCC_ALL_PERIPH(PERIPH) ((PERIPH) == RCC)

/** @defgroup RCC_Exported_Constants
 * @{
 */
#define RCC_CLK_KEYMASK ((uint32_t)0xffff0000)
#define RCC_CLKCONKEY ((uint32_t)0x5A690000)
#define RCC_LXTCLK_KEY ((uint32_t)0x5A690000)
#define RCC_HIRCCLK_KEY ((uint32_t)0x5A690000)
#define RCC_HXTCLK_KEY ((uint32_t)0x5A690000)
#define RCC_LIRCCLK_KEY ((uint32_t)0x5A690000)
#define RCC_SYSCLKSEL_KEY ((uint32_t)0x5A690000)
#define RCC_REGLOCKKEY ((uint32_t)0x55aa6699)
#define RCC_RESGLOCKKEY ((uint32_t)0x55aa6698)
#define RCC_APBPeriph_RTCRSTKEY ((uint32_t)0x5A690000)
#define RCC_RSTKEY ((uint32_t)0x55AA6698)

/** @defgroup HCLKEN_configuration
 * @{
 */
#define RCC_AHB_CLKMASK ((uint32_t)0x0000001F)
#define RCC_APB_CLKMASK ((uint32_t)0x001FFFFF)

/** @defgroup System_clock_source/定义系统时钟源
 * @{
 */
#define RCC_SYSCLKSource_HIRC ((uint32_t)0x00000001) /*!< 系统时钟源：内部高速时钟HIRC */
#define RCC_SYSCLKSource_HXT ((uint32_t)0x00000002)  /*!< 系统时钟源：外部高速晶体时钟HXT */
#define RCC_SYSCLKSource_LIRC ((uint32_t)0x00000004) /*!< 系统时钟源：内部低速时钟LIRC */
// #define RCC_SYSCLKSource_LXRC       ((uint32_t)0x00000100)  /*!< 系统时钟源：外部低速晶体时钟LXT */
#define RCC_SYSCLKSource_LXRC ((uint32_t)0x00000008) /*!< 系统时钟源：外部低速晶体时钟LXT */

#define IS_RCC_SYSCLK_SOURCE(SOURCE) (((SOURCE) == RCC_SYSCLKSource_HIRC) || \
                                      ((SOURCE) == RCC_SYSCLKSource_HXT) ||  \
                                      ((SOURCE) == RCC_SYSCLKSource_LIRC) || \
                                      ((SOURCE) == RCC_SYSCLKSource_LXRC))

/** @defgroup System_clock_source
 * @{
 */
#define RCC_SYSCLKSource_HIRC_EN ((uint32_t)0x00000001) /*!< 系统时钟源HIRC时钟使能 */
#define RCC_SYSCLKSource_HXT_EN ((uint32_t)0x00000002)  /*!< 系统时钟源HXT时钟使能 */
#define RCC_SYSCLKSource_LIRC_EN ((uint32_t)0x00000004) /*!< 系统时钟源LIRC时钟使能 */
#define RCC_SYSCLKSource_LXRC_EN ((uint32_t)0x00000100) /*!< 系统时钟源LXT时钟使能 */
#define RCC_SYSCLKSource_EN_MASK ((uint32_t)0x00000007)
#define RCC_LXTCLKSource_EN_MASK ((uint32_t)0x00000100)

/** @defgroup AHB_clock_source
 * @{
 */
#define IS_RCC_SYSCLK_DIV(SYSHCLK) ((SYSHCLK >= 0x00) || (SYSHCLK <= 0xff))

#define IS_RCC_HCLK_DIV(SYSPCLK) ((SYSPCLK >= 0x00) || (SYSPCLK <= 0xff))

#define IS_RCC_FCLK_DIV(SYSPCLK) ((SYSPCLK >= 0x00) || (SYSPCLK <= 0xff))

#define RCC_FCLK_DIV_MASK 0x000000ff

/** @defgroup 时钟输出源定义
 * @{
 */
#define RCC_SYSCLKOSEL_HIRC ((uint32_t)(0x00 << 8)) /*!< 时钟输出源：HIRC */
#define RCC_SYSCLKOSEL_HXT ((uint32_t)(0x01 << 8))  /*!< 时钟输出源：HXT */
#define RCC_SYSCLKOSEL_LIRC ((uint32_t)(0x02 << 8)) /*!< 时钟输出源：LIRC */
#define RCC_SYSCLKOSEL_LXT ((uint32_t)(0x03 << 8))  /*!< 时钟输出源：LXT */
#define RCC_SYSCLKOSEL_SYS ((uint32_t)(0x04 << 8))  /*!< 时钟输出源：SYSCLK */
#define RCC_SYSCLKOSEL_FCLK ((uint32_t)(0x05 << 8)) /*!< 时钟输出源：FCLK */
#define RCC_SYSCLKOSEL_MASK ((uint32_t)(0x07 << 8))

#define IS_RCC_SYSCLKOSEL_SOURCE(SOURCE) (((SOURCE) == RCC_SYSCLKOSEL_HIRC) || \
                                          ((SOURCE) == RCC_SYSCLKOSEL_HXT) ||  \
                                          ((SOURCE) == RCC_SYSCLKOSEL_LIRC) || \
                                          ((SOURCE) == RCC_SYSCLKOSEL_LXT) ||  \
                                          ((SOURCE) == RCC_SYSCLKOSEL_SYS) ||  \
                                          ((SOURCE) == RCC_SYSCLKOSEL_FCLK))

#define RCC_SYSCLKOUTPUT_ENABLE 0x1000

/** @defgroup AHB_peripheral_CLK
 * @{
 */
#define RCC_AHBPeriph_GPIOAEN ((uint32_t)0x00000001) /*!< GPIOA端口时钟使能 */
#define RCC_AHBPeriph_GPIOBEN ((uint32_t)0x00000002) /*!< GPIOB端口时钟使能 */
#define RCC_AHBPeriph_GPIOCEN ((uint32_t)0x00000004) /*!< GPIOC端口时钟使能 */
#define RCC_AHBPeriph_GPIODEN ((uint32_t)0x00000008) /*!< GPIOD端口时钟使能 */
#define RCC_AHBPeriph_CRCEN ((uint32_t)0x00000010)   /*!< CRC外设时钟使能   */
#define RCC_AHBPeriph_FLASHCE ((uint32_t)0x00000100) /*!< FLASH外设时钟使能 */
#define IS_RCC_AHB_PERIPH(PERIPH) ((((PERIPH)&0xFFFFFF00) == 0x00) && ((PERIPH) != 0x00))

    /** @defgroup APB_peripheral_CLKEN
     * @{
     */
    // #define RCC_APBPeriph_UART0CKEN              ((uint32_t)0x00000001)
    // #define RCC_APBPeriph_UART1CKEN              ((uint32_t)0x00000002)
    // #define RCC_APBPeriph_I2CCKEN                ((uint32_t)0x00000004)
    // #define RCC_APBPeriph_LPUARTCKEN             ((uint32_t)0x00000008)
    // #define RCC_APBPeriph_SPICKEN                ((uint32_t)0x00000010)
    // #define RCC_APBPeriph_LPTIMCKEN              ((uint32_t)0x00000020)
    // #define RCC_APBPeriph_BASETIMCKEN            ((uint32_t)0x00000040)
    // #define RCC_APBPeriph_SYSCONCKEN             ((uint32_t)0x00000080)
    // #define RCC_APBPeriph_PCACKEN                ((uint32_t)0x00000100)
    // #define RCC_APBPeriph_ONEWIRECKEN            ((uint32_t)0x00000200)
    // #define RCC_APBPeriph_PWMCKEN                ((uint32_t)0x00000400)
    // #define RCC_APBPeriph_IWDTCKEN               ((uint32_t)0x00000800)
    // #define RCC_APBPeriph_WWDTCKEN               ((uint32_t)0x00001000)
    // #define RCC_APBPeriph_ADCCKEN                ((uint32_t)0x00002000)
    // #define RCC_APBPeriph_AWKCKEN                ((uint32_t)0x00004000)
    // #define RCC_APBPeriph_RTCCKEN                ((uint32_t)0x00008000)
    // #define RCC_APBPeriph_TRIMCKEN               ((uint32_t)0x00010000)
    // #define RCC_APBPeriph_LVDVCCKEN              ((uint32_t)0x00040000)
    // #define RCC_APBPeriph_BEEPCKEN               ((uint32_t)0x00080000)
    // #define RCC_APBPeriph_MCUDBGCKEN             ((uint32_t)0x00100000)

#define RCC_APBPeriph_UART0CKEN ((uint32_t)(0x01 << 0))   /*!< UART0 PCLK 模块时钟使能 */
#define RCC_APBPeriph_UART1CKEN ((uint32_t)(0x01 << 1))   /*!< UART1 PCLK 模块时钟使能 */
#define RCC_APBPeriph_I2CCKEN ((uint32_t)(0x01 << 2))     /*!< I2C PCLK 模块时钟使能 */
#define RCC_APBPeriph_LPUARTCKEN ((uint32_t)(0x01 << 3))  /*!< Low Power UART PCLK 寄存器配置时钟使能 */
#define RCC_APBPeriph_SPICKEN ((uint32_t)(0x01 << 4))     /*!< SPI PCLK 模块时钟使能 */
#define RCC_APBPeriph_LPTIMCKEN ((uint32_t)(0x01 << 5))   /*!< Low Power Timer PCLK 时钟使能 */
#define RCC_APBPeriph_BASETIMCKEN ((uint32_t)(0x01 << 6)) /*!< TIM10/11 PCLK 时钟使能 */
#define RCC_APBPeriph_SYSCONCKEN ((uint32_t)(0x01 << 7))  /*!< SYSCON PCLK 时钟使能 */
#define RCC_APBPeriph_PCACKEN ((uint32_t)(0x01 << 8))     /*!< PCA PCLK 时钟使能 */
#define RCC_APBPeriph_ONEWIRECKEN ((uint32_t)(0x01 << 9)) /*!< 1-WIRE PCLK 时钟使能 */
#define RCC_APBPeriph_TIM1CKEN ((uint32_t)(0x01 << 10))   /*!< TIM1 PCLK 时钟使能 */
#define RCC_APBPeriph_TIM2CKEN ((uint32_t)(0x01 << 11))   /*!< TIM2 PCLK 时钟使能 */
#define RCC_APBPeriph_WWDTCKEN ((uint32_t)(0x01 << 12))   /*!< WWDG PCLK 时钟使能 */
#define RCC_APBPeriph_ADCCKEN ((uint32_t)(0x01 << 13))    /*!< ADC PCLK 时钟使能 */
#define RCC_APBPeriph_AWKCKEN ((uint32_t)(0x01 << 14))    /*!< AWK PCLK 时钟使能 */
#define RCC_APBPeriph_RTCCKEN ((uint32_t)(0x01 << 15))    /*!< RTC PCLK 时钟使能 */
#define RCC_APBPeriph_TRIMCKEN ((uint32_t)(0x01 << 16))   /*!< CLKTRIM PCLK 时钟使能 */
#define RCC_APBPeriph_IWDTCKEN ((uint32_t)(0x01 << 17))   /*!< IWDG PCLK 时钟使能 */
#define RCC_APBPeriph_LVDVCCKEN ((uint32_t)(0x01 << 18))  /*!< LVD/VC PCLK 时钟使能 */
#define RCC_APBPeriph_BEEPCKEN ((uint32_t)(0x01 << 19))   /*!< BEEP PCLK 时钟使能 */
#define RCC_APBPeriph_MCUDBGCKEN ((uint32_t)(0x01 << 20)) /*!< Debug PCLK 时钟使能 */

#define IS_RCC_APB_PERIPH(PERIPH) ((((PERIPH)&0xFFE00000) == 0x00) && ((PERIPH) != 0x00))

/** @defgroup APB_peripheral RST
 * @{
 */
#define RCC_APBPeriph_UART0RST ((uint32_t)(0x01 << 0))   /*!< UART0 模块复位 */
#define RCC_APBPeriph_UART1RST ((uint32_t)(0x01 << 1))   /*!< UART1 模块复位 */
#define RCC_APBPeriph_I2CRST ((uint32_t)(0x01 << 2))     /*!< I2C 模块复位 */
#define RCC_APBPeriph_LPUARTRST ((uint32_t)(0x01 << 3))  /*!< LPUART 模块复位 */
#define RCC_APBPeriph_SPIRST ((uint32_t)(0x01 << 4))     /*!< SPI 模块复位 */
#define RCC_APBPeriph_LPTIMRST ((uint32_t)(0x01 << 5))   /*!< Low Power Timer0/1 复位 */
#define RCC_APBPeriph_BASETIMRST ((uint32_t)(0x01 << 6)) /*!< Base Timer10/11 复位 */
#define RCC_APBPeriph_SYSCONRST ((uint32_t)(0x01 << 7))  /*!< SYSCON 模块复位 */
#define RCC_APBPeriph_PCARST ((uint32_t)(0x01 << 8))     /*!< PCA 模块复位 */
#define RCC_APBPeriph_ONEWIRERST ((uint32_t)(0x01 << 9)) /*!< 1-Wire 模块复位 */
#define RCC_APBPeriph_TIM1RST ((uint32_t)(0x01 << 10))   /*!< TIM1 模块复位 */
#define RCC_APBPeriph_TIM2RST ((uint32_t)(0x01 << 11))   /*!< TIM2 模块复位 */
#define RCC_APBPeriph_WWDTRST ((uint32_t)(0x01 << 12))   /*!< WWDG 模块复位 */
#define RCC_APBPeriph_ADCRST ((uint32_t)(0x01 << 13))    /*!< ADC 模块复位 */
#define RCC_APBPeriph_AWKRST ((uint32_t)(0x01 << 14))    /*!< AWK 模块复位 */
#define RCC_APBPeriph_TRIMRST ((uint32_t)(0x01 << 16))   /*!< Clock TRIM 模块复位 */
#define RCC_APBPeriph_LVDVCRST ((uint32_t)(0x01 << 18))  /*!< LVD/VC 模块复位 */
#define RCC_APBPeriph_BEEPRST ((uint32_t)(0x01 << 19))   /*!< BEEP 模块复位 */
#define RCC_APBPeriph_MCUDBGRST ((uint32_t)(0x01 << 20)) /*!< MCU DEBUG 模块复位 */
#define RCC_AHBPeriph_GPIOARST ((uint32_t)(0x01 << 24))  /*!< GPIOA 模块复位 */
#define RCC_AHBPeriph_GPIOBRST ((uint32_t)(0x01 << 25))  /*!< GPIOB 模块复位 */
#define RCC_AHBPeriph_GPIOCRST ((uint32_t)(0x01 << 26))  /*!< GPIOC 模块复位 */
#define RCC_AHBPeriph_GPIODRST ((uint32_t)(0x01 << 27))  /*!< GPIOD 模块复位 */
#define RCC_AHBPeriph_CRCRST ((uint32_t)(0x01 << 28))    /*!< CRC 模块复位 */

#define IS_RCC_PERIPHRST(PERIPH) ((((PERIPH)&0x1f1d7fff) == PERIPH) && ((PERIPH) != 0x00))

#define RCC_RTCPeriph_RSTEABLE ((uint32_t)0x01)
#define RCC_RTCPeriph_RSTDISABLE ((uint32_t)0x00)

/** @defgroup RST MODEL
 * @{
 */
#define RCC_MCURST ((uint32_t)0x01)
#define RCC_CPURST ((uint32_t)0x02)
#define IS_RCC_RST(RST) ((RST == RCC_MCURST) || (RST == RCC_CPURST))

/** @defgroup GET RCC FLAG
 * @{
 */
#define RCC_FLAG_MCURST ((uint32_t)0x00000001)
#define RCC_FLAG_CPURST ((uint32_t)0x00000002)
#define RCC_FLAG_WWDTRST ((uint32_t)0x00000004)
#define RCC_FLAG_IWDTRST ((uint32_t)0x00000008)
#define RCC_FLAG_LVDRST ((uint32_t)0x00000010)
#define RCC_FLAG_PORRST ((uint32_t)0x00000020)
#define RCC_FLAG_LOCKUPRST ((uint32_t)0x00000040)
#define RCC_FLAG_PADRST ((uint32_t)0x00000080)
#define IS_RCC_FLAG_RST(FLAG) ((((FLAG)&0xFFFFFF00) == 0x00) && ((FLAG) != 0x00))

/** @defgroup GET RCC FLAG
 * @{
 */
#define RCC_FLAG_HIRCRDY ((uint32_t)0x00001000)
#define RCC_FLAG_HXTRDY ((uint32_t)0x00000040)
#define RCC_FLAG_LIRCRDY ((uint32_t)0x00001000)
#define RCC_FLAG_LXTRDY ((uint32_t)0x00000040)

/** @defgroup GET RCC FLAG
 * @{
 */
#define RCC_HXT_20M30M ((uint32_t)(0x03 << 2))
#define RCC_HXT_12M20M ((uint32_t)(0x02 << 2))
#define RCC_HXT_6M12M ((uint32_t)(0x01 << 2))
#define RCC_HXT_4M6M ((uint32_t)(0x00 << 2))
#define IS_RCC_HXT_CLK(CLK) (((CLK & 0xFFFFFFF0) == 0x00) && (CLK >= 0x00))

#define RCC_HXT_DRIVER_LEVEL3 ((uint32_t)0x03)
#define RCC_HXT_DRIVER_LEVEL2 ((uint32_t)0x02)
#define RCC_HXT_DRIVER_LEVEL1 ((uint32_t)0x01)
#define RCC_HXT_DRIVER_LEVEL0 ((uint32_t)0x00)
#define IS_RCC_HXT_DRIVER(LEVEL) (((LEVEL & 0xFFFFFFF0) == 0x00) && (LEVEL >= 0x00))

#define RCC_HIRC_TRIM_MASK ((uint32_t)0x00000FFF)

#define RCC_HIRC_TRIM_24MHZ ((uint32_t)0x00000001)
#define RCC_HIRC_TRIM_22P12MHZ ((uint32_t)0x00000002)
#define RCC_HIRC_TRIM_16MHZ ((uint32_t)0x00000003)
#define RCC_HIRC_TRIM_8MHZ ((uint32_t)0x00000004)
#define RCC_HIRC_TRIM_4MHZ ((uint32_t)0x00000005)

#define RCC_LIRC_TRIM_MASK ((uint32_t)0x000001FF)

#define RCC_LIRC_TRIM_32768HZ ((uint32_t)0x00000001)
#define RCC_LIRC_TRIM_38400HZ ((uint32_t)0x00000002)

#define RCC_HXT_DRIVER_MASK ((uint32_t)0x0000000F)
#define RCC_HXT_STARTUP_MASK ((uint32_t)0x00000030)
#define RCC_LIRC_STARTUP_MASK ((uint32_t)0x000000C0)
#define RCC_LXT_STARTUP_MASK ((uint32_t)0x000000C0)
#define RCC_LXT_DRIVER_MASK ((uint32_t)0x0000000F)

#define RCC_LXT_AON_MASK ((uint32_t)0x00000400)
#define RCC_LXT_AON_EN ((uint32_t)0x00000100)
#define RCC_LXT_BYP_EN ((uint32_t)0x00000200)

#define RCC_HXT_BYP_EN ((uint32_t)(0x01 << 5))
#define RCC_HXT_PORT_EN ((uint32_t)(0x01 << 6))
#define RCC_X32K_PORT_EN ((uint32_t)0x00000800)
#define RCC_SYSTEMTICKTIMER_STCALIB_MASK ((uint32_t)0x01FFFFFF)

#define RCC_SYSTEMTICKTIMER_REFCORE_CLOCK ((uint32_t)0x02000000)

#define RCC_HIRC_STARTUP_PERIOD0 ((uint32_t)0x00)
#define RCC_HIRC_STARTUP_PERIOD2 ((uint32_t)0x01)
#define RCC_HIRC_STARTUP_PERIOD4 ((uint32_t)0x02)
#define RCC_HIRC_STARTUP_PERIOD32 ((uint32_t)0x03)
#define RCC_HIRC_STARTUP_PERIOD256 ((uint32_t)0x04)
#define RCC_HIRC_STARTUP_PERIOD1024 ((uint32_t)0x05)
#define RCC_HIRC_STARTUP_PERIOD4096 ((uint32_t)0x06)
#define RCC_HIRC_STARTUP_PERIOD8192 ((uint32_t)0x07)
#define IS_RCC_HIRC_STARTUP_PERIOD(PERIOD) (((PERIOD & 0xFFFFFFFF8) == 0x00) && ((PERIOD >= 0x00)))

#define RCC_HXT_STARTUP_PERIOD256 ((uint32_t)(0x00 << 4))
#define RCC_HXT_STARTUP_PERIOD1024 ((uint32_t)(0x01 << 4))
#define RCC_HXT_STARTUP_PERIOD4096 ((uint32_t)(0x02 << 4))
#define RCC_HXT_STARTUP_PERIOD16384 ((uint32_t)(0x03 << 4))
#define IS_RCC_HXT_STARTUP_PERIOD(PERIOD) (((PERIOD & 0xFFFFFFCF) == 0x00) && ((PERIOD >= 0x00)))

#define RCC_LIRC_STARTUP_PERIOD4 ((uint32_t)(0x00 << 10))
#define RCC_LIRC_STARTUP_PERIOD16 ((uint32_t)(0x01 << 10))
#define RCC_LIRC_STARTUP_PERIOD64 ((uint32_t)(0x02 << 10))
#define RCC_LIRC_STARTUP_PERIOD256 ((uint32_t)(0x03 << 10))
#define IS_RCC_LIRC_STARTUP_PERIOD(PERIOD) (((PERIOD & 0xFFFFFF3F) == 0x00) && ((PERIOD >= 0x00)))

#define RCC_LXT_STARTUP_PERIOD1024 ((uint32_t)(0x00 << 4))
#define RCC_LXT_STARTUP_PERIOD2048 ((uint32_t)(0x01 << 4))
#define RCC_LXT_STARTUP_PERIOD4096 ((uint32_t)(0x02 << 4))
#define RCC_LXT_STARTUP_PERIOD16384 ((uint32_t)(0x03 << 4))
#define IS_RCC_LXT_STARTUP_PERIOD(PERIOD) (((PERIOD & 0xFFFFFFCF) == 0x00) && ((PERIOD >= 0x00)))

#define PIN_SWD_FUNCTION ((uint32_t)0x01)
#define PIN_GPIO_FUNCTION ((uint32_t)0x00)
#define IS_PIN_FUNCTION(FUNCTION) ((FUNCTION == PIN_SWD_FUNCTION) || (FUNCTION == PIN_GPIO_FUNCTION))

#if !defined(Fpclk)
#define Fpclk ((uint32_t)4000000) /*!< Value of the External Low Speed oscillator in Hz */
// #define Fpclk  ((uint32_t)16000000)
#endif /* LSE_VALUE */

    /**
     * @brief In the following line adjust the value of External High Speed oscillator (HSE)
       used in your application

       Tip: To avoid modifying this file each time you need to use different HSE, you
            can define the HSE value in your toolchain compiler preprocessor.
      */

#if !defined(HICR_STABLE_TIMEOUT)
#define HICR_STABLE_TIMEOUT ((uint16_t)0x5000) /*!< Time out for HICR STABLE */
#endif                                         /*HICR_STABLE_TIMEOUT */

/**
 * @brief In the following line adjust the Internal High Speed oscillator (HSI) Startup
   Timeout value
   */
#if !defined(HXT_STABLE_TIMEOUT)
#define HXT_STABLE_TIMEOUT ((uint16_t)0x5000) /*!< Time out for HICR STABLE */
#endif                                        /* HSI_STARTUP_TIMEOUT */

/**
 * @brief In the following line adjust the Internal High Speed oscillator (HSI) Startup
   Timeout value
   */
#if !defined(LICR_STABLE_TIMEOUT)
#define LICR_STABLE_TIMEOUT ((uint16_t)0x5000) /*!< Time out for HICR STABLE */ * /
#endif /* HSI_VALUE */

/**
 * @brief In the following line adjust the Internal High Speed oscillator (HSI) Startup
   Timeout value
   */
#if !defined(LXT_STABLE_TIMEOUT)
#define LXT_STABLE_TIMEOUT ((uint16_t)0x5000) /*!< Time out for HICR STABLE */
#endif                                        /* HSI14_VALUE */

/**
 * @brief In the following line adjust the Internal High Speed oscillator (HSI) Startup
   Timeout value
   */
#if !defined(SYSTEMTICKTIMER_SKEW_TIMEOUT)
#define SYSTEMTICKTIMER_SKEW_TIMEOUT ((uint16_t)0x5000) /*!< Time out for HICR STABLE */
#endif                                                  /* HSI14_VALUE */

    /* Exported functions --------------------------------------------------------*/
    void RCC_SysClkSourceSwitchHirc(RCC_TypeDef *RCCx,uint32_t RCC_HIRCCR_Sel);
    void RCC_DeInit(void);
    void RCC_HIRCTrim(RCC_TypeDef *RCCx, uint32_t RCC_HIRCCR_Sel);
    void RCC_LIRCTrim(RCC_TypeDef *RCCx, uint32_t RCC_LIRCCR_Sel);
    void RCC_SetHXTDrive(RCC_TypeDef *RCCx, uint32_t HXTCKL, uint32_t HXTDRIVER);
    void RCC_SetLXTDrive(RCC_TypeDef *RCCx, uint8_t LXTAM, uint8_t LXTDRIVER);
    ErrorStatus RCC_WaitForHIRCStable(RCC_TypeDef *RCCx);
    ErrorStatus RCC_WaitForHXTStable(RCC_TypeDef *RCCx, uint32_t PERIOD);
    ErrorStatus RCC_WaitForLIRCStable(RCC_TypeDef *RCCx, uint32_t PERIOD);
    ErrorStatus RCC_WaitForLXTStable(RCC_TypeDef *RCCx, uint32_t PERIOD);
    void RCC_LXTCmd(RCC_TypeDef *RCCx, LXT_InitTypeDef *LXT_InitStruct);
    void RCC_SetX32KPort(RCC_TypeDef *RCCx, FunctionalState NewState);
    void RCC_SetHXTPort(RCC_TypeDef *RCCx, FunctionalState NewState);
    void RCC_SetM0IRQLatency(RCC_TypeDef *RCCx, uint32_t IRQLATENCY);
    void RCC_SystemTickTimerConfig(RCC_TypeDef *RCCx, uint8_t SKEW, uint32_t TICKTIMERSTCALIB);
    void RCC_SetSystemTickTimerCoreRefClockcmd(RCC_TypeDef *RCCx, FunctionalState NewState);
    void RCC_SysclkCmd(RCC_TypeDef *RCCx, uint32_t RCC_SYSCLKSource, FunctionalState NewState);
    void RCC_SelSysclk(RCC_TypeDef *RCCx, uint32_t RCC_SYSCLKSource);
    void RCC_ClkOutputSourceConfig(RCC_TypeDef *RCCx, uint32_t RCC_SYSCLKOutSource);
    void RCC_ClkOutputCmd(RCC_TypeDef *RCCx, FunctionalState NewState);
    void RCC_ClkFclkDiv(RCC_TypeDef *RCCx, uint8_t RCC_SYSCLKSource_DIV);
    uint8_t RCC_GetSysclkSource(RCC_TypeDef *RCCx);
    void RCC_SetHclkDiv(RCC_TypeDef *RCCx, uint8_t RCC_SYSCLK_DIV);
    void RCC_SetPclkDiv(RCC_TypeDef *RCCx, uint8_t RCC_HCLK_DIV);
    void RCC_GetClocksFreq(RCC_TypeDef *RCCx, RCC_ClocksTypeDef *RCC_Clocks);
    void RCC_AHBPeriphClockCmd(RCC_TypeDef *RCCx, uint32_t RCC_AHBPeriph, FunctionalState NewState);
    void RCC_APBPeriphClockCmd(RCC_TypeDef *RCCx, uint32_t RCC_APBPeriph, FunctionalState NewState);
    void RCC_PeriphResetCmd(RCC_TypeDef *RCCx, uint32_t RCC_PeriphRst, FunctionalState NewState);
    void RCC_RTCResetCmd(RCC_TypeDef *RCCx, FunctionalState NewState);
    void RCC_ResetCmd(RCC_TypeDef *RCCx, uint32_t RCC_RSTModle, FunctionalState NewState);
    FlagStatus RCC_GetFlagStatus(RCC_TypeDef *RCCx, uint32_t RCC_FLAG_RST);
    void RCC_SetIoMux(RCC_TypeDef *RCCx, FunctionalState NewState);
    uint32_t HAL_RCC_GetFclk(void);
    uint32_t HAL_RCC_GetPclk(void);

    void HAL_RCC_DeepSleep(void);
    void HAL_RCC_Sleep(void);

#ifdef __cplusplus
}
#endif

#endif
