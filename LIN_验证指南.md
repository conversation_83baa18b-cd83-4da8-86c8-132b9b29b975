# LIN 协议验证指南

本文档提供了对 LIN_ZCUL_RLS_FRONTWIPER_IRMM_Protocol.ldf 文件的验证指南，帮助您确认协议文件的正确性和完整性。

## 1. 基本信息验证

### 协议版本

- LIN 协议版本: 2.1
- LIN 语言版本: 2.1
- LIN 波特率: 19200 bps

### 节点信息

| 节点名称 | 类型 | NAD | 产品ID |
|---------|------|-----|--------|
| ZCUL | 主节点 | - | - |
| RLS | 从节点 | 0x10 | 0x1234, 0x5678, 0 |
| FRONTWIPER | 从节点 | 0x11 | 0x1234, 0x5679, 0 |
| IRMM | 从节点 | 0x12 | 0x1234, 0x567A, 0 |

## 2. 帧ID和PID对应关系验证

| 帧名称 | 帧ID (十六进制) | PID (十六进制) | 长度 (字节) | 发送节点 |
|--------|----------------|---------------|------------|----------|
| ZCUL_01 | 0x31 | 0x71 | 8 | ZCUL |
| ZCUL_02 | 0x32 | 0x72 | 8 | ZCUL |
| ZCUL_03 | 0x33 | 0x73 | 8 | ZCUL |
| RLS_01 | 0x23 | 0x63 | 8 | RLS |
| RLS_02 | 0x24 | 0x64 | 8 | RLS |
| FRONTWIPER_01 | 0x25 | 0x65 | 8 | FRONTWIPER |
| IRMM_01 | 0x26 | 0xA6 | 8 | IRMM |
| MasterReq | 0x3C | 0x7C | 8 | ZCUL |
| SlaveResp | 0x3D | 0x7D | 8 | 从节点 |

> 注意：PID = ID XOR (ID >> 4 XOR ID >> 5 XOR ID >> 6 XOR ID >> 7) XOR 0x80

## 3. 信号定义验证

### ZCUL_01 帧信号

| 信号名称 | 长度 (位) | 起始位置 | 初始值 | 描述 |
|---------|----------|---------|--------|------|
| ZCUL_Status_IGN | 1 | 0 | 0 | 点火状态 |
| ZCUL_CMD_AutoWiper | 1 | 1 | 0 | 自动雨刷命令 |
| ZCUL_RQ_FrontWash | 1 | 2 | 0 | 前洗涤请求 |
| ZCUL_ParkPosition | 1 | 3 | 0 | 停车位置 |
| ZCUL_RainSensitivity | 3 | 4 | 0 | 雨量敏感度 |
| ... | ... | ... | ... | ... |

### RLS_01 帧信号

| 信号名称 | 长度 (位) | 起始位置 | 初始值 | 描述 |
|---------|----------|---------|--------|------|
| RLS_RQ_WiperSPD | 8 | 0 | 0 | 雨刷速度请求 |
| RLS_RQ_RainLevel | 8 | 8 | 0 | 雨量等级请求 |
| RLS_RQ_LowBeam | 1 | 16 | 0 | 近光灯请求 |
| ... | ... | ... | ... | ... |

### IRMM_01 帧信号

| 信号名称 | 长度 (位) | 起始位置 | 初始值 | 描述 |
|---------|----------|---------|--------|------|
| IntrMirrDimRespIntrMirrDimPerc | 8 | 0 | 0 | 内部后视镜防眩响应百分比 |
| IntrMirrDimRespIntrMirrIntFailr | 1 | 8 | 0 | 内部后视镜防眩响应内部故障 |
| ... | ... | ... | ... | ... |

## 4. 调度表验证

### Dynamic 调度表

| 时隙 | 帧ID | 延迟 (ms) |
|------|------|----------|
| 1 | 0x31 (ZCUL_01) | 10 |
| 2 | 0x32 (ZCUL_02) | 10 |
| 3 | 0x33 (ZCUL_03) | 10 |
| 4 | 0x23 (RLS_01) | 10 |
| 5 | 0x24 (RLS_02) | 10 |
| 6 | 0x31 (ZCUL_01) | 10 |
| 7 | 0x25 (FRONTWIPER_01) | 10 |
| 8 | 0x26 (IRMM_01) | 10 |
| 9 | 0x23 (RLS_01) | 10 |
| 10 | 0x24 (RLS_02) | 10 |

## 5. 信号编码验证

### 编码类型

| 编码名称 | 类型 | 描述 |
|---------|------|------|
| DimSnvty_Encoding | 枚举 | 内部后视镜防眩敏感度 |
| Boolean_Encoding | 枚举 | 布尔值编码 |
| Percentage_Encoding | 物理值 | 百分比编码 |
| Speed_Encoding | 混合 | 速度编码 |
| Temperature_Encoding | 物理值 | 温度编码 |
| Brightness_Encoding | 物理值 | 亮度编码 |

## 6. 验证步骤

1. **协议文件语法验证**：
   - 使用 LIN 协议验证工具（如 Vector LDF Explorer）检查 LDF 文件的语法正确性

2. **节点通信验证**：
   - 确认主节点 ZCUL 能够正确发送调度表中的帧
   - 确认从节点能够正确响应主节点的请求

3. **信号值验证**：
   - 验证各信号的值是否在定义的范围内
   - 验证信号编码是否正确应用

4. **诊断功能验证**：
   - 测试主节点发送诊断请求帧
   - 验证从节点正确响应诊断请求

## 7. 常见问题

1. **PID 计算错误**：
   - 确保 PID 计算公式正确应用
   - 验证帧 ID 和 PID 的对应关系

2. **信号定义不完整**：
   - 检查所有信号是否都有正确的长度、起始位置和初始值
   - 确保信号的发送节点和接收节点定义正确

3. **调度表不匹配**：
   - 确保调度表中的帧 ID 与帧定义一致
   - 验证调度表的时序是否符合系统要求

## 8. 参考资料

- LIN 规范 2.1 版本
- Vector LIN 系统描述
- LIN_ZCUL_RLS_FRONTWIPER_IRMM_V1.0_20250109 文档