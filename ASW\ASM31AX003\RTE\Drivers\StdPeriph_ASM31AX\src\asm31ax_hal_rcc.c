#include "asm31ax_hal.h"

#ifdef HAL_RCC_MODULE_ENABLED

void RCC_SysClkSourceSwitchHirc(RCC_TypeDef *RCCx, uint32_t RCC_HIRCCR_Sel)
{

    uint8_t ClkStatus = 0;

    /* 获取HIRC, HXT, LIRC使能状态 */
    ClkStatus = RCC_GetSysclkSource(RCC);

    /* 判断HIRC时钟是否开启 */
    if ((ClkStatus & 0x01) != 0x01)
    {
        RCC_SysclkCmd(RCC, RCC_SYSCLKSource_HIRC, ENABLE); // 使能HIRC时钟
    }

    /* HIRC时钟频率调整及校准 */
    RCC_HIRCTrim(RCC, RCC_HIRCCR_Sel);

    /* 等待HIRC时钟稳定 */
    RCC_WaitForHIRCStable(RCC);
}

/**
 * @brief  Resets the RCC clock configuration to the default reset state.
 * @param  None
 * @retval None
 */
void RCC_DeInit(void)
{
    /*Close AHB Clock*/
    RCC->HCLKEN &= ~RCC_AHB_CLKMASK;

    /*Close APB Clock*/
    RCC->HCLKEN &= ~RCC_APB_CLKMASK;
}

/**
 * @brief  Configures HIRC Triming vlaue.
 * @param  RCCx: Selects the RCC peripheral
 * @param  RCC_HIRCCR_Sel: specifies the HIRC Triming value used as HIRC Frq.
 *   This parameter can be one of the following values:
 *     @arg RCC_HIRC_TRIM_24MHZ     :  Set HIRC to 24MHz and trim Freq.
 *     @arg RCC_HIRC_TRIM_22P12MHZ  :  Set HIRC to 22.12MHz and trim Freq.
 *     @arg RCC_HIRC_TRIM_16MHZ     :  Set HIRC to 16MHz and trim Freq.
 *     @arg RCC_HIRC_TRIM_8MHZ      :  Set HIRC to 8MHz and trim Freq.
 *     @arg RCC_HIRC_TRIM_4MHZ      :  Set HIRC to 4MHz and trim Freq.
 * @retval None
 */
void RCC_HIRCTrim(RCC_TypeDef *RCCx, uint32_t RCC_HIRCCR_Sel)
{
    uint32_t HIRC_TrimValue;

    /*Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));

    switch (RCC_HIRCCR_Sel)
    {
    case RCC_HIRC_TRIM_24MHZ:
        HIRC_TrimValue = (*(volatile uint32_t *)0x180000A0);
        if (0xFFFFFFFF == HIRC_TrimValue)
        {
            HIRC_TrimValue = (*(volatile uint32_t *)0x180000C0);
        }
        HIRC_TrimValue = (HIRC_TrimValue)&0xfff;
        break;
    case RCC_HIRC_TRIM_22P12MHZ:
        HIRC_TrimValue = (*(volatile uint32_t *)0x180000A0);
        if (0xFFFFFFFF == HIRC_TrimValue)
        {
            HIRC_TrimValue = (*(volatile uint32_t *)0x180000C0);
        }
        HIRC_TrimValue = (HIRC_TrimValue >> 16) & 0xfff;
        break;
    case RCC_HIRC_TRIM_16MHZ:
        HIRC_TrimValue = (*(volatile uint32_t *)0x180000A4);
        if (0xFFFFFFFF == HIRC_TrimValue)
        {
            HIRC_TrimValue = (*(volatile uint32_t *)0x180000C4);
        }
        HIRC_TrimValue = (HIRC_TrimValue)&0xfff;
        break;
    case RCC_HIRC_TRIM_8MHZ:
        HIRC_TrimValue = (*(volatile uint32_t *)0x180000A4);
        if (0xFFFFFFFF == HIRC_TrimValue)
        {
            HIRC_TrimValue = (*(volatile uint32_t *)0x180000C4);
        }
        HIRC_TrimValue = (HIRC_TrimValue >> 16) & 0xfff;
        break;
    case RCC_HIRC_TRIM_4MHZ:
        HIRC_TrimValue = (*(volatile uint32_t *)0x180000A8);
        if (0xFFFFFFFF == HIRC_TrimValue)
        {
            HIRC_TrimValue = (*(volatile uint32_t *)0x180000C8);
        }
        HIRC_TrimValue = (HIRC_TrimValue)&0xfff;
        break;
    default:
        break;
    }

    /*Close the lock of register*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;

    /*Set the Triming value to the HIRCCR register*/
    RCCx->HIRCCR &= ~RCC_HIRC_TRIM_MASK;
    RCCx->HIRCCR = 0x5A690000 + HIRC_TrimValue;

    /*Open the lock of register*/
    // RCCx->UNLOCK = RCC_RESGLOCKKEY;
    RCCx->UNLOCK = 0;
}

/**
 * @brief  Configures LIRC Triming vlaue.
 * @param  RCCx: Selects the RCC peripheral
 * @param  RCC_LIRCCR_Sel: specifies the LIRC Triming value used as LIRC Frq.
 *   This parameter can be one of the following values:
 *     @arg RCC_LIRC_TRIM_32768HZ:  Set LIRC to 32.768KHz and trim Freq.
 *     @arg RCC_LIRC_TRIM_38400HZ:  Set LIRC to 38.4KHz and trim Freq.
 * @retval None
 */
void RCC_LIRCTrim(RCC_TypeDef *RCCx, uint32_t RCC_LIRCCR_Sel)
{
    uint32_t LIRC_TrimValue;

    /*Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));

    switch (RCC_LIRCCR_Sel)
    {
    case RCC_LIRC_TRIM_32768HZ:
        LIRC_TrimValue = (*(volatile uint32_t *)0x180000D0);
        break;
    case RCC_LIRC_TRIM_38400HZ:
        LIRC_TrimValue = (*(volatile uint32_t *)0x180000D4);
        break;
    default:
        LIRC_TrimValue = (*(volatile uint32_t *)0x180000D0);
        break;
    }

    // LIRC_TrimValue = LIRC_TrimValue & 0x000000FF;
    LIRC_TrimValue &= 0x000001FF;

    /*Close the lock of register*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;

    /*Set the Triming value to the LIRCCR register*/
    RCCx->LIRCCR &= ~RCC_LIRC_TRIM_MASK;
    RCCx->LIRCCR = 0x5A690000 + LIRC_TrimValue;

    /*Open the lock of register*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
}

/**
 * @brief  Set the HXT DRIVE function
 * @param  RCCx: Selects the RCC peripheral
 * @param  HXTCKL: Selects the hxt clk
 * @param  HXTDRIVER: Set the value to the HXT DRIVER of register
 * @retval None
 */
void RCC_SetHXTDrive(RCC_TypeDef *RCCx, uint32_t HXTCKL, uint32_t HXTDRIVER)
{
    uint32_t HXTDRIVER1 = 0;

    /*Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_HXT_CLK(HXTCKL));
    assert_param(IS_RCC_HXT_DRIVER(HXTDRIVER));

    /*Set the value of HXT DRIVER*/
    HXTDRIVER1 = (uint32_t)(HXTCKL | HXTDRIVER);

    /*Close the lock of register*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;

    /*Set the value of driver to the HXTCR register*/
    RCCx->HXTCR &= ~RCC_HXT_DRIVER_MASK;
    RCCx->HXTCR |= HXTDRIVER1;

    /*Open the lock of register*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
}

/**
 * @brief  Set the LXT DRIVE function
 * @param  RCCx: Selects the RCC peripheral
 * @param  LXTAM: Selects the lxt clk
 * @param  LXTDRIVER: Set the value to the LXT DRIVER of register
 * @retval None
 */
void RCC_SetLXTDrive(RCC_TypeDef *RCCx, uint8_t LXTAM, uint8_t LXTDRIVER)
{
    uint32_t LXTDRIVER1 = 0;
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));

    /*Set the value of LXT DRIVER*/
    LXTDRIVER1 = (uint32_t)(LXTAM | LXTDRIVER);

    /*Close the lock of register*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;

    /*Set the value of driver to the LXTCR register*/
    RCCx->HXTCR &= ~RCC_LXT_DRIVER_MASK;
    RCCx->HXTCR |= LXTDRIVER1;

    /*Open the lock of register*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
}

/**
 * @brief  Waits for HIRC Stable.
 * @param  RCCx: Selects the RCC peripheral
 * @retval An ErrorStatus enumuration value:
 * - SUCCESS: HSE oscillator is stable and ready to use
 * - ERROR: HSE oscillator not yet ready
 */
ErrorStatus RCC_WaitForHIRCStable(RCC_TypeDef *RCCx)
{
    __IO uint32_t StartUpCounter = 0;
    uint32_t HIRCStatus = 0;
    ErrorStatus status = ERROR;

    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));

    /* Wait till HICR STABLE*/
    do
    {
        HIRCStatus = (RCCx->HIRCCR & RCC_FLAG_HIRCRDY);
        StartUpCounter++;
    } while ((StartUpCounter != HICR_STABLE_TIMEOUT) && (HIRCStatus == RCC_FLAG_HIRCRDY));

    if (HIRCStatus == RCC_FLAG_HIRCRDY)
    {
        status = SUCCESS;
    }
    else
    {
        status = ERROR;
    }
    return (status);
}

/**
 * @brief  Waits for HXT Stable.
 * @param  RCCx: Selects the RCC peripheral
 * @param  PERIOD: the time of HXT stable
 *   This parameter can be one of the following values:
 *     @arg RCC_HXT_STARTUP_PERIOD256
 *     @arg RCC_HXT_STARTUP_PERIOD1024
 *     @arg RCC_HXT_STARTUP_PERIOD4096
 *     @arg RCC_HXT_STARTUP_PERIOD16384
 * @retval An ErrorStatus enumuration value:
 * - SUCCESS: HSE oscillator is stable and ready to use
 * - ERROR: HSE oscillator not yet ready
 */
ErrorStatus RCC_WaitForHXTStable(RCC_TypeDef *RCCx, uint32_t PERIOD)
{
    __IO uint32_t StartUpCounter = 0;
    uint32_t HXTStatus = 0;
    ErrorStatus status = ERROR;

    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_HXT_STARTUP_PERIOD(PERIOD));

    /*Close the lock of register*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;
    /*Set HXT STARTUP*/
    RCCx->HXTCR &= ~RCC_HXT_STARTUP_MASK;
    RCCx->HXTCR |= PERIOD;

    /* Wait till HXT STABLE*/
    do
    {
        HXTStatus = (RCCx->HXTCR & RCC_FLAG_HXTRDY);
        StartUpCounter++;
    } while ((StartUpCounter != HXT_STABLE_TIMEOUT) && (HXTStatus == RCC_FLAG_HXTRDY));

    if (HXTStatus == RCC_FLAG_HXTRDY)
    {
        status = SUCCESS;
    }
    else
    {
        status = ERROR;
    }

    /*Open the lock of register*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
    return (status);
}

/**
 * @brief  Waits for LIRC Stable.
 * @param  RCCx: Selects the RCC peripheral
 * @param  PERIOD: the time of LIRC stable
 *   This parameter can be one of the following values:
 *     @arg RCC_LIRC_STARTUP_PERIOD4
 *     @arg RCC_LIRC_STARTUP_PERIOD16
 *     @arg RCC_LIRC_STARTUP_PERIOD64
 *     @arg RCC_LIRC_STARTUP_PERIOD256
 * @retval An ErrorStatus enumuration value:
 * - SUCCESS: HSE oscillator is stable and ready to use
 * - ERROR: HSE oscillator not yet ready
 */
ErrorStatus RCC_WaitForLIRCStable(RCC_TypeDef *RCCx, uint32_t PERIOD)
{
    __IO uint32_t StartUpCounter = 0;
    uint32_t LIRCStatus = 0;
    ErrorStatus status = ERROR;

    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_LIRC_STARTUP_PERIOD(PERIOD));

    /*Close the lock of register*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;
    /*Set LIRC STARTUP*/
    RCCx->LIRCCR &= ~RCC_LIRC_STARTUP_MASK;
    RCCx->LIRCCR |= PERIOD;

    /* Wait till LIRC STABLE*/
    do
    {
        LIRCStatus = (RCCx->LIRCCR & RCC_FLAG_LIRCRDY);
        StartUpCounter++;
    } while ((StartUpCounter != HXT_STABLE_TIMEOUT) && (LIRCStatus == RCC_FLAG_LIRCRDY));

    if (LIRCStatus == RCC_FLAG_LIRCRDY)
    {
        status = SUCCESS;
    }
    else
    {
        status = ERROR;
    }

    /*Open the lock of register*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
    return (status);
}

/**
 * @brief  Waits for LXT Stable.
 * @param  RCCx: Selects the RCC peripheral
 * @param  PERIOD: the time of LXT stable
 *   This parameter can be one of the following values:
 *     @arg RCC_LXT_STARTUP_PERIOD1024
 *     @arg RCC_LXT_STARTUP_PERIOD2048
 *     @arg RCC_LXT_STARTUP_PERIOD4096
 *     @arg RCC_LXT_STARTUP_PERIOD16384
 * @retval An ErrorStatus enumuration value:
 * - SUCCESS: HSE oscillator is stable and ready to use
 * - ERROR: HSE oscillator not yet ready
 */
ErrorStatus RCC_WaitForLXTStable(RCC_TypeDef *RCCx, uint32_t PERIOD)
{
    __IO uint32_t StartUpCounter = 0;
    uint32_t LXTStatus = 0;
    ErrorStatus status = ERROR;
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_LXT_STARTUP_PERIOD(PERIOD));

    /*Close the lock of register*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;

    /* Wait till HXT STABLE*/
    do
    {
        LXTStatus = (RCCx->LXTCR & RCC_FLAG_LXTRDY);
        StartUpCounter++;
    } while ((StartUpCounter != LXT_STABLE_TIMEOUT) && (LXTStatus == RCC_FLAG_LXTRDY));

    if (LXTStatus == RCC_FLAG_LXTRDY)
    {
        status = SUCCESS;
    }
    else
    {
        status = ERROR;
    }

    /*Open the lock of register*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
    return (status);
}

/**
 * @brief  Enables LXT function
 * @param  RCCx: Selects the RCC peripheral
 * @param  LXT_InitStruct: pointer to a SPI_InitTypeDef structure which will be used.
 * @retval None
 */
void RCC_LXTCmd(RCC_TypeDef *RCCx, LXT_InitTypeDef *LXT_InitStruct)
{
    /*Check the parameters */
    uint32_t tmp = 0, tmp1 = 0;
    uint32_t key = 0x5a690000;

    /*Set the parameters of LXT register */
    tmp1 = ((LXT_InitStruct->LXAON << 10) |
            (LXT_InitStruct->LXPORT << 11) |
            (LXT_InitStruct->LXTBYP << 9) |
            (LXT_InitStruct->LXTDIV << 0) |
            (LXT_InitStruct->LXTEN << 8) |
            (LXT_InitStruct->LXTSTARTUP << 4));

    /*Set the vlaue for lxt */
    tmp = key | tmp1;

    /*Close the lock of register*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;
    RCCx->LXTCR = tmp;

    /*Open the lock of register*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
}

/**
 * @brief  Set the port of 32k
 * @param  RCCx: Selects the RCC peripheral
 * @param  NewState: new state of the 32k port. This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void RCC_SetX32KPort(RCC_TypeDef *RCCx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    /*Close the lock of register*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;

    if (NewState != DISABLE)
    {
        /*Enable the 32k port*/
        RCCx->LXTCR |= RCC_X32K_PORT_EN;
    }
    else
    {
        /*Enable the GPIO port*/
        RCCx->LXTCR &= ~RCC_X32K_PORT_EN;
    }

    /*Open the lock of register*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
}

/**
 * @brief  Set the port of HXT
 * @param  RCCx: Selects the RCC peripheral
 * @param  NewState: new state of the HXT port. This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void RCC_SetHXTPort(RCC_TypeDef *RCCx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    /*Close the lock of register*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;

    if (NewState != DISABLE)
    {
        /*Enable the HXT port*/
        RCCx->SYSCLKCR |= RCC_HXT_PORT_EN;

        /* HXT 内部振荡模块未被旁路， 与 OSC_IN/OSC_OUT 相连 */
        RCCx->SYSCLKCR &= ~RCC_HXT_BYP_EN;
    }
    else
    {
        /* Enable the GPIO port */
        RCCx->SYSCLKCR &= ~RCC_HXT_PORT_EN;
    }

    /*Open the lock of register*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
}

/**
 * @brief  Set M0 IRQ Latency./设置中断延时控制
 * @param  RCCx: Selects the RCC peripheral
 * @param  IRQLATENCY: The value of IRQ LATENCY
 * @retval None
 */
void RCC_SetM0IRQLatency(RCC_TypeDef *RCCx, uint32_t IRQLATENCY)
{
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));

    /* Close the lock of register */
    RCCx->UNLOCK = RCC_REGLOCKKEY;

    /* Set value to IRQ latency register */
    RCCx->IRQLATENCY = IRQLATENCY;

    /* Open the lock of register */
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
}

/**
 * @brief  config system tick timer
 * @param  RCCx: Selects the RCC peripheral
 * @param  SKEW:
 * @param  TICKTIMERSTCALIB:
 * @retval None
 */
void RCC_SystemTickTimerConfig(RCC_TypeDef *RCCx, uint8_t SKEW, uint32_t TICKTIMERSTCALIB)
{
    /*Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));

    /*Set the value to STICKCR register*/
    RCCx->STICKCR &= ~RCC_SYSTEMTICKTIMER_STCALIB_MASK;
    RCCx->STICKCR |= (SKEW << 24);
    RCCx->STICKCR |= TICKTIMERSTCALIB;
}

/**
 * @brief  Enables system tick timer core ref clock
 * @param  RCCx: Selects the RCC peripheral
 * @param  NewState: new state of the 32k port. This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void RCC_SetSystemTickTimerCoreRefClockcmd(RCC_TypeDef *RCCx, FunctionalState NewState)
{
    /*Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));

    RCCx->STICKCR &= ~RCC_SYSTEMTICKTIMER_REFCORE_CLOCK;

    if (NewState != DISABLE)
    {
        /*set core clock */
        RCCx->STICKCR |= RCC_SYSTEMTICKTIMER_REFCORE_CLOCK;
    }
    else
    {
        /*set sclk/4*/
        RCCx->STICKCR &= ~RCC_SYSTEMTICKTIMER_REFCORE_CLOCK;
    }
}

/**
 * @brief  Enables the system clock (SYSCLK).
 * @param  RCCx: Selects the RCC peripheral
 * @param  RCC_SYSCLKSource: specifies the clock source used as system clock.
 *   This parameter can be one of the following values:
 *     @arg RCC_SYSCLKSource_HIRC:  source is hirc
 *     @arg RCC_SYSCLKSource_HXT:   source is hxt
 *     @arg RCC_SYSCLKSource_LIRC:  source is lirc
 *     @arg RCC_SYSCLKSource_LXRC:  source is lxrc
 * @param  NewState: new state of the specified peripheral clock.
 *   This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void RCC_SysclkCmd(RCC_TypeDef *RCCx, uint32_t RCC_SYSCLKSource, FunctionalState NewState)
{
    uint32_t tmpreg = 0;
    uint32_t tmpreg1 = 0;
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_SYSCLK_SOURCE(RCC_SYSCLKSource));
    tmpreg = RCCx->SYSCLKCR; // 备份系统时钟源寄存器
    tmpreg1 = RCCx->LXTCR;   // 备份外部低速晶体振荡器控制寄存器

    // set keyflag
    tmpreg &= ~RCC_CLK_KEYMASK;
    tmpreg |= RCC_CLKCONKEY;
    tmpreg1 &= ~RCC_CLK_KEYMASK;
    tmpreg1 |= RCC_CLKCONKEY;

    if (RCC_SYSCLKSource == RCC_SYSCLKSource_HIRC)
    {
        if (NewState != DISABLE)
        {
            /*RCC_SYSCLKSource value */
            tmpreg |= RCC_SYSCLKSource_HIRC_EN; // 使能内部高速时钟 HIRC 使能信号
        }
        else
        {
            tmpreg &= ~RCC_SYSCLKSource_HIRC_EN;
        }
    }
    else if (RCC_SYSCLKSource == RCC_SYSCLKSource_HXT)
    {
        if (NewState != DISABLE)
        {
            /*RCC_SYSCLKSource value */
            tmpreg |= RCC_SYSCLKSource_HXT_EN; // 使能外部高速晶体时钟
        }
        else
        {
            tmpreg &= ~RCC_SYSCLKSource_HXT_EN;
        }
    }
    else if (RCC_SYSCLKSource == RCC_SYSCLKSource_LIRC)
    {
        if (NewState != DISABLE)
        {
            /*RCC_SYSCLKSource value */
            tmpreg |= RCC_SYSCLKSource_LIRC_EN; // 使能内部低速时钟
        }
        else
        {
            tmpreg &= ~RCC_SYSCLKSource_LIRC_EN;
        }
    }
    else if (RCC_SYSCLKSource == RCC_SYSCLKSource_LXRC)
    {
        if (NewState != DISABLE)
        {
            /*RCC_SYSCLKSource value */
            tmpreg1 |= RCC_SYSCLKSource_LXRC_EN; // 使能外部低速晶体时钟
        }
        else
        {
            tmpreg1 &= ~RCC_SYSCLKSource_LXRC_EN;
        }
    }
    /*close lock*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;
    /*set clock source*/
    RCCx->SYSCLKCR = tmpreg;
    RCCx->LXTCR = tmpreg1;
    /*open lock*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
}

/**
 * @brief  Selects the system clock (SYSCLK).
 * @param  RCCx: Selects the RCC peripheral
 * @param  RCC_SYSCLKSource: specifies the clock source used as system clock.
 *  This parameter can be one of the following values:
 *     @arg RCC_SYSCLKSource_HIRC:  source is hirc
 *     @arg RCC_SYSCLKSource_HXT:   source is hxt
 *     @arg RCC_SYSCLKSource_LIRC:  source is lirc
 *     @arg RCC_SYSCLKSource_LXRC:  source is lxrc
 * @retval None
 */
void RCC_SelSysclk(RCC_TypeDef *RCCx, uint32_t RCC_SYSCLKSource)
{
    uint32_t tmpreg = 0;
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_SYSCLK_SOURCE(RCC_SYSCLKSource));
    /*RCC_SYSCLKSource value */
    tmpreg |= RCC_SYSCLKSource;
    /*set clkcon  key */
    tmpreg |= RCC_SYSCLKSEL_KEY;
    /*close lock*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;
    /*set clock source*/
    RCCx->SYSCLKSEL = tmpreg;
    /*open lock*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
}

/**
 * @brief  Configures the clock output source function
 * @param  RCCx: Selects the RCC peripheral
 * @param  RCC_SYSCLKSource: specifies the clock source used as system clock.
 *  This parameter can be one of the following values:
 *     @arg RCC_SYSCLKOSEL_HIRC:   source is HIRC
 *     @arg RCC_SYSCLKOSEL_HXT:    source is HXT
 *     @arg RCC_SYSCLKOSEL_LIRC:   source is lirc
 *     @arg RCC_SYSCLKOSEL_LXT:    source is LXT
 *     @arg RCC_SYSCLKOSEL_SYS:    source is SYSCLK
 *     @arg RCC_SYSCLKOSEL_FCLK:   source is FCLK
 * @retval None
 */
void RCC_ClkOutputSourceConfig(RCC_TypeDef *RCCx, uint32_t RCC_SYSCLKOutSource)
{
    uint32_t tmpreg = 0;
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_SYSCLKOSEL_SOURCE(RCC_SYSCLKOutSource));
    tmpreg = RCCx->MCOCR;
    /*RCC_SYSCLKSource value */
    tmpreg &= ~RCC_SYSCLKOSEL_MASK;
    tmpreg |= RCC_SYSCLKOutSource;
    /*set clock source*/
    RCCx->MCOCR = tmpreg;
}

/**
 * @brief  Enables the function of clock output
 * @param  RCCx: Selects the RCC peripheral
 * @param  NewState: new state of the Sysclk Output. This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void RCC_ClkOutputCmd(RCC_TypeDef *RCCx, FunctionalState NewState)
{
    uint32_t tmpreg = 0;
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    /*read CLKOCON */
    tmpreg = RCCx->MCOCR;
    if (NewState != DISABLE)
    {
        /*RCC_SYSCLKOUTPUT_ENABLE*/
        tmpreg |= RCC_SYSCLKOUTPUT_ENABLE;
    }
    else
    {
        /*RCC_SYSCLKOUTPUT_DISABLE*/
        tmpreg &= ~RCC_SYSCLKOUTPUT_ENABLE;
    }
    /*set clock source*/
    RCCx->MCOCR = tmpreg;
}

/**
 * @brief  Configures the fclk division frequery
 * @param  RCCx: Selects the RCC peripheral
 * @param  RCC_SYSCLKSource_DIV: Set clock division parameters
 * @retval None
 */
void RCC_ClkFclkDiv(RCC_TypeDef *RCCx, uint8_t RCC_SYSCLKSource_DIV)
{
    uint32_t tmpreg = 0;
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_FCLK_DIV(RCC_SYSCLKSource_DIV));
    /*read CLKOCON */
    tmpreg = RCCx->MCOCR;
    /*set fclk div */
    tmpreg &= ~RCC_FCLK_DIV_MASK;
    tmpreg |= RCC_SYSCLKSource_DIV;
    /*set fclk value*/
    RCCx->MCOCR = tmpreg;
}

/**
 * @brief  Returns the clock source used as system clock.
 * @param  RCCx: Selects the RCC peripheral
 * @retval the value of SYSCLKCR register
 */
uint8_t RCC_GetSysclkSource(RCC_TypeDef *RCCx)
{
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    return ((uint8_t)(RCCx->SYSCLKCR & RCC_SYSCLKSource_EN_MASK));
}

/**
  * @brief  Configures the AHB clock (HCLK).
  * @param  RCCx: Selects the RCC peripheral
  * @param  RCC_SYSCLK_DIV: defines the AHB clock divider. This clock is derived from
  *   the system clock (SYSCLK).
  *   This parameter can be one of the following values:
  *     @arg RCC_SYSCLK_Div0: AHB clock = SYSCLK
        @arg RCC_SYSCLK_Div1~255: AHB clock = SYSCLK/(2*DIV)
  *
  * @retval None
  */
void RCC_SetHclkDiv(RCC_TypeDef *RCCx, uint8_t RCC_SYSCLK_DIV)
{
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_SYSCLK_DIV(RCC_SYSCLK_DIV));
    /* Store the new value */
    RCCx->HCLKDIV = (uint32_t)RCC_SYSCLK_DIV;
}

/**
  * @brief  Configures the Low Speed APB clock (PCLK).
  * @param  RCCx: Selects the RCC peripheral
  * @param  RCC_PCLK_DIV: defines the APB clock divider. This clock is derived from
  *   the APB clock (PCLK).
  *   This parameter can be one of the following values:
        @arg RCC_AHB_Div0: APB clock = AHBCLK
        @arg RCC_AHB_Div1~255: APB clock = AHBCLK/(2*DIV)
  * @retval None
  */
void RCC_SetPclkDiv(RCC_TypeDef *RCCx, uint8_t RCC_PCLK_DIV)
{
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    /* Check the parameters */
    assert_param(IS_RCC_HCLK_DIV(RCC_PCLK_DIV));
    /* Store the new value */
    RCCx->PCLKDIV = (uint32_t)RCC_PCLK_DIV;
}

/**
  * @brief  Enables or disables the AHB peripheral clock.
  * @param  RCCx: Selects the RCC peripheral
  * @param  RCC_AHBPeriph: specifies the AHB peripheral to gates its clock.
  * this parameter can be any combination of the following values:
                @arg RCC_AHBPeriph_GPIOAEN :     Enables GPIOA
                @arg RCC_AHBPeriph_GPIOBEN :     Enables GPIOB
                @arg RCC_AHBPeriph_GPIOCEN :     Enables GPIOC
                @arg RCC_AHBPeriph_GPIODEN :     Enables GPIOD
                @arg RCC_AHBPeriph_CRCEN   :     Enables CRC
                @arg RCC_AHBPeriph_FLASHCE :     Enables FLASH
  * @param  NewState: new state of the AHB Periph clock. This parameter can be: ENABLE or DISABLE.
  * @retval None
  */
void RCC_AHBPeriphClockCmd(RCC_TypeDef *RCCx, uint32_t RCC_AHBPeriph, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_AHB_PERIPH(RCC_AHBPeriph));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        RCCx->HCLKEN |= RCC_AHBPeriph;
    }
    else
    {
        RCCx->HCLKEN &= ~RCC_AHBPeriph;
    }
}

/**
  * @brief  Enables or disables the High Speed APB (APB2) peripheral clock.
  * @param  RCCx: Selects the RCC peripheral
  * @param  RCC_APB2Periph: specifies the APB peripheral to gates its clock.
  *   This parameter can be any combination of the following values:
        @arg RCC_APBPeriph_UART0CKEN              ((uint32_t)0x00000001)
        @arg RCC_APBPeriph_UART1CKEN              ((uint32_t)0x00000002)
        @arg RCC_APBPeriph_I2CCKEN                ((uint32_t)0x00000004)
        @arg RCC_APBPeriph_LPUARTCKEN             ((uint32_t)0x00000008)
        @arg RCC_APBPeriph_SPICKEN                ((uint32_t)0x00000010)
        @arg RCC_APBPeriph_LPTIMCKEN              ((uint32_t)0x00000020)
        @arg RCC_APBPeriph_BASETIMCKEN            ((uint32_t)0x00000040)
        @arg RCC_APBPeriph_SYSCONCKEN             ((uint32_t)0x00000080)
        @arg RCC_APBPeriph_PCACKEN                ((uint32_t)0x00000100)
        @arg RCC_APBPeriph_ONEWIRECKEN            ((uint32_t)0x00000200)
        @arg RCC_APBPeriph_TIM1CKEN               ((uint32_t)0x00000400)
        @arg RCC_APBPeriph_TIM2CKEN               ((uint32_t)0x00000800)
        @arg RCC_APBPeriph_WWDTCKEN               ((uint32_t)0x00001000)
        @arg RCC_APBPeriph_ADCCKEN                ((uint32_t)0x00002000)
        @arg RCC_APBPeriph_AWKCKEN                ((uint32_t)0x00004000)
        @arg RCC_APBPeriph_RTCCKEN                ((uint32_t)0x00008000)
        @arg RCC_APBPeriph_TRIMCKEN               ((uint32_t)0x00010000)
        @arg RCC_APBPeriph_IWDTCKEN               ((uint32_t)0x00020000)
        @arg RCC_APBPeriph_LVDVCCKEN              ((uint32_t)0x00040000)
        @arg RCC_APBPeriph_BEEPCKEN               ((uint32_t)0x00080000)
        @arg RCC_APBPeriph_MCUDBGCKEN             ((uint32_t)0x00100000)
  * @param  NewState: new state of the specified peripheral clock.
  *   This parameter can be: ENABLE or DISABLE.
  * @retval None
  */
void RCC_APBPeriphClockCmd(RCC_TypeDef *RCCx, uint32_t RCC_APBPeriph, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_APB_PERIPH(RCC_APBPeriph));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        RCCx->PCLKEN |= RCC_APBPeriph;
    }
    else
    {
        RCCx->PCLKEN &= ~RCC_APBPeriph;
    }
}

/**
  * @brief  Forces or releases peripheral reset.
  * @param  RCCx: Selects the RCC peripheral
  * @param  RCC_PeriphRst: specifies the peripheral to reset.
  *   This parameter can be any combination of the following values:
       @arg RCC_APBPeriph_UART0RST               ((uint32_t)(0x01 << 0))
       @arg RCC_APBPeriph_UART1RST               ((uint32_t)(0x01 << 1))
       @arg RCC_APBPeriph_I2CRST				 ((uint32_t)(0x01 << 2))
       @arg RCC_APBPeriph_LPUARTRST 			 ((uint32_t)(0x01 << 3))
       @arg RCC_APBPeriph_SPIRST				 ((uint32_t)(0x01 << 4))
       @arg RCC_APBPeriph_LPTIMRST				 ((uint32_t)(0x01 << 5))
       @arg RCC_APBPeriph_BASETIMRST			 ((uint32_t)(0x01 << 6))
       @arg RCC_APBPeriph_SYSCONRST 			 ((uint32_t)(0x01 << 7))
       @arg RCC_APBPeriph_PCARST				 ((uint32_t)(0x01 << 8))
       @arg RCC_APBPeriph_ONEWIRERST			 ((uint32_t)(0x01 << 9))
       @arg RCC_APBPeriph_TIM1RST				 ((uint32_t)(0x01 << 10))
       @arg RCC_APBPeriph_TIM2RST				 ((uint32_t)(0x01 << 11))
       @arg RCC_APBPeriph_WWDTRST				 ((uint32_t)(0x01 << 12))
       @arg RCC_APBPeriph_ADCRST				 ((uint32_t)(0x01 << 13))
       @arg RCC_APBPeriph_AWKRST				 ((uint32_t)(0x01 << 14))
       @arg RCC_APBPeriph_TRIMRST				 ((uint32_t)(0x01 << 16))
       @arg RCC_APBPeriph_LVDVCRST				 ((uint32_t)(0x01 << 18))
       @arg RCC_APBPeriph_BEEPRST				 ((uint32_t)(0x01 << 19))
       @arg RCC_APBPeriph_MCUDBGRST 			 ((uint32_t)(0x01 << 20))
       @arg RCC_AHBPeriph_GPIOARST               ((uint32_t)(0x01 << 24))
       @arg RCC_AHBPeriph_GPIOBRST               ((uint32_t)(0x01 << 25))
       @arg RCC_AHBPeriph_GPIOCRST               ((uint32_t)(0x01 << 26))
       @arg RCC_AHBPeriph_GPIODRST               ((uint32_t)(0x01 << 27))
       @arg RCC_AHBPeriph_CRCRST                 ((uint32_t)(0x01 << 28))
  * @param  NewState: new state of the specified peripheral reset.
  *   This parameter can be: ENABLE or DISABLE.
  * @retval None
  */
void RCC_PeriphResetCmd(RCC_TypeDef *RCCx, uint32_t RCC_PeriphRst, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_PERIPHRST(RCC_PeriphRst));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    /*close lock*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;

    if (NewState != DISABLE)
    {
        RCCx->PERIRST |= RCC_PeriphRst;
    }
    else
    {
        RCCx->PERIRST &= ~RCC_PeriphRst;
    }

    /*open lock*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
}

/**
 * @brief  Forces or releases rtc peripheral reset.
 * @param  RCCx: Selects the RCC peripheral
 * @param  NewState: new state of the specified peripheral clock.
 *   This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void RCC_RTCResetCmd(RCC_TypeDef *RCCx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_PERIPHRST(RCC_RTCPeriphRST));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    /*close lock*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;

    if (NewState != DISABLE)
    {
        RCCx->RTCRST = (RCC_APBPeriph_RTCRSTKEY | RCC_RTCPeriph_RSTEABLE);
    }
    else
    {
        RCCx->RTCRST = (RCC_APBPeriph_RTCRSTKEY | RCC_RTCPeriph_RSTDISABLE);
    }

    /*open lock*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
}

/**
  * @brief  Forces or releases the Backup domain reset.
  * @param  RCCx: Selects the RCC peripheral
  * @param  RCC_RSTModle: rcc reset mode
  *   This parameter can be the following values:
       @arg RCC_MCURST       ((uint32_t)0x01)
       @arg RCC_CPURST       ((uint32_t)0x02)
  * @param  NewState: new state of the Backup domain reset.
  *   This parameter can be: ENABLE or DISABLE.
  * @retval None
  */
void RCC_ResetCmd(RCC_TypeDef *RCCx, uint32_t RCC_RSTModle, FunctionalState NewState)
{
    uint32_t RCC_RSTMODLE;
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_RST(RCC_RSTModle));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    RCC_RSTMODLE = RCC_RSTModle | RCC_RSTKEY;

    /*close lock of register*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;

    if (NewState != DISABLE)
    {
        RCCx->RSTCR |= RCC_RSTMODLE;
    }
    else
    {
        RCCx->RSTCR &= ~RCC_RSTMODLE;
    }

    /*open lock of register*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
}

/**
  * @brief  Checks whether the specified RCC flag is set or not.
  * @param  RCCx: Selects the RCC peripheral
  * @param  RCC_FLAG: specifies the flag to check.
            @arg RCC_FLAG_MCURST      ((uint32_t)0x00000001)
            @arg RCC_FLAG_CPURST      ((uint32_t)0x00000002)
            @arg RCC_FLAG_WWDTRST     ((uint32_t)0x00000004)
            @arg RCC_FLAG_IWDTRST     ((uint32_t)0x00000008)
            @arg RCC_FLAG_LVDRST      ((uint32_t)0x00000010)
            @arg RCC_FLAG_PORRST      ((uint32_t)0x00000020)
            @arg RCC_FLAG_LOCKUPRST   ((uint32_t)0x00000040)
            @arg RCC_FLAG_PADRST      ((uint32_t)0x00000080)
  * @retval The new state of RCC_FLAG (SET or RESET).
  */
FlagStatus RCC_GetFlagStatus(RCC_TypeDef *RCCx, uint32_t RCC_FLAG_RST)
{
    uint32_t tmp = 0;
    FlagStatus bitstatus = RESET;
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_RCC_FLAG_RST(RCC_FLAG_RST));

    /* Get the RCC Reset Value */
    tmp = RCCx->RSTSR;

    if ((tmp & RCC_FLAG_RST) != (uint32_t)RESET)
    {
        bitstatus = SET;
    }
    else
    {
        bitstatus = RESET;
    }
    /* Return the flag status */
    return bitstatus;
}

/**
  * @brief  Set IO MUX function
  * @param  RCCx: Selects the RCC peripheral
  * @param  NewState: new state of the SWDPORT
  *   This parameter can be: ENABLE or DISABLE.
        ENABLE  : PC7和PD1的端口功能模式为SWD端口功能
        DISABLE : 周边模块功能模式
  * @retval None
  */
void RCC_SetIoMux(RCC_TypeDef *RCCx, FunctionalState NewState)
{
    uint32_t tmpreg = 0x5a690000; // KEY
    /* Check the parameters */
    assert_param(IS_RCC_ALL_PERIPH(RCCx));
    assert_param(IS_PIN_FUNCTION(I0_MUX));
    // tmpreg |= I0_MUX;

    if (NewState != DISABLE)
    {
        tmpreg = 0x5a690001;
    }
    else
    {
        tmpreg = 0x5a690000;
    }

    /*close lock of register*/
    RCCx->UNLOCK = RCC_REGLOCKKEY;

    /*set the value to the SWDIOCR register*/
    RCCx->SWDIOCR = tmpreg;

    /*open lock of register*/
    RCCx->UNLOCK = RCC_RESGLOCKKEY;
}

uint32_t HAL_RCC_GetFclk(void)
{

    uint32_t sysclk = 0;
    uint32_t div;
    const uint32_t const ss[] = {4000000, 4000000, 8000000, 12000000, 16000000, 22120000, 24000000};
    if ((RCC->SYSCLKSEL & RCC_SYSCLKSEL_CLKSW_Msk) == RCC_SYSCLKSEL_CLKSW_HIRC)
    {
        uint32_t t = (RCC->HIRCCR >> 9) & 0x07;
        sysclk = ss[t];
    }
    else if ((RCC->SYSCLKSEL & RCC_SYSCLKSEL_CLKSW_Msk) == RCC_SYSCLKSEL_CLKSW_HXT)
    {
        sysclk = Fpclk;
    }
    else if ((RCC->SYSCLKSEL & RCC_SYSCLKSEL_CLKSW_Msk) == RCC_SYSCLKSEL_CLKSW_LIRC)
    {
        sysclk = 32768;
    }
    else if ((RCC->SYSCLKSEL & RCC_SYSCLKSEL_CLKSW_Msk) == RCC_SYSCLKSEL_CLKSW_LXT)
    {
        sysclk = 32768;
    }

    // 分频计算
    if ((RCC->HCLKDIV & RCC_HCLKDIV_AHBCKDIV_Msk) == 0x00UL)
    {
        div = 1;
    }
    else
    {
        div = 2 * (RCC->HCLKDIV & RCC_HCLKDIV_AHBCKDIV_Msk);
    }

    return sysclk / div;
}

uint32_t HAL_RCC_GetPclk(void)
{
    uint32_t pclk = 0;
    uint32_t div;

    pclk = HAL_RCC_GetFclk();

    // 分频计算
    if ((RCC->PCLKDIV & RCC_PCLKDIV_APBCKDIV_Msk) == 0x00UL)
    {
        div = 1;
    }
    else
    {
        div = 2 * (RCC->PCLKDIV & RCC_PCLKDIV_APBCKDIV_Msk);
    }

    return pclk / div;
}

/**
 *****************************
 * @brief MCU进入深度休眠
 *****************************
 */
void HAL_RCC_DeepSleep(void)
{

    SCB->SCR |= 0x04;
    __WFI();
}

/**
 *****************************
 * @brief MCU进入休眠
 *****************************
 */
void HAL_RCC_Sleep(void)
{
    SCB->SCR &= (~0x04);
    __WFI();
}

#endif
