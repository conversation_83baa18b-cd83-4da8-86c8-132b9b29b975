*** Using Compiler 'V6.18', folder: 'D:\Keil_v5\ARM\ARMCLANG\Bin'
Build target 'PY32F002Bx5_Project'
compiling lin_stack.c...
linking...
.\Objects\PY32F002B.axf: Error: L6218E: Undefined symbol UART_Send_Data (referred from lin_stack.o).
.\Objects\PY32F002B.axf: Error: L6218E: Undefined symbol clear_rx_queue (referred from lin_stack.o).
Not enough information to list image symbols.
Not enough information to list load addresses in the image map.
Finished: 2 information, 0 warning and 2 error messages.
".\Objects\PY32F002B.axf" - 2 Error(s), 0 Warning(s).
Target not created.
Build Time Elapsed:  00:00:02
