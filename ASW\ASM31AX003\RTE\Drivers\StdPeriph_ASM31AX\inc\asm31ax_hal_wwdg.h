/* Define to prevent recursive inclusion -----------*/
#ifndef __ASM31AX_WWDG_H
#define __ASM31AX_WWDG_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

    typedef struct
    {
        WWDG_TypeDef *Instance;
        uint32_t Prescaler;
        uint32_t Window;
        uint32_t ItEn;

    } WWDG_InitTypeDef;

/* --------------------- WWDG registers bit mask ------------------------ */
/* CFR register bit mask */
#define CTL_WDGTB_MASK ((uint32_t)0xF00000FF)
#define CTL_W_MASK ((uint32_t)0xFFFFFF00)
#define BIT_MASK ((uint32_t)0x000000FF)

#define WWDG_CTL_ENBLE ((uint32_t)0x10000000)  /*!< Activation bit */
#define WWDG_CRL_STATUS ((uint32_t)0x00000001) /*!< Activation bit */
#define WWDG_INT_FLAG ((uint32_t)0x00000001)   /*!< Activation bit */
#define WWDG_INT_ENABLE ((uint32_t)0x00000001) /*!< Activation bit */
#define WWDG_INT_DISABLE (0x00000000UL)
    /** @defgroup WWDG_Prescaler
     * @{
     */

#define IS_WWDG_PRESCALER(PRESCALER) (((PRESCALER) >= 0) && ((PRESCALER) <= 0xfffff))

    /* Exported functions -----------------------------*/
    void HAL_WWDG_Init(WWDG_InitTypeDef *WWDG_InitStruct);
    void HAL_WWDG_DeInit(void);
    void HAL_WWDG_SetPrescaler(uint32_t WWDG_Prescaler);
    void HAL_WWDG_SetWindowValue(uint8_t WindowValue);
    void HAL_WWDG_EnableIT(FunctionalState NewStatus);
    void HAL_WWDG_SetRldCnt(uint8_t Rldcnt);
    void HAL_WWDG_Cmd(FunctionalState NewStatus);
    FlagStatus HAL_WWDG_GetFlagStatus(uint16_t WWDG_FLAG);
    void HAL_WWDG_ClearFlag(void);

#ifdef __cplusplus
}
#endif

#endif /*__ASM31X_WWDG_H */
