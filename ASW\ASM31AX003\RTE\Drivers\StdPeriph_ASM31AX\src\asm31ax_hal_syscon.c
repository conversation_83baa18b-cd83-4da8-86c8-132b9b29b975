#include "asm31ax_hal.h"

#ifdef HAL_SYSCON_MODULE_ENABLED

/**
 * @brief  Deinitializes the SYSCTRL Functions
 *   registers to their default reset values.
 * @param  None
 * @retval None
 */
void SYSCTRL_DeInit(void)
{
    RCC_PeriphResetCmd(RCC, RCC_APBPeriph_SYSCONRST, ENABLE);
    RCC_PeriphResetCmd(RCC, RCC_APBPeriph_SYSCONRST, DISABLE);
}

/**
 * @brief  Enables M0 Lock up function
 * @param  SYSCTRLx:Select the LPUART or the SYSCON peripheral.
 * @param  NewState: new state of the LPUART mode.
 *   This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void SYSCTRL_CortexM0LockUpCmd(SYSCFG_TypeDef *SYSCTRLx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_FUNCTIONAL_STATE(NewState));
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));

    /* Close lock */
    SYSCTRLx->UNLOCK = SYSCTRL_UNLOCKKEY;

    if (NewState != DISABLE)
    {
        SYSCTRLx->CFGR0 |= SYSCTRL_LOCKUP_EN;
    }
    else
    {
        SYSCTRLx->CFGR0 &= ~SYSCTRL_LOCKUP_EN;
    }

    /* Open lock */
    SYSCTRLx->UNLOCK = SYSCTRL_LOCKKEY;
}

/**
 * @brief  Enables or disables the Deep sleep Port interrupts.
 *
 * @param  SYSCTRLx:Select the LPUART or the SYSCON peripheral.
 * @param  NewState: new state of the LPUART mode.
 *   This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void SYSCTRL_DeepsleepPortITCmd(SYSCFG_TypeDef *SYSCTRLx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_FUNCTIONAL_STATE(NewState));
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));

    if (NewState != DISABLE)
    {
        SYSCTRLx->PORTINTCR |= SYSCTRL_DEEPSLEEPPORTINT_EN;
    }
    else
    {
        SYSCTRLx->CFGR0 &= ~SYSCTRL_DEEPSLEEPPORTINT_EN;
    }
}

/**
 * @brief  Enables or disables Active Sleep Port interrupts
 * @param  SYSCTRLx:Select the LPUART or the SYSCON peripheral.
 * @param  NewState: new state of the Active Sleep Port.
 *   This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void SYSCTRL_ActiveSleepPortITCmd(SYSCFG_TypeDef *SYSCTRLx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_FUNCTIONAL_STATE(NewState));
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));

    if (NewState != DISABLE)
    {
        SYSCTRLx->PORTINTCR &= SYSCTRL_ACTIVESLEEPPORTINT_EN;
    }
    else
    {
        SYSCTRLx->CFGR0 |= ~SYSCTRL_ACTIVESLEEPPORTINT_EN;
    }
}

/**
  * @brief  Config the SPI peripheral cs singnal
  * @param  SYSCTRLx:Select the LPUART or the SYSCON peripheral.
  * @param  SYSCTRL_SPINSS_PIN : selects the GPIO port pin to be used as
            SPI peripheral cs signal
  * @retval None
  */
void SYSCTRL_SPICSConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_SPINSS_PIN)
{
    /* Check the parameters */
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));
    assert_param(IS_SPINSS_PORT_PIN(SPINSS_PIN));
    /* SET SPINSS */
    SYSCTRLx->PORTCR &= ~SPINSS_SEL_MASK;
    SYSCTRLx->PORTCR |= SYSCTRL_SPINSS_PIN;
}

/**
 * @brief  Config TIM10 GATE singnal
 * @param  SYSCTRLx:Select the LPUART or the SYSCON peripheral.
 * @param  SYSCTRL_TIM10_GATE: TIM10 GATE signal parameter
 * @retval None
 */
void SYSCTRL_TIM10_GateConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_TIM10_GATE)
{
    /* Check the parameters */
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));
    assert_param(IS_TIM0_GATE_SEL(SYSCTRL_TIM0_GATE));

    /* Configure TIM0 GATE selection PIN */
    SYSCTRLx->PORTCR &= ~TIM10_GATE_MASK;
    SYSCTRLx->PORTCR |= SYSCTRL_TIM10_GATE;
}

/**
 * @brief  Config TIM11 GATE singnal
 * @param  SYSCTRLx:Select the LPUART or the SYSCON peripheral.
 * @param  SYSCTRL_TIM10_GATE: TIM11 GATE signal parameter
 * @retval None
 */
void SYSCTRL_TIM11_GateConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_TIM11_GATE)
{
    /* Check the parameters */
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));
    assert_param(IS_TIM1_GATE_SEL(SYSCTRL_TIM11_GATE));

    /* Configure TIM1 GATE selection PIN */
    SYSCTRLx->PORTCR &= ~TIM11_GATE_MASK;
    SYSCTRLx->PORTCR |= SYSCTRL_TIM11_GATE;
}

/**
 * @brief  Config LPTIM GATE singnal
 * @param  SYSCTRLx:Select the LPUART or the SYSCON peripheral.
 * @param  SYSCTRL_IPTIM_GATE: LPTIM GATE signal parameter
 * @retval None
 */
void SYSCTRL_IPTIM_GateConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_IPTIM_GATE)
{
    /* Check the parameters */
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));
    assert_param(IS_IPTIM_GATE_SEL(SYSCTRL_IPTIM_GATE));

    /* Configure IPTIM GATE selection PIN */
    SYSCTRLx->PORTCR &= ~IPTIM_GATE_MASK;
    SYSCTRLx->PORTCR |= SYSCTRL_IPTIM_GATE;
}

/**
 * @brief  Config PCA Capture mux config
 * @param  SYSCTRLx:Select the LPUART or the SYSCON peripheral.
 * @param  SYSCTRL_CAP_CHANNEL: PCA Capture channel
 * @param  SYSCTRL_CAP_SINGNAL: PCA Capture singnal
 * @retval None
 */
void SYSCTRL_PCACapMuxConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_CAP_CHANNEL, uint32_t SYSCTRL_CAP_SINGNAL)
{
    /* Check the parameters */
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));
    assert_param(IS_PCA_CHANNEL_SET(SYSCTRL_CAP_CHANNEL));
    assert_param(IS_CAP_CHANNEL_SEL(SYSCTRL_CAP_SINGNAL));

    /* set PCA CAPMUX */
    SYSCTRLx->PCACR &= ~(uint32_t)(PCA_CH_MASK << (SYSCTRL_CAP_CHANNEL * 2));
    SYSCTRLx->PCACR |= (SYSCTRL_CAP_SINGNAL << (SYSCTRL_CAP_CHANNEL * 2));
}

/**
 * @brief  Config tim1 input signal
 * @param  SYSCTRLx:Select the LPUART or the SYSCON peripheral.
 * @param  SYSCTRL_INPUT_CHANNEL: SYSCTRL INPUT CHANNEL PARAMETER
 *   This parameter can be one of the following values:
 *       @args TIM_CHANNEL1
 *       @args TIM_CHANNEL2
 *       @args TIM_CHANNEL3
 *       @args TIM_CHANNEL4
 * @param  SYSCTRL_INPUT_SINGNAL: SYSCTRL INPUT SINGNAL PARAMETER
 *   This parameter can be one of the following values:
 *       @args TIM_CH
 *       @args TIM_UART0RXD
 *       @args TIM_UART1RXD
 *       @args TIM_LPUARTRXD
 *       @args TIM_LIRC
 * @retval None
 */
void SYSCTRL_TIM1_InputSignalConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_INPUT_CHANNEL, uint32_t SYSCTRL_INPUT_SINGNAL)
{
    /* Check the parameters */
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));
    assert_param(IS_TIM_CHANNEL_SET(SYSCTRL_INPUT_CHANNEL));
    assert_param(IS_TIM_INPUTSIGNAL_SEL(SYSCTRL_INPUT_SINGNAL));

    /* Set TIM1 CHINMUX */
    SYSCTRLx->TIM1CR &= ~(uint32_t)(TIM_CH_MASK << (SYSCTRL_INPUT_CHANNEL * 4));
    SYSCTRLx->TIM1CR |= (SYSCTRL_INPUT_SINGNAL << (SYSCTRL_INPUT_CHANNEL * 4));
}

/**
 * @brief  Config tim1 ETR signal
 * @param  SYSCTRLx:Select the LPUART or the SYSCON peripheral.
 * @param  SYSCTRL_INPUT_SINGNAL: SYSCTRL INPUT SINGNAL PARAMETER
 * @retval None
 */
void SYSCTRL_TIM1_ETRSignalConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_INPUT_SINGNAL)
{
    /* Check the parameters */
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));
    assert_param(IS_TIMETR_ETR_SET(SYSCTRL_INPUT_SINGNAL));

    /* Set TIM1 CHINMUX */
    SYSCTRLx->TIM1CR &= ~TIM_ETR_MASK;
    SYSCTRLx->TIM1CR |= SYSCTRL_INPUT_SINGNAL;
}

/**
 * @brief  Config tim1 Break Stm
 * @param  SYSCTRLx:Select the LPUART or the SYSCON peripheral.
 * @retval None
 */
void SYSCTRL_TIM1_SetBreakSTM(SYSCFG_TypeDef *SYSCTRLx)
{
    /*Check the parameters */
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));
    /* Set TIM1 CHINMUX */
    SYSCTRLx->TIM1CR &= TIM1_BREAK_ZERO;
    SYSCTRLx->TIM1CR |= TIM1_BREAK_STM;
}

/**
 * @brief  Config Break Zero
 * @param  SYSCTRLx:Select the SYSCON peripheral.
 * @retval None
 */
void SYSCTRL_TIM1_SetBreakZERO(SYSCFG_TypeDef *SYSCTRLx)
{
    /*Check the parameters */
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));
    /* Set TIM1 CHINMUX */
    SYSCTRLx->TIM1CR &= TIM1_BREAK_ZERO;
}

/**
 * @brief  Config tim2 input signal
 * @param  SYSCTRLx:Select the LPUART or the SYSCON peripheral.
 * @param  SYSCTRL_INPUT_CHANNEL: SYSCTRL INPUT CHANNEL PARAMETER
 * @param  SYSCTRL_INPUT_SINGNAL: SYSCTRL INPUT SINGNAL PARAMETER
 * @retval None
 */
void SYSCTRL_TIM2_InputSignalConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_INPUT_CHANNEL, uint32_t SYSCTRL_INPUT_SINGNAL)
{
    /* Check the parameters */
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));
    assert_param(IS_TIM_CHANNEL_SET(SYSCTRL_INPUT_CHANNEL));
    assert_param(IS_TIM_INPUTSIGNAL_SEL(SYSCTRL_INPUT_SINGNAL));

    /* Set TIM1 CHINMUX */
    SYSCTRLx->TIM2CR &= ~(uint32_t)(TIM_CH_MASK << (SYSCTRL_INPUT_CHANNEL * 4));
    SYSCTRLx->TIM2CR |= (SYSCTRL_INPUT_SINGNAL << (SYSCTRL_INPUT_CHANNEL * 4));
}

/**
 * @brief  Config tim2 ETR signal
 * @param  SYSCTRLx:Select the SYSCON peripheral.
 * @param  SYSCTRL_INPUT_SINGNAL: SYSCTRL INPUT SINGNAL PARAMETER
 * @retval None
 */
void SYSCTRL_TIM2_ETRSignalConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_INPUT_SINGNAL)
{
    /* Check the parameters */
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));
    assert_param(IS_TIMETR_ETR_SET(SYSCTRL_INPUT_SINGNAL));

    /* Set TIM1 CHINMUX */
    SYSCTRLx->TIM2CR &= ~TIM_ETR_MASK;
    SYSCTRLx->TIM2CR |= SYSCTRL_INPUT_SINGNAL;
}

/**
 * @brief  Config Lpuart clock
 * @param  SYSCTRLx:Select the SYSCON peripheral.
 * @param  SYSCTRL_LPUART_CLK: Config lpuart clock
 * @retval None
 */
void SYSCTRL_LPUART_CLKConfig(SYSCFG_TypeDef *SYSCTRLx, uint32_t SYSCTRL_LPUART_CLK)
{
    uint32_t tmpreg = 0;
    /* Check the parameters */
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));
    assert_param(IS_LPUART_CLK_SEL(SYSCTRL_LPUART_CLK));

    /*Close lock */
    SYSCTRLx->UNLOCK = SYSCTRL_UNLOCKKEY;

    tmpreg = (((SYSCTRLx->LPUARTCON) & SYSCTRL_LPUARTKEY_MASK) | SYSCTRL_LPUARTKEY);
    tmpreg = ((tmpreg & (~LPUARTCLK_MASK)) | SYSCTRL_LPUART_CLK);
    /* Set TIM1 CHINMUX */
    SYSCTRLx->LPUARTCON = tmpreg;

    /*open lock */
    SYSCTRLx->UNLOCK = SYSCTRL_LOCKKEY;
}

/**
 * @brief  Enables SYSCTRL LPUART Clock
 * @param  SYSCTRLx:Select the LPUART or the SYSCON peripheral.
 * @param  NewState: new state of the LPUARTx peripheral.
 *         This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void SYSCTRL_LPUART_CLKCmd(SYSCFG_TypeDef *SYSCTRLx, FunctionalState NewState)
{
    uint32_t tmpreg = 0;
    /*Check the parameters */
    assert_param(IS_SYSCTRL_ALL_PERIPH(SYSCTRLx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    /*Close lock */
    SYSCTRLx->UNLOCK = SYSCTRL_UNLOCKKEY;

    tmpreg = (((SYSCTRLx->LPUARTCON) & SYSCTRL_LPUARTKEY_MASK) | SYSCTRL_LPUARTKEY);

    /* Set TIM1 CHINMUX */
    if (NewState != DISABLE)
    {
        tmpreg |= (uint32_t)LPUARTCLK_EN;
        SYSCTRLx->LPUARTCON = tmpreg;
    }
    else
    {
        tmpreg &= ~(uint32_t)LPUARTCLK_EN;
        SYSCTRLx->LPUARTCON = tmpreg;
    }
    /*open lock */
    SYSCTRLx->UNLOCK = SYSCTRL_LOCKKEY;
}

#endif
