/*
  Copyright (C), 2018-2019, VCAE. Co., Ltd.
  FileName:    tuning.c
  Description:  ����Ӳ���͹������Ե�һЩ����
  Version: 0.1
  Function List:
    1. gpio_init
    2.
  History:
      <author>   <time>      <version >                <desc>
        benny      19/11/1        0.1                  �´���ģ��
*/

/**********************************************************************************************
 * External objects
 **********************************************************************************************/
#include <stdio.h>

#include "config.h"
#include "drv.h"

#include "tuning.h"
#include "db.h"
#include "pid.h"

#ifdef LIN_ENABLE
#include "lin_stack.h"
#endif

extern uint8_t log_on;
extern uint8_t is_running_data_output;

#if defined(ASW_ENABLED)
extern uint8_t sensor_calibration_check(uint8_t *log_buffer);
extern uint8_t sensor_calibration(uint8_t *cmd, uint8_t *log_buffer);
#endif

extern void DB_Init(void);

/**********************************************************************************************
 * Global variables
 **********************************************************************************************/
typedef struct
{
    uint8_t als;
    uint8_t v12;
    uint8_t iec;
    uint8_t oec;
    uint16_t temp;
} IEMDiag_TestData_t;

IEMDiag_TestData_t IEMDiag_TestData;

uint8_t iec_pwm;

/**********************************************************************************************
 * Constants and macros
 **********************************************************************************************/
#define CMD_BUFFER_SIZE 30

extern uint8_t code *sw_version;

extern uint8_t sensor_get_hw_data(uint8_t *buffer);
// const pwm_ch_t ch_list[] =
//     {
//         PWM_CH_NONE, PWM_CH_IEC, PWM_CH_OEC
//     };

/**********************************************************************************************
 * Local types
 **********************************************************************************************/

/**********************************************************************************************
 * Local function prototypes
 *********************************************************************************************/

/**********************************************************************************************
 * Local variables
 **********************************************************************************************/
// static  uint8_t cmd_buffer[CMD_BUFFER_SIZE];
/**********************************************************************************************
 * Local functions
 **********************************************************************************************/

/**********************************************************************************************
 * Global functions
 **********************************************************************************************/
uint16_t get_als_value(void)
{
    uint16_t value;

    if (IEMDiag_TestData.als)
    {
        value = IEMDiag_TestData.als;
    }
    else
    {
        value = adc_get_front();

        /*������10bit,�����Ƶ�250*/
        if (db_main.als_bits == 0)
            db_main.als_bits = 8;
        value = value >> (12 - db_main.als_bits);
        if (value > 250)
        {
            value = 250;
        }
    }
    return value;
}

uint16_t get_temp_value(void)
{
    uint16_t value;
    static uint16_t temp_value;

    if (IEMDiag_TestData.temp)
    {
        value = IEMDiag_TestData.temp;
    }
    else
    {
        value = adc_get_ntc();
    }

    temp_value = Filter_LowPass(temp_value, value, 5);

    return temp_value;
}

uint16_t GetIEC(void)
{
    uint16_t iec;

    if (IEMDiag_TestData.iec)
    {
        iec = (uint16_t)IEMDiag_TestData.iec;
    }
    else
    {
        iec = (adc_get_ec_int() - adc_get_ec_ext()) * 49 / 404;
    }
    return iec;
}

uint16_t GetOEC(void)
{
    uint16_t oec;

    if (IEMDiag_TestData.oec)
    {
        oec = (uint16_t)IEMDiag_TestData.oec;
    }
    else
    {
        oec = adc_get_ec_ext() * 49 / 404;
    }
    return oec;
}

uint16_t GetV12(void)
{
    uint16_t v12;

    if (IEMDiag_TestData.v12)
    {
        v12 = (uint16_t)IEMDiag_TestData.v12;
    }
    else
    {
        v12 = adc_get_ign() * 8 / 100;
    }
    return v12;
}

/**
 * @brief    tat�����
 * @param  None
 * @retval   None
 */

#if (MCU == ARM)
static uint8_t __attribute__((aligned(4))) buffer[128];
#else
static uint8_t buffer[128];
#endif
static uint8_t checksum;
void sendmsg2host(uint8_t msg_type, uint8_t *buf, uint8_t len)
{
    uint8_t i, send_buf[25];

    // add msg to send buf
    checksum = 0;

    // ��ʼ��
    send_buf[0] = 0xA5;

    // ����
    send_buf[1] = (len + 1);

    // ����
    send_buf[2] = msg_type;
    for (i = 0; i < len; i++)
        send_buf[3 + i] = buf[i];

    for (i = 0; i < len + 3; i++)
        checksum ^= send_buf[i];

    // checksum
    send_buf[len + 3] = checksum;

    // ������
    send_buf[len + 4] = 0x5A;

    uart_tat_send(send_buf, len + 5);
}

void UART_TAT_RCV_func(void)
{
    uint8_t *cmd, length;

    cmd = uart_tat_get_buffer(&length);

#ifdef LIN_ENABLE
    if (length > 0)
    {
        if (length == 1)
        {
            // 单字节PID请求，调用lin_stack_single_rx处理
            lin_stack_single_rx(cmd[0]);
        }
        else
        {
            // 多字节数据帧，调用lin_stack_recv处理
            lin_stack_recv(cmd, length);
        }
    }
#endif

    if (cmd[0] == 0xA5 && cmd[length - 1] == 0x5A)
    {
        // checksum
        // uint8_t i;

        // checksum = 0;
        // for (i = 0; i < length - 2; i++)
        // {
        //     checksum ^= cmd[i];
        // }
        // if (checksum != cmd[length - 2])
        // {
        //     return;
        // }

        if (cmd[2] == CMD_GET_HW_DATA)
        {
            length = sensor_get_hw_data(buffer);
            sendmsg2host(cmd[2], buffer, length);
        }
        else if (cmd[2] == CMD_GET_SW_VERSION)
        {
            length = strlen((const char *)sw_version);
            memcpy(buffer, sw_version, length);
            sendmsg2host(cmd[2], buffer, length);
        }
        else if (cmd[2] == CMD_GET_UID)
        {
            length = nvm_readchipid(buffer);
            sendmsg2host(cmd[2], buffer, length);
        }
        else if (cmd[2] == CMD_SET_DARK_CALI)
        {
            uint16_t value;

            value = get_als_value();
            if (value < cmd[4] || value > 20)
                db_main.cali.F_dark = 0;
            else
                db_main.cali.F_dark = value - cmd[4];

            value = adc_get_back();
            if (value < cmd[6] || value > 20)
                db_main.cali.B_dark = 0;
            else
                db_main.cali.B_dark = value - cmd[6];

            buffer[0] = 0;
            buffer[1] = db_main.cali.F_dark;
            buffer[2] = 0;
            buffer[3] = db_main.cali.B_dark;
            db_main.cali.dark_count++;
            buffer[4] = db_main.cali.dark_count / 256;
            buffer[5] = db_main.cali.dark_count & 0xFF;
            sendmsg2host(cmd[2], buffer, 6);
            DB_Save(DB_MAIN, &db_main);
            DB_Save(DB_MIRROR, &db_main);
        }
    }

    switch (cmd[PROT_CMD])
    {
    case CMD_GET_ACK:
        printf("OK");
        break;
    case CMD_DATA_OUTPUT_LOG:
        is_running_data_output = cmd[1];
        if (is_running_data_output == 0)
            Log_Enabled = 0;
        break;
    case CMD_GET_MP_VCAE_VERSION:
        printf("%s", sw_version);
        break;

    case CMD_GET_CHIP_ID:
    {
        uint8_t id[15], length;
        length = nvm_readchipid(id);
        id[length] = 0;
        uart_tat_send(id, length);
    }
    break;

    case CMD_GET_CUSTOM_SW_VERSION:
        printf("%s", CUSTOM_VERSION);
        break;

#if defined(ASW_ENABLED)
    case CMD_CALI_CHECK:
        length = sensor_calibration_check(buffer);
        uart_tat_send(buffer, length);
        break;
    case CMD_CALI:
        length = sensor_calibration(cmd, buffer);
        uart_tat_send(buffer, length);
        break;
#endif

    case CMD_LOG:
        if (log_on)
        {
            log_on = 0;
        }
        else
        {
            log_on = 1;
        }

        break;

    case CMD_WDOG_RESET:
        while (1)
            ;

    case CMD_NVM:
    {
        uint8_t count, i;
        uint16_t addr;

        addr = cmd[PROT_PARAM + 1];
        addr = addr << 8;
        addr += cmd[PROT_PARAM + 2];

        switch (cmd[PROT_PARAM])
        {
        case 1: // read
            count = cmd[PROT_PARAM + 3];
            if (count > 20)
            {
                count = 20;
            }

            nvm_read(addr, buffer, count);
            uart_tat_send(buffer, count);

            break;
        case 2: // write
            count = cmd[PROT_PARAM + 3];
            if (addr + count > 128)
                break;
            // nvm_read(0, buffer, 128);
            for (i = 0; i < count; i++)
            {
                buffer[addr + i] = cmd[5 + i];
            }
            // memcpy(buffer, cmd + 5, count);
            nvm_erase(0);
            nvm_write(0, buffer, 128);
            if (addr == 112)
                DB_Init();

            break;
        case 3: // erase
            nvm_read(0, buffer, 128);
            nvm_erase(addr);
            break;
        }
    }
    case CMD_PWM:
    {
        iec_pwm = cmd[PROT_PARAM];
        pwm_set_duty(PWM_CH_IEC, iec_pwm);
    }
    break;
    case CMD_DIAG:
    {
        uint8_t offset, *p;

        offset = cmd[PROT_PARAM];
#if (MCU == ARM)
        if (offset == 4)
            offset = 5;
        else if (offset == 5)
            offset = 4;
#endif
        if (offset < sizeof(IEMDiag_TestData))
        {
            p = (uint8_t *)&IEMDiag_TestData;
            *(p + offset) = cmd[PROT_PARAM + 1];
        }
    }
    break;
    }

    uart_tat_rcv_buffer_clear();
}
