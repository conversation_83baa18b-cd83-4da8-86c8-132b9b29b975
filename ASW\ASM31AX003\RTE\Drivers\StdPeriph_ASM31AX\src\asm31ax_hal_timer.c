#include "asm31ax_hal.h"

#ifdef HAL_TIMER_MODULE_ENABLED

uint8_t basetimer0_flg = 0;
uint8_t basetimer1_flg = 0;

static void HAL_TIM_GPIO_Init(TIM_TimeBaseInitTypeDef *TIM_TimeBaseInitStruct)
{
    GPIO_InitTypeDef GPIO_InitStruct;
    if (TIM_TimeBaseInitStruct->TogPin != NOPIN)
    {
        const GPIO_UnitTypeDef *toggpio = HAL_GPIO_Get_Pin(TIM_TimeBaseInitStruct->TogPin);
        if (TIM_TimeBaseInitStruct->TogPin == PD4)
        {
            HAL_GPIO_PinAFConfig1(toggpio->Gpio, toggpio->Pin, 4);
        }
        else
        {
            HAL_GPIO_PinAFConfig1(toggpio->Gpio, toggpio->Pin, 6);
        }
    }

    if (TIM_TimeBaseInitStruct->TogNPin != NOPIN)
    {
        const GPIO_UnitTypeDef *togngpio = HAL_GPIO_Get_Pin(TIM_TimeBaseInitStruct->TogNPin);
        HAL_GPIO_PinAFConfig1(togngpio->Gpio, togngpio->Pin, 6);
    }

    if (TIM_TimeBaseInitStruct->TogPin != NOPIN)
    {
        const GPIO_UnitTypeDef *extgpio = HAL_GPIO_Get_Pin(TIM_TimeBaseInitStruct->ExtPin);
        HAL_GPIO_PinAFConfig1(extgpio->Gpio, extgpio->Pin, 6);
    }

    if (TIM_TimeBaseInitStruct->GatePin != NOPIN)
    {
        const GPIO_UnitTypeDef *gategpio = HAL_GPIO_Get_Pin(TIM_TimeBaseInitStruct->GatePin);
        if (TIM_TimeBaseInitStruct->GatePin == PB4)
        {
            HAL_GPIO_PinAFConfig1(gategpio->Gpio, gategpio->Pin, 1);
        }
        else
        {
            HAL_GPIO_PinAFConfig1(gategpio->Gpio, gategpio->Pin, 6);
        }
    }
}

/**
 * @brief  Deinitializes the TIMx peripheral registers to their default reset values.
 * @param  TIMx: Select the SPI peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @retval None
 */
void HAL_BASETIM_DeInit(BASETIM_TypeDef *TIMx)
{
    /* Check the parameters */
    assert_param(IS_BASETIM_ALL_PERIPH(TIMx));

    if ((TIMx == TIM10) || (TIMx == TIM11))
    {
        /*Enable SPI reset state */
        RCC_PeriphResetCmd(RCC, RCC_APBPeriph_BASETIMRST, ENABLE);
        /*Release SPI from reset state */
        RCC_PeriphResetCmd(RCC, RCC_APBPeriph_BASETIMRST, DISABLE);
    }
}

/**
 * @brief  Initializes the TIMx Time Base Unit peripheral according to
 *         the specified parameters in the TIM_TimeBaseInitStruct.
 * @param  TIMx: Select the TIM peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @param  TIM_TimeBaseInitStruct: pointer to a TIM_TimeBaseInitTypeDef
 *         structure that contains the configuration information for the
 *         specified TIM peripheral.
 * @retval None
 */
void HAL_BASETIM_Init(TIM_TimeBaseInitTypeDef *TIM_TimeBaseInitStruct)
{
    uint32_t tmp = 0;
    /* Check the parameters */
    assert_param(IS_TIM_ALL_PERIPH(TIMx));
    assert_param(IS_TIM_COUNTER_MODE(TIM_TimeBaseInitStruct->WorkMode));
    assert_param(IS_TIM_CLOCK_DIV(TIM_TimeBaseInitStruct->ClockDivision));
    // if (TIM_TimeBaseInitStruct->TogEn || TIM_TimeBaseInitStruct->GateEn)
    // {
    //     HAL_TIM_GPIO_Init(TIM_TimeBaseInitStruct);
    // }

    /*read timer con value*/
    tmp = TIM_TimeBaseInitStruct->Instance->CR;

    /*Control register value MASK Initialization*/
    tmp = tmp & TMRxCON_MASK;

    /*Control register value Initialization*/
    tmp |= (TIM_TimeBaseInitStruct->GatePolarity) |
           (TIM_TimeBaseInitStruct->GateEn) |
           (TIM_TimeBaseInitStruct->CountSize) |
           (TIM_TimeBaseInitStruct->CountMode) |
           (TIM_TimeBaseInitStruct->WorkMode) |
           (TIM_TimeBaseInitStruct->OneShot) |
           (TIM_TimeBaseInitStruct->TogEn) |
           (TIM_TimeBaseInitStruct->ClockDivision);

    /* Generate an update event to reload the Prescaler and the Repetition counter
    values immediately */
    TIM_TimeBaseInitStruct->Instance->CR = tmp;

    /*Set the value to the Load register*/
    TIM_TimeBaseInitStruct->Instance->LOAD = TIM_TimeBaseInitStruct->Period;

    /*Set the value to the BGLoad register*/
    TIM_TimeBaseInitStruct->Instance->BGLOAD = TIM_TimeBaseInitStruct->Period;
}

/**
 * @brief  Fills each TIM_TimeBaseInitStruct member with its default value.
 * @param  TIM_TimeBaseInitStruct : pointer to a TIM_TimeBaseInitTypeDef structure which will be initialized.
 * @retval None
 */
void HAL_BASETIM_StructInit(TIM_TimeBaseInitTypeDef *TIM_TimeBaseInitStruct)
{
    /*--------------- ResetTIM init structure parameters values --------------*/
    /* initialize theTIM_ClockDivision */
    TIM_TimeBaseInitStruct->ClockDivision = TIM_CLOCK_DIV0;
    /* Initialize the WorkMode */
    TIM_TimeBaseInitStruct->WorkMode = TIM_WORKMODE_COUNTER;
    /* Initialize the >GateEn */
    TIM_TimeBaseInitStruct->GateEn = TIM_GATE_DISABLE;
    /* Initialize the GatePolarity */
    TIM_TimeBaseInitStruct->GatePolarity = TIM_GATE_POLARITY_LOW;
    /* Initialize the CountMode*/
    TIM_TimeBaseInitStruct->CountMode = TIM_COUNT_MODE_PERIODIC;
    /* Initialize the OneShot */
    TIM_TimeBaseInitStruct->OneShot = TIM_ONESHOT_ENABLE;
    /* Initialize the CountSize */
    TIM_TimeBaseInitStruct->CountSize = TIM_COUNT_SIZE_32BIT;

    TIM_TimeBaseInitStruct->TogEn = TIM_TOG_DISABLE;
}

/**
 * @brief  Enables or disables the specified TIM peripheral TR function.
 * @param  TIMx: Select the TIM peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @param  NewState: new state of the TIMx peripheral.
 *   This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_BASETIM_Cmd(BASETIM_TypeDef *TIMx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_BASETIM_ALL_PERIPH(TIMx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*Enable the TIM Counter */
        TIMx->CR |= TIM_TR_ENABLE;
    }
    else
    {
        /*Disable the TIM Counter */
        TIMx->CR &= ~TIM_TR_ENABLE;
    }
}

/**
 * @brief  Enables or disables the specified TIM peripheral TOG function.
 * @param  TIMx: Select the TIM peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @param  NewState: new state of the TIMx peripheral.
 * This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_BASETIM_TogCmd(BASETIM_TypeDef *TIMx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_BASETIM_ALL_PERIPH(TIMx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*Enable the TIM TOG Function*/
        TIMx->CR |= TIM_TOG_ENABLE;
    }
    else
    {
        /*Disable the TIM TOG Function */
        TIMx->CR &= ~TIM_TOG_ENABLE;
    }
}

/**
 * @brief  Enables or disables the specified TIM interrupts.
 * @param  TIMx: Select the TIM peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @param  NewState: specifies the TIM interrupts sources to be enabled or disabled.
 */
void HAL_BASETIM_ITConfig(BASETIM_TypeDef *TIMx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_BASETIM_ALL_PERIPH(TIMx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /* Enable the Interrupt sources */
        TIMx->CR |= TIM_IT_ENABLE;
    }
    else
    {
        /* Disable the Interrupt sources */
        TIMx->CR &= ~TIM_IT_ENABLE;
    }
}

/**
 * @brief  Config TIM Counter the specified Mode/配置TIMx为定时模式还是计数模式
 * @param  TIMx: Select the TIM peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @param  Mode: specifies the Counter Mode or Timer Mode.
 * This parameter can be one of the following values:
 *		@arg TIM_WORKMODE_TIMER
 *		@arg TIM_WORKMODE_COUNTER
 * @retval None
 */
void HAL_BASETIM_CounterModeConfig(BASETIM_TypeDef *TIMx, uint32_t Mode)
{
    /* Check the parameters */
    assert_param(IS_BASETIM_ALL_PERIPH(TIMx));

    /*Clear function bit to zero*/
    TIMx->CR &= ~TIM_WORKMODE_COUNTER;

    /*Set the specifies the Mode*/
    TIMx->CR |= Mode;
}

/**
 * @brief  Select  TIMX Gate Polarity
 * @param  TIMx: Select the TIM peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @param  TIM_GatePolarity: The Input Gate Polarity .
 * This parameter can be one of the following values:
 *     @arg TIM_GATE_POLARITY_HIGH
 *     @arg TIM_GATE_POLARITY_LOW
 * @retval None
 */
void HAL_BASETIM_SelectGatePolarity(BASETIM_TypeDef *TIMx, uint32_t TIM_GatePolarity)
{
    /*Check the parameters */
    assert_param(IS_BASETIM_ALL_PERIPH(TIMx));

    /*Clear function bit to zero*/
    TIMx->CR &= ~TIM_GATE_POLARITY_LOW;

    /*Set the specifies the Gate polarity*/
    TIMx->CR |= TIM_GatePolarity;
}

/**
 * @brief  Sets the TIMx Clock Division value.
 * @param TIMx: Select the TIM peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @param  TIM_DIV: specifies the clock division value.
 * This parameter can be one of the following value:
 *     @arg TIM_CLOCK_DIV0:   TIM_DIV = Tck_tim
 *     @arg TIM_CLOCK_DIV2:   TIM_DIV = Tck_tim/2
 *     @arg TIM_CLOCK_DIV4:   TIM_DIV = Tck_tim/4
 *     @arg TIM_CLOCK_DIV8:   TIM_DIV = Tck_tim/8
 *     @arg TIM_CLOCK_DIV16:  TIM_DIV = Tck_tim/16
 *     @arg TIM_CLOCK_DIV32:  TIM_DIV = Tck_tim/32
 *     @arg TIM_CLOCK_DIV64:  TIM_DIV = Tck_tim/64
 *     @arg TIM_CLOCK_DIV128: TIM_DIV = Tck_tim/128
 * @retval None
 */
void HAL_BASETIM_SetClockDivision(BASETIM_TypeDef *TIMx, uint32_t TIM_DIV)
{
    /* Check the parameters */
    assert_param(IS_BASETIM_ALL_PERIPH(TIMx));
    assert_param(IS_TIM_CLOCK_DIV(TIM_CKD));
    /* Reset the TIM DIV Bits */
    TIMx->CR &= ~(uint32_t)TIM_CLOCK_DIV128;
    /* Set the TIM DIV value */
    TIMx->CR |= (uint32_t)TIM_DIV;
}

/**
 * @brief Gets the TIMx Prescaler value.
 * @param TIMx: Select the TIM peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @retval Prescaler Register value.
 */
uint16_t HAL_BASETIM_GetPrescaler(BASETIM_TypeDef *TIMx)
{
    /* Check the parameters */
    assert_param(IS_BASETIM_ALL_PERIPH(TIMx));
    /* Get the Prescaler Register value */
    return (uint16_t)(TIMx->CR & 0x07);
}

/**
 * @brief  Set Timer Load Register value, Load Register and BGLoad Register value
 * @param  TIMx: Select the TIM peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @param  TIM_Value: Set to the LOAD/BGLOAD register of value
 * @retval None.
 */
void HAL_BASETIM_SetTimerLoadRegister(BASETIM_TypeDef *TIMx, uint32_t TIM_Value)
{
    /* Check the parameters */
    assert_param(IS_BASETIM_ALL_PERIPH(TIMx));

    /*Set the value to the Load register*/
    TIMx->LOAD = (uint32_t)TIM_Value;

    /*Set the value to the BGLoad register*/
    TIMx->BGLOAD = (uint32_t)TIM_Value;
}

/**
 * @brief  Gets the Timer Load Register's value
 * @param  TIMx: Select the TIM peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @retval Counter Register value.
 */
uint32_t HAL_BASETIM_GetTimerLoadRegister(BASETIM_TypeDef *TIMx)
{
    /* Check the parameters */
    assert_param(IS_BASETIM_ALL_PERIPH(TIMx));

    /* Get the Timer Load Register value */
    return (TIMx->LOAD);
}

/**
 * @brief  Gets the Timer count Register's value
 * @param  TIMx: Select the TIM peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @retval Counter Register value.
 */
uint32_t HAL_BASETIM_GetCountRegister(BASETIM_TypeDef *TIMx)
{
    /* Check the parameters */
    assert_param(IS_BASETIM_ALL_PERIPH(TIMx));

    /* Get the Counter Register value */
    return (uint32_t)(TIMx->CNT);
}

/**
 * @brief  Gets ITSource Flag Status
 * @param  TIMx: Select the TIM peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @param  TIMx: IT Flag Status.
 * This parameter can be one of the following values:
 *     @arg TIM_IT_FLAG
 * @retval  The new state of TIM_ITFLAG (SET or RESET).
 */
ITStatus HAL_BASETIM_GetITSourceFlagStatus(BASETIM_TypeDef *TIMx, uint32_t TIM_ITFLAG)
{
    ITStatus bitstatus = RESET;

    /* Check the parameters */
    assert_param(IS_BASETIM_ALL_PERIPH(TIMx));

    if ((TIMx->RIS & TIM_ITFLAG) != (uint16_t)RESET)
    {
        bitstatus = SET;
    }
    else
    {
        bitstatus = RESET;
    }

    return bitstatus;
}

/**
 * @brief  Gets IT Flag shield Status
 * @param  TIMx: Select the TIM peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @param  TIMx: IT Flag Status.
 * This parameter can be one of the following values:
 *     @arg TIM_IT_FLAG
 * @retval The new state of TIM_ITFLAG (SET or RESET).
 */
ITStatus HAL_BASETIM_GetITShieldFlagStatus(BASETIM_TypeDef *TIMx, uint32_t TIM_ITFLAG)
{
    ITStatus bitstatus = RESET;

    /* Check the parameters */
    assert_param(IS_BASETIM_ALL_PERIPH(TIMx));

    if ((TIMx->MIS & TIM_ITFLAG) != (uint16_t)RESET)
    {
        bitstatus = SET;
    }
    else
    {
        bitstatus = RESET;
    }

    return bitstatus;
}

/**
 * @brief  Clear the IT Flag
 * @param  TIMx: Select the TIM peripheral
 * This parameter can be one of the following values: TMER10, TMER11.
 * @param  TIM_ITFLAG: The state of TIM_ITFLAG
 * This parameter can be one of the following values:
 *     @arg TIM_IT_FLAG
 */
void HAL_BASETIM_ClearITFlag(BASETIM_TypeDef *TIMx, uint32_t TIM_ITFLAG)
{
    /* Check the parameters */
    assert_param(IS_BASETIM_ALL_PERIPH(TIMx));

    /* Clear the flags */
    TIMx->ICR = (uint32_t)(0x01 & TIM_ITFLAG);
}

#endif
