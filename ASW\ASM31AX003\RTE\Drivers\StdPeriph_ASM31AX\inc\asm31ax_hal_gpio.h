/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_GPIO_H // asm31x
#define __ASM31AX_GPIO_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

/* Exported types ------------------------------------------------------------*/
#define IS_GPIO_ALL_PERIPH(PERIPH) (((PERIPH) == GPIOA) || \
                                    ((PERIPH) == GPIOB) || \
                                    ((PERIPH) == GPIOC) || \
                                    ((PERIPH) == GPIOD))

    /**
     * @brief  GPIO Configuration Mode enumeration
     */
    typedef enum
    {
        GPIO_MODE_INPUT = 0x00,     /*!< GPIO Input Mode */
        GPIO_MODE_OUTPUT_PP = 0x01, /*!< GPIO Output Mode */
        GPIO_MODE_OUTPUT_OD = 0x02, /*!< GPIO Alternate function Mode */
        GPIO_Mode_ANALOG = 0x03,    /*!< GPIO Analog Mode */
        GPIO_MODE_IT_LOW_LEVEL = 0x04,
        GPIO_MODE_IT_HIGH_LEVEL = 0x05,
        GPIO_MODE_IT_RISING = 0x06,
        GPIO_MODE_IT_FALLING = 0x07,
        GPIO_MODE_IT_BOTH_EDGE = 0x08
    } GPIO_MODE;
#define IS_GPIO_MODE(MODE) (((MODE) == GPIO_MODE_INPUT) ||  \
                            ((MODE) == GPIO_MODE_OUTPUT_PP) || \
                            ((MODE) == GPIO_MODE_OUTPUT_OD) ||  \
                            ((MODE) == GPIO_Mode_ANALOG) ||  \
                            ((MODE) == GPIO_MODE_IT_LOW_LEVEL) ||  \
                            ((MODE) == GPIO_MODE_IT_HIGH_LEVEL) ||  \
                            ((MODE) == GPIO_MODE_IT_RISING) ||  \
                            ((MODE) == GPIO_MODE_IT_FALLING) ||  \
                            ((MODE) == GPIO_MODE_OUTPUT_OD)

    /**
     * @brief  GPIO  Output speed enumeration
     */
    typedef enum
    {
        GPIO_SPEED_HIGH = 0x00, /*!< Low speed    */
        GPIO_SPEED_LOW = 0x01,  /*!< Medium speed */
    } GPIO_SPEED;

    typedef enum
    {
        GPIO_DRIVE_HIGH = 0x00, /*!< Low speed    */
        GPIO_DRIVE_LOW = 0x01,  /*!< Medium speed */
    } GPIO_DRIVE;

/* Add legacy definition */
#define GPIO_Speed_2MHz GPIO_Low_Speed
#define GPIO_Speed_25MHz GPIO_Medium_Speed
#define GPIO_Speed_50MHz GPIO_Fast_Speed
#define GPIO_Speed_100MHz GPIO_High_Speed
#define IS_GPIO_SPEED(SPEED) (((SPEED) == GPIO_Low_Speed) ||    \
                              ((SPEED) == GPIO_Medium_Speed) || \
                              ((SPEED) == GPIO_Fast_Speed) ||   \
                              ((SPEED) == GPIO_High_Speed))

    /**
     * @brief  GPIO Configuration PullUp PullDown enumeration
     */
    typedef enum
    {
        GPIO_PuPd_NOPULL = 0x00, /*!< 禁止上拉、下拉   */
        GPIO_PuPd_UP = 0x01,     /*!< 上拉使能        */
        GPIO_PuPd_DOWN = 0x02,   /*!< 下拉使能        */
        GPIO_PuPd_ALL = 0x03     /*!< 保留          */
    } GPIOPuPd_TypeDef;
#define IS_GPIO_PUPD(PUPD) (((PUPD) == GPIO_PuPd_NOPULL)||\
                            ((PUPD) == GPIO_PuPd_UP)    ||\
                            ((PUPD) == GPIO_PuPd_DOWN)) ||\
                            ((PUPD) == GPIO_PuPd_ALL))

    typedef enum
    {
        DB_CLK_DISABLE = 0x00UL,
        DB_CLK_1CYCLE = 0x10UL,
        DB_CLK_2CYCLE = 0x11UL,
        DB_CLK_4CYCLE = 0x12UL,
        DB_CLK_8CYCLE = 0x13UL,
        DB_CLK_16CYCLE = 0x14UL,
        DB_CLK_32CYCLE = 0x15UL,
        DB_CLK_64CYCLE = 0x16UL,
        DB_CLK_128CYCLE = 0x17UL,
        DB_CLK_256CYCLE = 0x18UL,
        DB_CLK_512CYCLE = 0x19UL,
        DB_CLK_1024CYCLE = 0x1AUL,
        DB_CLK_2_1024CYCLE = 0x1BUL,
        DB_CLK_4_1024CYCLE = 0x1CUL,
        DB_CLK_8_10024CYCLE = 0x1DUL,
        DB_CLK_16_1024CYCLE = 0x1EUL,
        DB_CLK_32_1024CYCLE = 0x1FUL
    } DB_CLK;

    /**
     * @brief  GPIO Bit SET and Bit RESET enumeration
     */
    typedef enum
    {
        Bit_RESET = 0,
        Bit_SET
    } BitAction;
#define IS_GPIO_BIT_ACTION(ACTION) (((ACTION) == Bit_RESET) || \
                                    ((ACTION) == Bit_SET))
    /**
     * @brief   GPIO Init structure definition
     */
    typedef struct
    {
        uint32_t Pin; /*!< Specifies the GPIO pins to be configured.
                                This parameter can be any value of @ref GPIO_pins_define */
        uint32_t IndbEn;
        DB_CLK Debounce;
        GPIO_MODE Mode; /*!< Specifies the operating mode for the selected pins.
                                         This parameter can be a value of @ref GPIOMode_TypeDef */

        GPIO_SPEED Speed; /*!< Specifies the speed for the selected pins.
                                           This parameter can be a value of @ref GPIOSpeed_TypeDef */

        GPIO_DRIVE Drive; /*!< Specifies the operating output type for the selected pins.
                                           This parameter can be a value of @ref GPIOOType_TypeDef */

        GPIOPuPd_TypeDef PuPd; /*!< Specifies the operating Pull-up/Pull down for the selected pins.
                                         This parameter can be a value of @ref GPIOPuPd_TypeDef */
    } GPIO_InitTypeDef;

    typedef struct
    {
        uint32_t num;
        GPIO_TypeDef *Gpio;
        uint32_t Pin;
    } GPIO_UnitTypeDef;

/** @defgroup GPIO_pins_define
 * @{
 */
#define GPIO_PIN_0 ((uint16_t)0x0001)    /* Pin 0 selected */
#define GPIO_PIN_1 ((uint16_t)0x0002)    /* Pin 1 selected */
#define GPIO_PIN_2 ((uint16_t)0x0004)    /* Pin 2 selected */
#define GPIO_PIN_3 ((uint16_t)0x0008)    /* Pin 3 selected */
#define GPIO_PIN_4 ((uint16_t)0x0010)    /* Pin 4 selected */
#define GPIO_PIN_5 ((uint16_t)0x0020)    /* Pin 5 selected */
#define GPIO_PIN_6 ((uint16_t)0x0040)    /* Pin 6 selected */
#define GPIO_PIN_7 ((uint16_t)0x0080)    /* Pin 7 selected */
#define GPIO_PIN_All ((uint16_t)0x00FF)  /* All pins selected */
#define GPIO_PIN_MASK ((uint32_t)0x00FF) /* PIN mask for assert test */
#define IS_GPIO_PIN(PIN) (((PIN)&GPIO_PIN_MASK) != (uint32_t)0x00)
#define IS_GET_GPIO_PIN(PIN) (((PIN) == GPIO_PIN_0) || \
                              ((PIN) == GPIO_PIN_1) || \
                              ((PIN) == GPIO_PIN_2) || \
                              ((PIN) == GPIO_PIN_3) || \
                              ((PIN) == GPIO_PIN_4) || \
                              ((PIN) == GPIO_PIN_5) || \
                              ((PIN) == GPIO_PIN_6) || \
                              ((PIN) == GPIO_PIN_7))

/** @defgroup GPIO_PIN_sources
 * @{
 */
#define GPIO_PinSource0 ((uint8_t)0x00)
#define GPIO_PinSource1 ((uint8_t)0x01)
#define GPIO_PinSource2 ((uint8_t)0x02)
#define GPIO_PinSource3 ((uint8_t)0x03)
#define GPIO_PinSource4 ((uint8_t)0x04)
#define GPIO_PinSource5 ((uint8_t)0x05)
#define GPIO_PinSource6 ((uint8_t)0x06)
#define GPIO_PinSource7 ((uint8_t)0x07)
#define IS_GPIO_PIN_SOURCE(PINSOURCE) (((PINSOURCE) == GPIO_PinSource0) || \
                                       ((PINSOURCE) == GPIO_PinSource1) || \
                                       ((PINSOURCE) == GPIO_PinSource2) || \
                                       ((PINSOURCE) == GPIO_PinSource3) || \
                                       ((PINSOURCE) == GPIO_PinSource4) || \
                                       ((PINSOURCE) == GPIO_PinSource5) || \
                                       ((PINSOURCE) == GPIO_PinSource6) || \
                                       ((PINSOURCE) == GPIO_PinSource7))

#define PA00 0
#define PA01 1
#define PA02 2
#define PA03 3
#define PA04 4
#define PA05 5
#define PA06 6
#define PA07 7

#define PB00 8
#define PB01 9
#define PB02 10
#define PB03 11
#define PB04 12
#define PB05 13
#define PB06 14
#define PB07 15

#define PC00 16
#define PC01 17
#define PC02 18
#define PC03 19
#define PC04 20
#define PC05 21
#define PC06 22
#define PC07 23

#define PD00 24
#define PD01 25
#define PD02 26
#define PD03 27
#define PD04 28
#define PD05 29
#define PD06 30
#define PD07 31
#define PINFULL 32
#define NOPIN 0xFF

#define PA0 0
#define PA1 1
#define PA2 2
#define PA3 3
#define PA4 4
#define PA5 5
#define PA6 6
#define PA7 7

#define PB0 8
#define PB1 9
#define PB2 10
#define PB3 11
#define PB4 12
#define PB5 13
#define PB6 14
#define PB7 15

#define PC0 16
#define PC1 17
#define PC2 18
#define PC3 19
#define PC4 20
#define PC5 21
#define PC6 22
#define PC7 23

#define PD0 24
#define PD1 25
#define PD2 26
#define PD3 27
#define PD4 28
#define PD5 29
#define PD6 30
#define PD7 31

/** @defgroup GPIO_PIN_direction_mode
 * @{
 */
#define GPIO_PIN_OUT_MODE ((uint32_t)0x00000001)
#define GPIO_PIN_IN_MODE ((uint32_t)0x00000000)
#define IS_GPIO_PIN_DIRECTION(PINMODE) (((PINMODE) == GPIO_PIN_OUT_MODE) || \
                                        ((PINMODE) == GPIO_PIN_IN_MODE))

/******************  Bits definition for GPIO_MODER register  *****************/
#define GPIO_MODER_MODER0 ((uint32_t)0x0000000f)
#define GPIO_MODER_MODER0_0 ((uint32_t)0x00000000)
#define GPIO_MODER_MODER0_1 ((uint32_t)0x00000001)
#define GPIO_MODER_MODER0_2 ((uint32_t)0x00000002)
#define GPIO_MODER_MODER0_3 ((uint32_t)0x00000003)
#define GPIO_MODER_MODER0_4 ((uint32_t)0x00000004)
#define GPIO_MODER_MODER0_5 ((uint32_t)0x00000005)
#define GPIO_MODER_MODER0_6 ((uint32_t)0x00000006)
#define GPIO_MODER_MODER0_7 ((uint32_t)0x00000007)

#define GPIO_MODER_MODER1 ((uint32_t)0x000000f0)
#define GPIO_MODER_MODER1_0 ((uint32_t)0x00000000)
#define GPIO_MODER_MODER1_1 ((uint32_t)0x00000010)
#define GPIO_MODER_MODER1_2 ((uint32_t)0x00000020)
#define GPIO_MODER_MODER1_3 ((uint32_t)0x00000030)
#define GPIO_MODER_MODER1_4 ((uint32_t)0x00000040)
#define GPIO_MODER_MODER1_5 ((uint32_t)0x00000050)
#define GPIO_MODER_MODER1_6 ((uint32_t)0x00000060)
#define GPIO_MODER_MODER1_7 ((uint32_t)0x00000070)

#define GPIO_MODER_MODER2 ((uint32_t)0x00000f00)
#define GPIO_MODER_MODER2_0 ((uint32_t)0x00000000)
#define GPIO_MODER_MODER2_1 ((uint32_t)0x00000100)
#define GPIO_MODER_MODER2_2 ((uint32_t)0x00000200)
#define GPIO_MODER_MODER2_3 ((uint32_t)0x00000300)
#define GPIO_MODER_MODER2_4 ((uint32_t)0x00000400)
#define GPIO_MODER_MODER2_5 ((uint32_t)0x00000500)
#define GPIO_MODER_MODER2_6 ((uint32_t)0x00000600)
#define GPIO_MODER_MODER2_7 ((uint32_t)0x00000700)

#define GPIO_MODER_MODER3 ((uint32_t)0x0000f000)
#define GPIO_MODER_MODER3_0 ((uint32_t)0x00000000)
#define GPIO_MODER_MODER3_1 ((uint32_t)0x00001000)
#define GPIO_MODER_MODER3_2 ((uint32_t)0x00002000)
#define GPIO_MODER_MODER3_3 ((uint32_t)0x00003000)
#define GPIO_MODER_MODER3_4 ((uint32_t)0x00004000)
#define GPIO_MODER_MODER3_5 ((uint32_t)0x00005000)
#define GPIO_MODER_MODER3_6 ((uint32_t)0x00006000)
#define GPIO_MODER_MODER3_7 ((uint32_t)0x00007000)

#define GPIO_MODER_MODER4 ((uint32_t)0x000f0000)
#define GPIO_MODER_MODER4_0 ((uint32_t)0x00000000)
#define GPIO_MODER_MODER4_1 ((uint32_t)0x00010000)
#define GPIO_MODER_MODER4_2 ((uint32_t)0x00020000)
#define GPIO_MODER_MODER4_3 ((uint32_t)0x00030000)
#define GPIO_MODER_MODER4_4 ((uint32_t)0x00040000)
#define GPIO_MODER_MODER4_5 ((uint32_t)0x00050000)
#define GPIO_MODER_MODER4_6 ((uint32_t)0x00060000)
#define GPIO_MODER_MODER4_7 ((uint32_t)0x00070000)

#define GPIO_MODER_MODER5 ((uint32_t)0x00f00000)
#define GPIO_MODER_MODER5_0 ((uint32_t)0x00000000)
#define GPIO_MODER_MODER5_1 ((uint32_t)0x00100000)
#define GPIO_MODER_MODER5_2 ((uint32_t)0x00200000)
#define GPIO_MODER_MODER5_3 ((uint32_t)0x00300000)
#define GPIO_MODER_MODER5_4 ((uint32_t)0x00400000)
#define GPIO_MODER_MODER5_5 ((uint32_t)0x00500000)
#define GPIO_MODER_MODER5_6 ((uint32_t)0x00600000)
#define GPIO_MODER_MODER5_7 ((uint32_t)0x00700000)

#define GPIO_MODER_MODER6 ((uint32_t)0x0f000000)
#define GPIO_MODER_MODER6_0 ((uint32_t)0x00000000)
#define GPIO_MODER_MODER6_1 ((uint32_t)0x01000000)
#define GPIO_MODER_MODER6_2 ((uint32_t)0x02000000)
#define GPIO_MODER_MODER6_3 ((uint32_t)0x03000000)
#define GPIO_MODER_MODER6_4 ((uint32_t)0x04000000)
#define GPIO_MODER_MODER6_5 ((uint32_t)0x05000000)
#define GPIO_MODER_MODER6_6 ((uint32_t)0x06000000)
#define GPIO_MODER_MODER6_7 ((uint32_t)0x07000000)

#define GPIO_MODER_MODER7 ((uint32_t)0xf0000000)
#define GPIO_MODER_MODER7_0 ((uint32_t)0x00000000)
#define GPIO_MODER_MODER7_1 ((uint32_t)0x10000000)
#define GPIO_MODER_MODER7_2 ((uint32_t)0x20000000)
#define GPIO_MODER_MODER7_3 ((uint32_t)0x30000000)
#define GPIO_MODER_MODER7_4 ((uint32_t)0x40000000)
#define GPIO_MODER_MODER7_5 ((uint32_t)0x50000000)
#define GPIO_MODER_MODER7_6 ((uint32_t)0x60000000)
#define GPIO_MODER_MODER7_7 ((uint32_t)0x70000000)

/******************  Bits definition for GPIO_OTYPER register  ****************/
#define GPIO_OTYPER_OT_0 ((uint32_t)0x00000001)
#define GPIO_OTYPER_OT_1 ((uint32_t)0x00000002)
#define GPIO_OTYPER_OT_2 ((uint32_t)0x00000004)
#define GPIO_OTYPER_OT_3 ((uint32_t)0x00000008)
#define GPIO_OTYPER_OT_4 ((uint32_t)0x00000010)
#define GPIO_OTYPER_OT_5 ((uint32_t)0x00000020)
#define GPIO_OTYPER_OT_6 ((uint32_t)0x00000040)
#define GPIO_OTYPER_OT_7 ((uint32_t)0x00000080)

/******************  Bits definition for GPIO_PUPDR register  *****************/
#define GPIO_PUPDR_PUPDR0 ((uint32_t)0x00000003)
#define GPIO_PUPDR_PUPDR0_0 ((uint32_t)0x00000001)
#define GPIO_PUPDR_PUPDR0_1 ((uint32_t)0x00000002)
#define GPIO_PUPDR_PUPDR1 ((uint32_t)0x0000000C)
#define GPIO_PUPDR_PUPDR1_0 ((uint32_t)0x00000004)
#define GPIO_PUPDR_PUPDR1_1 ((uint32_t)0x00000008)
#define GPIO_PUPDR_PUPDR2 ((uint32_t)0x00000030)
#define GPIO_PUPDR_PUPDR2_0 ((uint32_t)0x00000010)
#define GPIO_PUPDR_PUPDR2_1 ((uint32_t)0x00000020)
#define GPIO_PUPDR_PUPDR3 ((uint32_t)0x000000C0)
#define GPIO_PUPDR_PUPDR3_0 ((uint32_t)0x00000040)
#define GPIO_PUPDR_PUPDR3_1 ((uint32_t)0x00000080)
#define GPIO_PUPDR_PUPDR4 ((uint32_t)0x00000300)
#define GPIO_PUPDR_PUPDR4_0 ((uint32_t)0x00000100)
#define GPIO_PUPDR_PUPDR4_1 ((uint32_t)0x00000200)
#define GPIO_PUPDR_PUPDR5 ((uint32_t)0x00000C00)
#define GPIO_PUPDR_PUPDR5_0 ((uint32_t)0x00000400)
#define GPIO_PUPDR_PUPDR5_1 ((uint32_t)0x00000800)
#define GPIO_PUPDR_PUPDR6 ((uint32_t)0x00003000)
#define GPIO_PUPDR_PUPDR6_0 ((uint32_t)0x00001000)
#define GPIO_PUPDR_PUPDR6_1 ((uint32_t)0x00002000)
#define GPIO_PUPDR_PUPDR7 ((uint32_t)0x0000C000)
#define GPIO_PUPDR_PUPDR7_0 ((uint32_t)0x00004000)
#define GPIO_PUPDR_PUPDR7_1 ((uint32_t)0x00008000)

/***********  Bits definition for Pxn(n=0~7)消抖使能配置位  *****************/
#define GPIO_DIDB_SYNCEN ((uint32_t)0x00000100)
#define GPIO_DIDB_PIN0EN ((uint32_t)0x00000001)
#define GPIO_DIDB_PIN1EN ((uint32_t)0x00000002)
#define GPIO_DIDB_PIN2EN ((uint32_t)0x00000004)
#define GPIO_DIDB_PIN3EN ((uint32_t)0x00000008)
#define GPIO_DIDB_PIN4EN ((uint32_t)0x00000010)
#define GPIO_DIDB_PIN5EN ((uint32_t)0x00000020)
#define GPIO_DIDB_PIN6EN ((uint32_t)0x00000040)
#define GPIO_DIDB_PIN7EN ((uint32_t)0x00000080)
#define GPIO_DIDB_PINMASK ((uint32_t)0x000000FF)

/*****************  GPIO DBCLK set register *****************/
#define GPIO_DBCLK_EN ((uint32_t)0x00000010) /*< 使能去抖动时钟 */
#define GPIO_DBCLK_1CYCLE ((uint32_t)0x00000000)
#define GPIO_DBCLK_2CYCLE ((uint32_t)0x00000001)
#define GPIO_DBCLK_4CYCLE ((uint32_t)0x00000002)
#define GPIO_DBCLK_8CYCLE ((uint32_t)0x00000003)
#define GPIO_DBCLK_16CYCLE ((uint32_t)0x00000004)
#define GPIO_DBCLK_32CYCLE ((uint32_t)0x00000005)
#define GPIO_DBCLK_64CYCLE ((uint32_t)0x00000006)
#define GPIO_DBCLK_128CYCLE ((uint32_t)0x00000007)
#define GPIO_DBCLK_256CYCLE ((uint32_t)0x00000008)
#define GPIO_DBCLK_512CYCLE ((uint32_t)0x00000009)
#define GPIO_DBCLK_1024CYCLE ((uint32_t)0x0000000A)
#define GPIO_DBCLK_2048CYCLE ((uint32_t)0x0000000B)
#define GPIO_DBCLK_4096CYCLE ((uint32_t)0x0000000C)
#define GPIO_DBCLK_8192CYCLE ((uint32_t)0x0000000D)
#define GPIO_DBCLK_16384CYCLE ((uint32_t)0x0000000E)
#define GPIO_DBCLK_32768CYCLE ((uint32_t)0x0000000F)
#define GPIO_DBCLK_CYCLEMASK ((uint32_t)0x0000000F)
#define IS_GPIO_DBCLK_CYCLE(CYCLE) (((CYCLE) == GPIO_DBCLK_1CYCLE) ||     \
                                    ((CYCLE) == GPIO_DBCLK_2CYCLE) ||     \
                                    ((CYCLE) == GPIO_DBCLK_4CYCLE) ||     \
                                    ((CYCLE) == GPIO_DBCLK_8CYCLE) ||     \
                                    ((CYCLE) == GPIO_DBCLK_16CYCLE) ||    \
                                    ((CYCLE) == GPIO_DBCLK_32CYCLE) ||    \
                                    ((CYCLE) == GPIO_DBCLK_64CYCLE) ||    \
                                    ((CYCLE) == GPIO_DBCLK_128CYCLE) ||   \
                                    ((CYCLE) == GPIO_DBCLK_256CYCLE) ||   \
                                    ((CYCLE) == GPIO_DBCLK_1024CYCLE) ||  \
                                    ((CYCLE) == GPIO_DBCLK_2048CYCLE) ||  \
                                    ((CYCLE) == GPIO_DBCLK_4096CYCLE) ||  \
                                    ((CYCLE) == GPIO_DBCLK_8192CYCLE) ||  \
                                    ((CYCLE) == GPIO_DBCLK_16384CYCLE) || \
                                    ((CYCLE) == GPIO_DBCLK_32768CYCLE))

/** @defgroup GPIO_Alternat_function_selection_define
 * @{
 */
/**
 * @brief   AF 0 selection
 */
#define GPIO_AF_NRST ((uint8_t)0x00)    /* NRST Alternate Function mapping */
#define GPIO_AF_OSCIN ((uint8_t)0x00)   /* OSCIN Alternate Function mapping */
#define GPIO_AF_OSCOUT ((uint8_t)0x00)  /* OSCOUT Alternate Function mapping */
#define GPIO_AF_X32KOUT ((uint8_t)0x00) /* X32KOUT Alternate Function mapping */
#define GPIO_AF_X32KIN ((uint8_t)0x00)  /* X32KIN Alternate Function mapping */
#define GPIO_AF_SWDIO ((uint8_t)0x00)   /* SWDIO Alternate Function mapping */
#define GPIO_AF_SWDCLK ((uint8_t)0x00)  /* SWDCLK Alternate Function mapping */
#define GPIO_AF_GPIO ((uint8_t)0x00)
/**
 * @brief   AF 1 selection
 */
#define GPIO_AF_TIM1_CH1_PD4 ((uint8_t)0x01)   /* TIM1_CH1 Alternate Function mapping */
#define GPIO_AF_TIM1_CH1N_PD5 ((uint8_t)0x01)  /* TIM1_CH1N Alternate Function mapping */
#define GPIO_AF_TIM1_CH2_PD6 ((uint8_t)0x01)   /* TIM1_CH2 Alternate Function mapping */
#define GPIO_AF_TIM1_CH2N_PA1 ((uint8_t)0x01)  /* TIM1_CH2N Alternate Function mapping */
#define GPIO_AF_TIM1_CH3_PA2 ((uint8_t)0x01)   /* TIM1_CH3 Alternate Function mapping */
#define GPIO_AF_TIM1_CH3N_PA3 ((uint8_t)0x01)  /* TIM1_CH3N Alternate Function mapping */
#define GPIO_AF_TIM1_BKIN_PB5 ((uint8_t)0x01)  /* TIM1_BKIN Alternate Function mapping */
#define GPIO_AF_LPTIM_GATE_PB4 ((uint8_t)0x01) /* LPTIM_GATE Alternate Function mapping */
#define GPIO_AF_TIM1_CH3_PC3 ((uint8_t)0x01)   /* TIM1_CH3 Alternate Function mapping */
#define GPIO_AF_TIM1_CH4_PC4 ((uint8_t)0x01)   /* TIM1_CH4 Alternate Function mapping */
#define GPIO_AF_TIM1_BKIN_PC5 ((uint8_t)0x01)  /* TIM1_BKIN Alternate Function mapping */
#define GPIO_AF_TIM1_CH1_PC6 ((uint8_t)0x01)   /* TIM1_CH1 Alternate Function mapping */
#define GPIO_AF_TIM1_CH2_PC7 ((uint8_t)0x01)   /* TIM1_CH2 Alternate Function mapping */
#define GPIO_AF_TIM1_CH2_PD2 ((uint8_t)0x01)   /* TIM1_CH2 Alternate Function mapping */
#define GPIO_AF_TIM1_CH3N_PD3 ((uint8_t)0x01)  /* TIM1_CH3N Alternate Function mapping */

/**
 * @brief   AF 2 selection
 */
#define GPIO_AF_PCA_CH0_PD4 ((uint8_t)0x02)   /* PCA_CH0 Alternate Function mapping */
#define GPIO_AF_PCA_CH4_PD5 ((uint8_t)0x02)   /* PCA_CH4 Alternate Function mapping */
#define GPIO_AF_PCA_CH3_PD6 ((uint8_t)0x02)   /* PCA_CH3 Alternate Function mapping */
#define GPIO_AF_PCA_CH2_PA3 ((uint8_t)0x02)   /* PCA_CH2 Alternate Function mapping */
#define GPIO_AF_PCA_CH4_PB5 ((uint8_t)0x02)   /* PCA_CH4 Alternate Function mapping */
#define GPIO_AF_PCA_ECI_PB4 ((uint8_t)0x02)   /* PCA_ECI Alternate Function mapping */
#define GPIO_AF_TIM1_CH1N_PC3 ((uint8_t)0x02) /* TIM1_CH1N Alternate Function mapping */
#define GPIO_AF_TIM1_CH2N_PC4 ((uint8_t)0x02) /* TIM1_CH2N Alternate Function mapping */
#define GPIO_AF_PCA_CH0_PC5 ((uint8_t)0x02)   /* PCA_CH0 Alternate Function mapping */
#define GPIO_AF_PCA_CH3_PC6 ((uint8_t)0x02)   /* PCA_CH3 Alternate Function mapping */
#define GPIO_AF_PCA_CH4_PC7 ((uint8_t)0x02)   /* PCA_CH4 Alternate Function mapping */
#define GPIO_AF_PCA_ECI_PD1 ((uint8_t)0x02)   /* PCA_ECI Alternate Function mapping */
#define GPIO_AF_PCA_CH2_PD2 ((uint8_t)0x02)   /* PCA_CH2 Alternate Function mapping */
#define GPIO_AF_PCA_CH1_PD3 ((uint8_t)0x02)   /* PCA_CH1 Alternate Function mapping */

/**
 * @brief   AF 3 selection
 */
#define GPIO_AF_RTC_1HZ_PD4 ((uint8_t)0x03)  /* RTC_1HZ Alternate Function mapping */
#define GPIO_AF_SPI_MISO_PD5 ((uint8_t)0x03) /* SPI_MISO Alternate Function mapping */
#define GPIO_AF_SPI_MOSI_PD6 ((uint8_t)0x03) /* SPI_MOSI Alternate Function mapping */
#define GPIO_AF_SPI_CLK_PA1 ((uint8_t)0x03)  /* SPI_CLK Alternate Function mapping */
#define GPIO_AF_SPI_NSS_PA2 ((uint8_t)0x03)  /* SPI_NSS Alternate Function mapping */
#define GPIO_AF_SPI_NSS_PA3 ((uint8_t)0x03)  /* SPI_NSS Alternate Function mapping */
#define GPIO_AF_SPI_CLK_PB5 ((uint8_t)0x03)  /* SPI_CLK Alternate Function mapping */
#define GPIO_AF_SPI_NSS_PB4 ((uint8_t)0x03)  /* SPI_NSS Alternate Function mapping */
#define GPIO_AF_SPI_CLK_PC5 ((uint8_t)0x03)  /* SPI_CLK Alternate Function mapping */
#define GPIO_AF_SPI_MOSI_PC6 ((uint8_t)0x03) /* SPI_MOSI Alternate Function mapping */
#define GPIO_AF_SPI_MISO_PC7 ((uint8_t)0x03) /* SPI_MISO Alternate Function mapping */
#define GPIO_AF_SPI_MISO_PD2 ((uint8_t)0x03) /* SPI_MISO Alternate Function mapping */
#define GPIO_AF_SPI_MOSI_PD3 ((uint8_t)0x03) /* SPI_MOSI Alternate Function mapping */

/**
 * @brief   AF 4 selection
 */
#define GPIO_AF_TIM10_TOG_PD4 ((uint8_t)0x04) /* TIM10_TOG Alternate Function mapping */
#define GPIO_AF_I2C_SCL_PD5 ((uint8_t)0x04)   /* I2C_SCL Alternate Function mapping */
#define GPIO_AF_I2C_SDA_PD6 ((uint8_t)0x04)   /* I2C_SDA Alternate Function mapping */
#define GPIO_AF_I2C_SDA_PA1 ((uint8_t)0x04)   /* I2C_SDA Alternate Function mapping */
#define GPIO_AF_I2C_SCL_PA2 ((uint8_t)0x04)   /* I2C_SCL Alternate Function mapping */
#define GPIO_AF_RTC_1HZ_PA3 ((uint8_t)0x04)   /* RTC_1HZ Alternate Function mapping */
#define GPIO_AF_I2C_SDA_PB5 ((uint8_t)0x04)   /* I2C_SDA Alternate Function mapping */
#define GPIO_AF_I2C_SCL_PB4 ((uint8_t)0x04)   /* I2C_SCL Alternate Function mapping */
#define GPIO_AF_I2C_SDA_PC3 ((uint8_t)0x04)   /* I2C_SDA Alternate Function mapping */
#define GPIO_AF_I2C_SCL_PC4 ((uint8_t)0x04)   /* I2C_SCL Alternate Function mapping */
#define GPIO_AF_RTC_1HZ_PD2 ((uint8_t)0x04)   /* RTC_1HZ Alternate Function mapping */
#define GPIO_AF_HXT_OUT_PD3 ((uint8_t)0x04)   /* HXT_OUT Alternate Function mapping */

/**
 * @brief   AF 5 selection
 */
#define GPIO_AF_UART0_TXD_PD4 ((uint8_t)0x05)  /* UART0_TXD Alternate Function mapping */
#define GPIO_AF_UART1_TXD_PD5 ((uint8_t)0x05)  /* UART1_TXD Alternate Function mapping */
#define GPIO_AF_UART1_RXD_PD6 ((uint8_t)0x05)  /* UART1_RXD Alternate Function mapping */
#define GPIO_AF_UART0_RXD_PA1 ((uint8_t)0x05)  /* UART0_RXD Alternate Function mapping */
#define GPIO_AF_UART0_TXD_PA2 ((uint8_t)0x05)  /* UART0_TXD Alternate Function mapping */
#define GPIO_AF_LPUART_RXD_PA3 ((uint8_t)0x05) /* LPUART_RXD Alternate Function mapping */
#define GPIO_AF_UART0_RXD_PB5 ((uint8_t)0x05)  /* UART0_RXD Alternate Function mapping */
#define GPIO_AF_UART0_TXD_PB4 ((uint8_t)0x05)  /* UART0_TXD Alternate Function mapping */
#define GPIO_AF_UART1_TXD_PC3 ((uint8_t)0x05)  /* UART1_TXD Alternate Function mapping */
#define GPIO_AF_UART1_RXD_PC4 ((uint8_t)0x05)  /* UART1_RXD Alternate Function mapping */
#define GPIO_AF_LPUART_TXD_PC5 ((uint8_t)0x05) /* LPUART_TXD Alternate Function mapping */
#define GPIO_AF_LPUART_RXD_PC6 ((uint8_t)0x05) /* LPUART_RXD Alternate Function mapping */
#define GPIO_AF_UART1_RXD_PC7 ((uint8_t)0x05)  /* UART1_RXD Alternate Function mapping */
#define GPIO_AF_UART1_TXD_PD1 ((uint8_t)0x05)  /* UART1_TXD Alternate Function mapping */
#define GPIO_AF_LPUART_TXD_PD2 ((uint8_t)0x05) /* LPUART_TXD Alternate Function mapping */
#define GPIO_AF_UART0_RXD_PD3 ((uint8_t)0x05)  /* UART0_RXD Alternate Function mapping */

/**
 * @brief   AF 6 selection
 */
#define GPIO_AF_TIM10_EXT_PD4 ((uint8_t)0x06)  /* TIM10_EXT Alternate Function mapping */
#define GPIO_AF_TIM10_GATE_PD5 ((uint8_t)0x06) /* TIM10_GATE Alternate Function mapping */
#define GPIO_AF_LPTIM_EXT_PD6 ((uint8_t)0x06)  /* LPTIM_EXT Alternate Function mapping */
#define GPIO_AF_TIM10_TOG_PA1 ((uint8_t)0x06)  /* TIM10_TOG Alternate Function mapping */
#define GPIO_AF_TIM10_TOGN_PA2 ((uint8_t)0x06) /* TIM10_TOGN Alternate Function mapping */
#define GPIO_AF_PCA_ECI_PA3 ((uint8_t)0x06)    /* PCA_ECI Alternate Function mapping */
#define GPIO_AF_TIM11_TOG_PB5 ((uint8_t)0x06)  /* TIM11_TOG Alternate Function mapping */
#define GPIO_AF_TIM11_TOGN_PB4 ((uint8_t)0x06) /* TIM11_TOGN Alternate Function mapping */
#define GPIO_AF_PCA_CH1_PC3 ((uint8_t)0x06)    /* PCA_CH1 Alternate Function mapping */
#define GPIO_AF_PCA_CH0_PC4 ((uint8_t)0x06)    /* PCA_CH0 Alternate Function mapping */
#define GPIO_AF_TIM11_GATE_PC5 ((uint8_t)0x06) /* TIM11_GATE Alternate Function mapping */
#define GPIO_AF_TIM11_EXT_PC6 ((uint8_t)0x06)  /* TIM11_EXT Alternate Function mapping */
#define GPIO_AF_LIRC_OUT_PC7 ((uint8_t)0x06)   /* LIRC_OUT Alternate Function mapping */
#define GPIO_AF_HIRC_OUT_PD1 ((uint8_t)0x06)   /* HIRC_OUT Alternate Function mapping */
#define GPIO_AF_LPTIM_TOG_PD2 ((uint8_t)0x06)  /* LPTIM_TOG Alternate Function mapping */
#define GPIO_AF_LPTIM_TOGN_PD3 ((uint8_t)0x06) /* LPTIM_TOGN Alternate Function mapping */

/**
 * @brief   AF 7 selection
 */
#define GPIO_AF_BEEP_PD4 ((uint8_t)0x07)      /* BEEP Alternate Function mapping */
#define GPIO_AF_UART0_TXD_PD5 ((uint8_t)0x07) /* UART0_TXD Alternate Function mapping */
#define GPIO_AF_UART0_RXD_PD6 ((uint8_t)0x07) /* UART0_RXD Alternate Function mapping */
#define GPIO_AF_UART1_RXD_PA1 ((uint8_t)0x07) /* UART1_RXD Alternate Function mapping */
#define GPIO_AF_UART1_TXD_PA2 ((uint8_t)0x07) /* UART1_TXD Alternate Function mapping */
#define GPIO_AF_VC0_OUT_PA3 ((uint8_t)0x07)   /* VC0_OUT Alternate Function mapping */
#define GPIO_AF_LVD_OUT_PB5 ((uint8_t)0x07)   /* LVD_OUT Alternate Function mapping */
#define GPIO_AF_1_WIRE_PC3 ((uint8_t)0x07)    /* 1_WIRE Alternate Function mapping */
#define GPIO_AF_CLK_MCO_PC4 ((uint8_t)0x07)   /* CLK_MCO Alternate Function mapping */
#define GPIO_AF_LVD_OUT_PC5 ((uint8_t)0x07)   /* LVD_OUT Alternate Function mapping */
#define GPIO_AF_CLK_MCO_PC6 ((uint8_t)0x07)   /* CLK_MCO Alternate Function mapping */
#define GPIO_AF_LXT_OUT_PC7 ((uint8_t)0x07)   /* LXT_OUT Alternate Function mapping */
#define GPIO_AF_VC0_OUT_PD1 ((uint8_t)0x07)   /* VC0_OUT Alternate Function mapping */
#define GPIO_AF_1_WIRE_PD2 ((uint8_t)0x07)    /* 1_WIRE Alternate Function mapping */

/**
 * @brief   AF 8 selection
 */
#define GPIO_AF_TIM2_CH1_PD4 ((uint8_t)0x08) /* TIM2_CH1 Alternate Function mapping */
#define GPIO_AF_TIM2_CH4_PD5 ((uint8_t)0x08) /* TIM2_CH4 Alternate Function mapping */
#define GPIO_AF_TIM2_CH2_PD6 ((uint8_t)0x08) /* TIM2_CH2 Alternate Function mapping */
#define GPIO_AF_TIM2_CH2_PA2 ((uint8_t)0x08) /* TIM2_CH2 Alternate Function mapping */
#define GPIO_AF_TIM2_CH3_PA3 ((uint8_t)0x08) /* TIM2_CH3 Alternate Function mapping */
#define GPIO_AF_TIM2_CH1_PB5 ((uint8_t)0x08) /* TIM2_CH1 Alternate Function mapping */
#define GPIO_AF_TIM2_CH3_PC3 ((uint8_t)0x08) /* TIM2_CH3 Alternate Function mapping */
#define GPIO_AF_TIM2_CH4_PC4 ((uint8_t)0x08) /* TIM2_CH4 Alternate Function mapping */
#define GPIO_AF_TIM2_CH1_PC5 ((uint8_t)0x08) /* TIM2_CH1 Alternate Function mapping */
#define GPIO_AF_TIM2_CH4_PC6 ((uint8_t)0x08) /* TIM2_CH4 Alternate Function mapping */
#define GPIO_AF_TIM2_CH3_PD2 ((uint8_t)0x08) /* TIM2_CH3 Alternate Function mapping */
#define GPIO_AF_TIM2_CH2_PD3 ((uint8_t)0x08) /* TIM2_CH2 Alternate Function mapping */

/**
 * @brief   AF F selection
 */
#define GPIO_AF_VCIN2_PD4 ((uint8_t)0x0F) /* VCIN2 Alternate Function mapping  */
#define GPIO_AF_AIN5_PD5 ((uint8_t)0x0F)  /* AIN5 Alternate Function mapping  */
#define GPIO_AF_AIN6_PD6 ((uint8_t)0x0F)  /* AIN6 Alternate Function mapping  */
#define GPIO_AF_AIN1_PC3 ((uint8_t)0x0F)  /* AIN1 Alternate Function mapping  */
#define GPIO_AF_AIN2_PC4 ((uint8_t)0x0F)  /* AIN2 Alternate Function mapping  */
#define GPIO_AF_VCIN1_PC5 ((uint8_t)0x0F) /* VCIN1 Alternate Function mapping  */
#define GPIO_AF_AIN0_PC6 ((uint8_t)0x0F)  /* AIN0 Alternate Function mapping  */
#define GPIO_AF_VCIN0_PD2 ((uint8_t)0x0F) /* VCIN0 Alternate Function mapping  */
#define GPIO_AF_AIN3_PD2 ((uint8_t)0x0F)  /* AIN3 Alternate Function mapping  */
#define GPIO_AF_AIN4_PD3 ((uint8_t)0x0F)  /* AIN4 Alternate Function mapping  */

#define IS_GPIO_AF(GPIO_AF) (((GPIO_AF >= 0) && (GPIO_AF <= 7)) || \
                             (GPIO_AF == 0x0f))

    /*Function used to set the GPIO configuration to the default reset state ****/
    void HAL_GPIO_DeInit(GPIO_TypeDef *GPIOx);
    void HAL_GPIO_AFIODeInit(void);
    void HAL_GPIO_Init(GPIO_TypeDef *GPIOx, GPIO_InitTypeDef *GPIO_InitStruct);
    void HAL_GPIO_StructInit(GPIO_InitTypeDef *GPIO_InitStruct);
    uint8_t HAL_GPIO_ReadInputDataBit(GPIO_TypeDef *GPIOx, uint16_t Pin);
    uint16_t HAL_GPIO_ReadInputData(GPIO_TypeDef *GPIOx);
    void HAL_GPIO_DBSyncCmd(GPIO_TypeDef *GPIOx, FunctionalState NewState);
    void HAL_GPIO_DBPinSyncCmd(GPIO_TypeDef *GPIOx, uint32_t DB_Pin, FunctionalState NewState);
    void HAL_GPIO_DBClkCmd(GPIO_TypeDef *GPIOx, FunctionalState NewState);
    void HAL_GPIO_SetDBClk(GPIO_TypeDef *GPIOx, uint32_t DBClk);
    uint8_t HAL_GPIO_ReadOutputDataBit(GPIO_TypeDef *GPIOx, uint16_t Pin);
    uint16_t HAL_GPIO_ReadOutputData(GPIO_TypeDef *GPIOx);
    void HAL_GPIO_SetBits(GPIO_TypeDef *GPIOx, uint16_t Pin);
    void HAL_GPIO_ResetBits(GPIO_TypeDef *GPIOx, uint16_t Pin);
    void HAL_GPIO_WriteBit(GPIO_TypeDef *GPIOx, uint16_t Pin, BitAction BitVal);
    void HAL_GPIO_Write(GPIO_TypeDef *GPIOx, uint16_t PortVal);
    void HAL_GPIO_ToggleBits(GPIO_TypeDef *GPIOx, uint16_t Pin);
    void HAL_GPIO_PinPuPdCmd(GPIO_TypeDef *GPIOx, uint16_t Pin, uint8_t GPIO_PUPD);
    void HAL_GPIO_PortDRCmd(GPIO_TypeDef *GPIOx, uint8_t Pin, FunctionalState NewState);
    void HAL_GPIO_PinRemapConfig(GPIO_TypeDef *GPIOx, uint8_t GPIO_AFR, uint8_t Pin);
    void HAL_GPIO_PinAFConfig(GPIO_TypeDef *GPIOx, uint16_t GPIO_PinSource, uint8_t GPIO_AF);
    void HAL_GPIO_PinAFConfig1(GPIO_TypeDef *GPIOx, uint32_t Pin, uint8_t GPIO_AF);
    const GPIO_UnitTypeDef *HAL_GPIO_Get_Pin(uint32_t num);
#ifdef __cplusplus
}
#endif

#endif /*__ASM31X_GPIO_H */
