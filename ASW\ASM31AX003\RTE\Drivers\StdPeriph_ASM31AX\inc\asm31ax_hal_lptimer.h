/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_LPTIMER_H // asm31x
#define __ASM31AX_LPTIMER_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

    typedef struct
    {
        LPTIMER_TypeDef *Instance;
        uint32_t Mode;      /*<! LPTIMER 工作模式 */
        uint32_t CTEN;      /*<! 计数器/定时器功能选择设置掩码 */
        uint32_t TCLK;      /*<! LPTIM 时钟选择掩码 */
        uint32_t GATEEN;    /*<! 定时器门控 */
        uint32_t GATEPOLE;  /*<! 输入 GATE 的有效极性 */
        uint32_t TCLKCUTEN; /*<! LPTIM 计数时钟使能 */
        uint32_t TogEn;
        uint32_t Period;
    } LPTIMER_InitTypeDef;

/* Exported types ------------------------------------------------------------*/
#define IS_LPTIMER_ALL_PERIPH(PERIPH)    (((PERIPH) == LPTIMER)

/*mask set*/
#define LPTIMER_MODE_MASK ((uint32_t)0x01 << 1) /*<! 工作模式设置掩码 */
#define LPTIMER_TOG_MASK ((uint32_t)0x01 << 3)
#define LPTIMER_CT_MASK ((uint32_t)0x01 << 2)       /*<! 计数器/定时器功能选择设置掩码 */
#define LPTIMER_TCLK_MASK ((uint32_t)0x03 << 4)     /*<! LPTIM 时钟选择掩码 */
#define LPTIMER_GATE_MASK ((uint32_t)0x01 << 6)     /*<! 定时器门控 */
#define LPTIMER_GATEPOLE_MASK ((uint32_t)0x01 << 7) /*<! 输入 GATE 的有效极性 */
#define LPTIMER_TCLKCUT_MASK ((uint32_t)0x01 << 9)  /*<! LPTIM 计数时钟使能 */

/*para mode set*/
#define LPTIMER_MODE1 ((uint32_t)0x00 << 1) /*<! 模式 1 无重载模式 16 位计数器/定时器 */
#define LPTIMER_MODE2 ((uint32_t)0x01 << 1) /*<! 模式 2 自动重装载 16 位计数器/定时器 */
#define IS_LPTIMER_MODE(MODE) ((MODE == LPTIMER_MODE1) || \
                               (MODE == LPTIMER_MODE2))

/*para CT set*/
#define LPTIMER_COUNT ((uint32_t)0x01 << 2)
#define LPTIMER_TIMER ((uint32_t)0x00 << 2)
#define IS_LPTIMER_CT(CT) ((CT == LPTIMER_COUNT) || \
                           (CT == LPTIMER_TIMER))

/*para TCLK set*/
#define LPTIMER_TCLK_PCLK ((uint32_t)0x00 << 4)
#define LPTIMER_TCLK_LXT ((uint32_t)0x02 << 4)
#define LPTIMER_TCLK_LIRC ((uint32_t)0x03 << 4)
#define IS_LPTIMER_TCLK(TCLK)  ((TCLK == LPTIMER_TCLK_PCLK)||\
                                (TCLK==LPTIMER_TCLK_LXT))  ||\
                                (TCLK==LPTIMER_TCLK_LIRC))

/*para GATE set*/
#define LPTIMER_GATE ((uint32_t)0x01 << 6)
#define LPTIMER_NGATE ((uint32_t)0x00 << 6)
#define IS_LPTIMER_GATE(GATE) ((GATE == LPTIMER_GATE) || \
                               (GATE == LPTIMER_NGATE))

/*para GATE LEVEL set*/
#define LPTIMER_GATE_HIGH ((uint32_t)0x00 << 7)
#define LPTIMER_GATE_LOW ((uint32_t)0x01 << 7)
#define IS_LPTIMER_GATEPOLE(GATE) ((GATE == LPTIMER_GATE_HIGH) || \
                                   (GATE == LPTIMER_GATE_LOW))

/*para TICK CUT set*/
#define LPTIMER_TICK_CUTENABLE ((uint32_t)0x01 << 9)
#define LPTIMER_TICK_CUTDISABLE ((uint32_t)0x00 << 9)
#define IS_LPTIMER_CUT(CUT) ((CUT == LPTIMER_TICK_CUTENABLE) || \
                             (CUT == LPTIMER_TICK_CUTDISABLE))

/*para LPTIMER RUN set*/
#define LPTIMER_RUN_ENABLE ((uint32_t)0x01)
#define LPTIMER_RUN_DISABLE ((uint32_t)0x00)

/*para LPTIMER TOG_EN set*/
#define LPTIMER_TOG_ENABLE ((uint32_t)0x01 << 3)
#define LPTIMER_TOG_DISABLE ((uint32_t)0x00 << 3)

/*para LPTIMER INT_EN set*/
#define LPTIMER_IT_ENABLE ((uint32_t)0x01 << 8)
#define LPTIMER_IT_DISABLE ((uint32_t)0x00 << 8)

/*para LPTIMER WT_FLAG set*/
#define LPTIMER_WT_FLAG ((uint32_t)0x01 << 16)

/* LPTIMER IT FLAG */
#define LPTIMER_IT_FLAG (uint32_t)0x01
#define IS_LPTIMER_IT(FLAG) (FLAG == LPTIMER_IT_FLAG)

#define IS_LPTIMER_LOAD(VALUE) ((VALUE >= 0x00) || \
                                (VALUE <= 2 ^ 15))
#define IS_LPTIMER_BGLOAD(VALUE) ((VALUE >= 0x00) || \
                                  (VALUE <= 2 ^ 15))

    /*define function*/
    uint32_t HAL_LPTIMER_ReadCnt(void);
    void HAL_LPTIMER_Init(LPTIMER_InitTypeDef *LPTIMER_InitStruct);
    void HAL_LPTIMER_Cmd(LPTIMER_TypeDef *LPTIMERx, FunctionalState NewState);
    void HAL_LPTIMER_TogCmd(LPTIMER_TypeDef *LPTIMERx, FunctionalState NewState);
    void HAL_LPTIMER_ITConfig(LPTIMER_TypeDef *LPTIMERx, FunctionalState NewState);
    FlagStatus HAL_LPTIMER_GetWTFlagStatus(LPTIMER_TypeDef *LPTIMERx);
    void HAL_LPTIMER_BGloadConfig(LPTIMER_TypeDef *LPTIMERx, uint32_t Value);
    void HAL_LPTIMER_LoadConfig(LPTIMER_TypeDef *LPTIMERx, uint32_t Value);
    ITStatus HAL_LPTIMER_GetITStatus(LPTIMER_TypeDef *LPTIMERx, uint32_t IntFlag);
    void HAL_LPTIMER_ClearITFlag(LPTIMER_TypeDef *LPTIMERx, uint32_t IntFlag);

#ifdef __cplusplus
}
#endif

#endif
