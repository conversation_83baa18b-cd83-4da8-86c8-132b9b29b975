/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_IWDG_H
#define __ASM31AX_IWDG_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

    typedef struct
    {

        IWDG_TypeDef *Instance;

        uint16_t Mode; /*!< Specifies the SPI operating mode.
                                This parameter can be a value of @ref SPI_mode */

        uint32_t Reload;

    } IWDG_InitTypeDef;

/** @defgroup IWDG_WriteAccess
 * @{
 */
#define IWDG_WriteAccess_Enable 0x55AA6698
#define IWDG_WriteAccess_Disable 0x55AA6699
#define IS_IWDG_WRITE_ACCESS(ACCESS) (((ACCESS) == IWDG_WriteAccess_Enable) || \
                                      ((ACCESS) == IWDG_WriteAccess_Disable))

/* KR register bit mask */
#define COMMAND_IWDT_RELOAD ((uint32_t)0x000000AA)
#define COMMAND_IWDT_ENABLE ((uint32_t)0x00000055)
#define COMMAND_WDTINTCLR_ENBLE ((uint32_t)0x00000001)
#define COMMAND_WDTINTSHIELD_ENBLE ((uint32_t)0x00000002)
#define COMMAND_WDT_RESET ((uint32_t)0x00000000)
#define COMMAND_WDT_INT ((uint32_t)0x00000001)
#define IWDG_INT_FLAG ((uint32_t)0x00000001)
#define IS_IWDG_RELOAD(RELOAD) ((RELOAD) <= 0x000FFFFF)

    /* Exported functions --------------------------------------------------------*/
    void HAL_IWDG_Init(IWDG_InitTypeDef *IWDG_InitStruct);
    void HAL_IWDG_Feed(IWDG_InitTypeDef *IWDG_InitStruct, uint32_t time);
    void HAL_IWDG_WriteAccessCmd(uint32_t IWDG_WriteAccess);
    void HAL_IWDG_SetPrescaler(uint8_t IWDG_Prescaler);
    void HAL_IWDG_SetReload(uint32_t Reload);
    void HAL_IWDG_ReloadCounter(void);
    void HAL_IWDG_SetWdtMode(uint32_t Mode);
    uint32_t HAL_IWDG_GetReload(void);
    void HAL_IWDG_WdtITShieldCmd(FunctionalState NewStatus);
    void HAL_IWDG_RELOAD(void);
    void HAL_IWDG_Cmd(void);
    FlagStatus HAL_IWDG_GetFlagStatus(uint16_t IWDG_FLAG);
    void HAL_IWDG_WdtITClear(void);

#ifdef __cplusplus
}
#endif

#endif /* __ASM31X_IWDG_H */
