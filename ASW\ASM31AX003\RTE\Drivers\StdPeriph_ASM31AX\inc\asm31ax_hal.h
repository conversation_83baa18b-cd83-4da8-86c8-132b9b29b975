#ifndef __ASM31_HAL_H__
#define __ASM31_HAL_H__

typedef enum
{
    RESET = 0,
    SET = !RESET
} FlagStatus,
    ITStatus;

typedef enum
{
    DISABLE = 0,
    ENABLE = !DISABLE
} FunctionalState;
#define IS_FUNCTIONAL_STATE(STATE) (((STATE) == DISABLE) || ((STATE) == ENABLE))

typedef enum
{
    ERROR = 0,
    SUCCESS = !ERROR
} ErrorStatus;

#include "asm31ax.h"

/// 中断优先级
#define NVIC_PRIO_0 0
#define NVIC_PRIO_1 1
#define NVIC_PRIO_2 2
#define NVIC_PRIO_3 3

void HAL_IRQ_Enable(IRQn_Type type, uint32_t priority);
void HAL_IRQ_Disable(IRQn_Type type);

#include "asm31ax_hal_conf.h"

#endif
