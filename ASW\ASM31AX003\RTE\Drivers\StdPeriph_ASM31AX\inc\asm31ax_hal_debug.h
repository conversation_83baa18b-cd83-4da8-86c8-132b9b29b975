/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_DEBUG_H // asm31x
#define __ASM31AX_DEBUG_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

    /** @defgroup DEBUG_InitTypeDef
     * @{
     */
    typedef struct
    {
        uint8_t TIMER10DEGSTOP; /*!< TIM10 调试模式停止工作 */
        uint8_t TIMER11DEGSTOP; /*!< TIM11 调试模式停止工作 */
        uint8_t LPTIMDEGSTOP;   /*!< Low Power Timer 调试模式停止工作 */
        uint8_t PCADEGSTOP;     /*!< PCA 调试模式停止工作 */
        uint8_t TIM1DESTOP;     /*!< TIM1 调试模式停止工作 */
        uint8_t RTCDEGSTOP;     /*!< RTC 调试模式停止工作 */
        uint8_t AWKDEGSTOP;     /*!< AWK 调试模式停止工作 */
        uint8_t BEEPDEGSTOP;    /*!< BEEP 调试模式停止工作 */
        uint8_t IWDTDEGSTOP;    /*!< IWDG 调试模式停止工作 */
        uint8_t WWDTDEGSTOP;    /*!< WWDG 调试模式停止工作 */
        uint8_t TIM2DEGSTOP;    /*!< TIM2 调试模式停止工作 */
    } DEBUG_InitTypeDef;

    /** @defgroup LPUART_Exported_Functions
     * @{
     */
    void DEBUG_DebugCmd(DEBUG_TypeDef *DEBUGx, DEBUG_InitTypeDef *DEBUG_InitStruct);

#ifdef __cplusplus
}
#endif

#endif /*__ASM31X_DEBUG_H */
