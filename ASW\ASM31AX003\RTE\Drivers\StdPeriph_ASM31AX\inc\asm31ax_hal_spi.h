/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_SPI_H
#define __ASM31AX_SPI_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

/** @defgroup SPI_Exported_Constants
 * @{
 */
#define IS_SPI_ALL_PERIPH(PERIPH) ((PERIPH) == SPI)

    /**
     * @brief  SPI Init structure definition
     */

    typedef struct
    {

        SPI_TypeDef *Instance;
        uint16_t Mode; /*!< Specifies the SPI operating mode.
                                This parameter can be a value of @ref SPI_mode */

        uint16_t CPOL; /*!< Specifies the serial clock steady state.
                                This parameter can be a value of @ref SPI_Clock_Polarity */

        uint16_t CPHA; /*!< Specifies the clock active edge for the bit capture.
                                This parameter can be a value of @ref SPI_Clock_Phase */

        uint16_t Prescaler; /*!< Specifies the Baud Rate prescaler value which will be
                                             used to configure the transmit and receive SCK clock.
                                             This parameter can be a value of @ref SPI_BaudRate_Prescaler.
                                             @note The communication clock is derived from the master
                                                   clock. The slave clock does not need to be set. */
    } SPI_InitTypeDef;

/** @defgroup SPI_mode
 * @{
 */
#define SPI_MODE_MASTER ((uint32_t)0x00000010)
#define SPI_MODE_SLAVE ((uint32_t)0x00000000)
#define IS_SPI_MODE(MODE) (((MODE) == SPI_MODE_MASTER) || \
                           ((MODE) == SPI_MODE_SLAVE))

#define SPI_NSS_ENABLE (0x00000001UL)
#define SPI_NSS_DISABLE (0x00000000UL)

/** @defgroup SPI_Clock_Polarity
 * @{
 */
#define SPI_CPOL_LOW ((uint32_t)0x00000000)
#define SPI_CPOL_HIGH ((uint32_t)0x00000008)
#define IS_SPI_CPOL(CPOL) (((CPOL) == SPI_CPOL_LOW) || \
                           ((CPOL) == SPI_CPOL_HIGH))
/**
 * @}SPI_SPEN_Enable
 */
#define SPI_SPEN_ENABLE ((uint32_t)0x00000040)  /*<! SPI模块Enable */
#define SPI_SPEN_DISABLE ((uint32_t)0x00000000) /*<! SPI模块Disable */

/** @defgroup SPI_Clock_Phase
 * @{
 */
#define SPI_CPHA_1EDGE ((uint32_t)0x00000000)
#define SPI_CPHA_2EDGE ((uint32_t)0x00000004)
#define IS_SPI_CPHA(CPHA) (((CPHA) == SPI_CPHA_1EDGE) || \
                           ((CPHA) == SPI_CPHA_2EDGE))

/** @defgroup SPI_BaudRate_Prescaler
 * @{
 */
#define PRESCALER_2 ((uint32_t)0x00000000)
#define PRESCALER_4 ((uint32_t)0x00000001)
#define PRESCALER_8 ((uint32_t)0x00000002)
#define PRESCALER_16 ((uint32_t)0x00000003)
#define PRESCALER_32 ((uint32_t)0x00000080)
#define PRESCALER_64 ((uint32_t)0x00000081)
#define PRESCALER_128 ((uint32_t)0x00000082)
#define IS_SPI_BAUDRATE_PRESCALER(PRESCALER) (((PRESCALER) == PRESCALER_2) ||  \
                                              ((PRESCALER) == PRESCALER_4) ||  \
                                              ((PRESCALER) == PRESCALER_8) ||  \
                                              ((PRESCALER) == PRESCALER_16) || \
                                              ((PRESCALER) == PRESCALER_32) || \
                                              ((PRESCALER) == PRESCALER_64) || \
                                              ((PRESCALER) == PRESCALER_128))

/**
 * @}SPI SSN  STATUS
 */
#define SPI_SSN_LOW (uint32_t)0x00000000
#define SPI_SSN_HIGH (uint32_t)0x00000001

/**
 * @}
 */
/*End of transmission interrupt flag*/
#define SPI_FLAG_SPIF (uint32_t)0x00000080
/*Write Conflict Interruption flag*/
#define SPI_FLAG_WCOL (uint32_t)0x00000040
/*Slave SSN error flag*/
#define SPI_FLAG_SSERR (uint32_t)0x00000020
/*Slave mode error flag*/
#define SPI_FLAG_MDF (uint32_t)0x00000010
#define IS_SPI_FLAG(FLAG) (((FLAG) == SPI_FLAG_SPIF) ||  \
                           ((FLAG) == SPI_FLAG_WCOL) ||  \
                           ((FLAG) == SPI_FLAG_SSERR) || \
                           ((FLAG) == SPI_FLAG_MDF))

/** @defgroup CR_CLEAR_Mask
 * @{
 */
#define CR_CLEAR_Mask (uint32_t)0x00000040

    /** @defgroup SPI_Exported_Functions
     * @{
     */
    void HAL_SPI_Init(SPI_InitTypeDef *SPI_InitStruct);
    void HAL_SPI_StructInit(SPI_InitTypeDef *SPI_InitStruct);
    void HAL_SPI_Cmd(SPI_TypeDef *SPIx, FunctionalState NewState);
    void HAL_SPI_SendData(SPI_TypeDef *SPIx, uint32_t Data);
    uint8_t HAL_SPI_ReceiveData(SPI_TypeDef *SPIx);
    void HAL_SPI_SSOutputCmd(SPI_TypeDef *SPIx, FunctionalState NewState);
    void HAL_SPI_DataSizeConfig(SPI_TypeDef *SPIx, uint32_t SPI_DataSize);
    FlagStatus HAL_SPI_GetFlagStatus(SPI_TypeDef *SPIx, uint16_t SPI_FLAG);

#ifdef __cplusplus
}
#endif

#endif /*__ASM31X_SPI_H */
