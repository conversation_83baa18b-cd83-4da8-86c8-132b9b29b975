#include "asm31ax_hal.h"

#ifdef HAL_PCA_MODULE_ENABLED

/* Includes ------------------------------------------------------------------*/

uint8_t pca_flg = 0;

void HAL_PCA_Init(PCA_InitTypeDef *PCA_InitStruct)
{
    int tmpreg = 0;
    uint8_t i;
    // Disable PCA
    PCA_InitStruct->Instance->CR &= ~PCA_CR_CR_Msk;

    tmpreg = PCA_InitStruct->TimCfg.ClkDiv | PCA_InitStruct->TimCfg.SleepRun;
    PCA_InitStruct->Instance->MOD &= ~(PCA_MOD_CPS_Msk | PCA_MOD_CIDL_Msk);
    PCA_InitStruct->Instance->MOD |= tmpreg;

    /*Setting counting overflow threshold*/
    PCA_InitStruct->Instance->CNT = PCA_InitStruct->TimCfg.Counter;

    // Set Ch
    for (i = 0; i < 5; i++)
    {
        if (PCA_InitStruct->Ch[i].En == ENABLE)
        {

            // GPIO配置

            PCA_InitStruct->Instance->CCAPMx[i] = 0x00;
            /*Set ECOM*/

            if (PCA_InitStruct->Ch[i].Mode == PCA_MODE_PWM)
            {
                PCA_InitStruct->Instance->CCAPxX[2 * i] = (PCA_InitStruct->Ch[i].Value & 0xFF);
                PCA_InitStruct->Instance->CCAPxX[2 * i + 1] = (PCA_InitStruct->Ch[i].Value & 0xFF);
                /*Set PWM*/

                PCA_InitStruct->Instance->CCAPMx[i] = PCA_MODE_PWM;
            }
            else if (PCA_InitStruct->Ch[i].Mode == PCA_MODE_HIGH_OUT)
            {
                // PCA_InitStruct->Instance->CCAPxX[2 * i] = (PCA_InitStruct->Ch[i].Value & 0xFF);
                // PCA_InitStruct->Instance->CCAPxX[2 * i + 1] = (PCA_InitStruct->Ch[i].Value >> 8);

                PCA_InitStruct->Instance->CCAPx[i] = PCA_InitStruct->Ch[i].Value;

                PCA_InitStruct->Instance->CCAPMx[i] = PCA_MODE_HIGH_OUT;
                PCA_InitStruct->Instance->CCAPO &= ~(1 << i);
                PCA_InitStruct->Instance->CCAPO |= (PCA_InitStruct->Ch[i].OutputVal << i);
            }
            else if (PCA_InitStruct->Ch[i].Mode == PCA_MODE_SOFT_TIM)
            {
                PCA_InitStruct->Instance->CCAPMx[i] = PCA_MODE_SOFT_TIM;
                PCA_InitStruct->Instance->CCAPx[i] = PCA_InitStruct->Ch[i].Value;
            }
            else if (PCA_InitStruct->Ch[i].Mode == PCA_MODE_CAPTURE)
            {
                PCA_InitStruct->Instance->CCAPMx[i] = PCA_MODE_CAPTURE;
            }

            PCA_InitStruct->Instance->POCR &= ~((PCA_POCR_POINV0_Msk | PCA_POCR_POE0_Msk) << i);
            PCA_InitStruct->Instance->POCR |= (((PCA_InitStruct->Ch[i].Inv << PCA_POCR_POINV0_Pos) | PCA_POCR_POE0_Msk) << i);
        }
        else
        {
            PCA_InitStruct->Instance->CCAPMx[i] = 0x00;
        }
    }

    PCA_InitStruct->Instance->CR |= PCA_CR_CR_Msk;
}

/**
 * @brief  Initializes the PCA_CountTimerInit
 *         the specified parameters in the PCA_CountTimerInitInitStruct.
 * @param  PCAx: Select the PCA peripheral.
 * @param  PCA_CountTimerInitInitStruct:pointer to an ADC_InitTypeDef structure that contains
 *         the configuration information for the specified ADC peripheral.
 * @retval None
 */
void HAL_PCA_CountTimerInit(PCA_TypeDef *PCAx, PCA_CountTimerInitTypeDef *PCA_CountTimerInitInitStruct)
{
    int tmpreg = 0;
    /*Check the parameters */
    assert_param(IS_PCA_ALL_PERIPH(PCAx));
    assert_param(IS_PCA_PRESCALE_DIV(PCA_CountTimerInitInitStruct->ClkDiv));
    assert_param(IS_PCA_CNT_VALUE(PCA_CountTimerInitInitStruct->Counter));

    /*Set clock */
    tmpreg = PCA_CountTimerInitInitStruct->ClkDiv;
    PCAx->MOD &= ~(pca_counterclk_mask);
    PCAx->MOD |= tmpreg;

    /*Setting counting overflow threshold*/
    PCAx->CNT = PCA_CountTimerInitInitStruct->Counter;
}

/**
 * @brief  enable the PCA_CountTimercmd
 *         the specified parameters in the PCA_CountTimerInitInitStruct.
 * @param  PCAx: Select the PCA peripheral.
 * @param  NewState: new state of the ADCx peripheral.
 *          This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_PCA_CountTimercmd(PCA_TypeDef *PCAx, FunctionalState NewState)
{
    /*Check the parameters */
    assert_param(IS_PCA_ALL_PERIPH(PCAx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*enable the PCA_CountTimercmd */
        PCAx->CR |= PCA_CR_ENABLE;
    }
    else
    {
        /*disable the PCA_CountTimercmd */
        PCAx->CR &= ~PCA_CR_ENABLE;
    }
}

/**
 * @brief  enable the PCA_Count Timer interrupt
 *         the specified parameters in the PCA_CountTimerInitInitStruct.
 * @param  PCAx: Select the PCA peripheral.
 * @param  NewState: new state of the ADCx peripheral.
 *          This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_PCA_CountTimerITCmd(PCA_TypeDef *PCAx, FunctionalState NewState)
{
    /*Check the parameters */
    assert_param(IS_PCA_ALL_PERIPH(PCAx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*enable the PCA_Count Timer interrupt*/
        PCAx->MOD |= PCA_TI_ENABLE;
    }
    else
    {
        /*disable the PCA_Count Timer interrupt*/
        PCAx->MOD &= ~PCA_TI_ENABLE;
    }
}

/**
 * @brief  Initializes the PCA capture function
 *         the specified parameters in the PCA_CountTimerInitInitStruct.
 * @param  PCAx: Set the PCA peripheral.
 * @param  PCA_ICInitStruct: pointer to an PCA_ICInitStruct structure that contains
 *         the configuration information for the specified PCA peripheral.
 * @retval None
 */
void HAL_PCA_CaptureInit(PCA_TypeDef *PCAx, PCA_ICInitTypeDef *PCA_ICInitStruct)
{
    int tmpreg = 0;
    /* Check the parameters */
    assert_param(IS_PCA_ALL_PERIPH(PCAx));
    assert_param(IS_PCA_CAP_Edge(PCA_ICInitStruct->PCA_ICPolarity));
    assert_param(IS_PCA_CHANNEL(PCA_ICInitStruct->PCA_Channel));
    assert_param(IS_PCA_PRESCALE_DIV(PCA_ICInitStruct->PCA_ICPrescaler));

    /* config pca parameter */
    tmpreg = PCA_ICInitStruct->PCA_ICPrescaler;
    PCAx->MOD &= ~(pca_counterclk_mask);
    PCAx->MOD |= tmpreg;

    /* config pca channel */

    PCAx->CCAPMx[PCA_ICInitStruct->PCA_Channel] &= ~PCA_CAP_ALL_Edge;
    PCAx->CCAPMx[PCA_ICInitStruct->PCA_Channel] |= PCA_ICInitStruct->PCA_ICPolarity;
}

/**
 * @brief  enable the PCA capture interrupt function
 * @param  PCAx: Set the PCA peripheral.
 * @param  PCA_ICInitStruct: pointer to an PCA_ICInitStruct structure that contains
 *         the configuration information for the specified PCA peripheral.
 * @param  NewState: new state of the ADCx peripheral.
 *         This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_PCA_CaptureITCmd(PCA_TypeDef *PCAx, PCA_ICInitTypeDef *PCA_ICInitStruct, FunctionalState NewState)
{
    /*Check the parameters */
    assert_param(IS_PCA_ALL_PERIPH(PCAx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));
    assert_param(IS_PCA_CAP_Edge(PCA_ICInitStruct->PCA_ICPolarity));
    assert_param(IS_PCA_CHANNEL(PCA_ICInitStruct->PCA_Channel));
    assert_param(IS_PCA_PRESCALE_DIV(PCA_ICInitStruct->PCA_ICPrescaler));

    /*Set parameters to channel*/

    if (NewState)
    {
        PCAx->CCAPMx[PCA_ICInitStruct->PCA_Channel] |= PCA_CCIE_ENABLE;
    }
    else
    {
        PCAx->CCAPMx[PCA_ICInitStruct->PCA_Channel] &= ~PCA_CCIE_ENABLE;
    }
}

/**
 * @brief  Set Soft Compare
 * @param  PCAx: Set the PCA peripheral.
 * @param  channel:
 * @param  value:
 * @retval None
 */
void HAL_PCA_SetSoftCompare(PCA_TypeDef *PCAx, PCA_Channel channel, uint16_t value)
{
    /*Check the parameters */
    assert_param(IS_PCA_ALL_PERIPH(PCAx));
    assert_param(IS_PCA_CHANNEL(channel));

    /*check channel*/

    PCAx->CCAPxX[channel * 2] = (uint8_t)value;
    PCAx->CCAPxX[channel * 2 + 1] = (uint8_t)(value >> 8);
    PCAx->CCAPMx[channel] &= ~PCA_MAT_ENABLE;
    PCAx->CCAPMx[channel] |= PCA_MAT_ENABLE;
}

/** @brief  enable the PCA capture interrupt function
 * @param  PCAx: Set the PCA peripheral.
 * @param  channel:
 * @param  value:
 * @retval None
 */
void HAL_PCA_ClearSoftCompare(PCA_TypeDef *PCAx, PCA_Channel channel, uint16_t value)
{
    /*Check the parameters */
    assert_param(IS_PCA_ALL_PERIPH(PCAx));
    assert_param(IS_PCA_CHANNEL(channel));

    PCAx->CCAPxX[channel * 2] = (uint8_t)value;
    PCAx->CCAPMx[channel] &= ~PCA_MAT_ENABLE;

    /*Set value to channel register*/
}

/** @brief  config high speed output function
 * @param  PCAx: Set the PCA peripheral.
 * @param  channel:
 * @param  value:
 * @param  bit:
 * @retval None
 */
void HAL_PCA_SetHighSpeed_output(PCA_TypeDef *PCAx, PCA_Channel channel, uint16_t value, uint8_t bit)
{
    /*Check the parameters */
    assert_param(IS_PCA_ALL_PERIPH(PCAx));
    assert_param(IS_PCA_CHANNEL(channel));

    if (bit != 0)
    {
        bit = 0x01;
    }

    /*check channel*/

    /*Set ECOM*/
    PCAx->CCAPxX[channel * 2] = (uint8_t)value;
    PCAx->CCAPxX[channel * 2 + 1] = (uint8_t)(value >> 8);
    /*Set MAT*/
    PCAx->CCAPMx[channel] &= ~PCA_MAT_ENABLE;
    PCAx->CCAPMx[channel] |= PCA_MAT_ENABLE;
    /*Set TOG*/
    PCAx->CCAPMx[channel] &= ~PCA_TOG_ENABLE;
    PCAx->CCAPMx[channel] |= PCA_TOG_ENABLE;

    /*Set TOG*/
    PCAx->CCAPMx[channel] &= ~PCA_CCIE_ENABLE;
    PCAx->CCAPMx[channel] |= PCA_CCIE_ENABLE;

    /*Setting the initial state*/
    PCAx->CCAPO &= ~(bit << channel);
    PCAx->CCAPO |= (bit << channel);

    /*Setting Output Enablation*/
    PCAx->POCR &= ~(PCA_OUTPUT_ENABLE << channel);
    PCAx->POCR |= (PCA_OUTPUT_ENABLE << channel);
}

/** @brief  set  Ch output
 * @param  PCAx: Set the PCA peripheral.
 * @param  channel:
 * @param  valuel:
 * @param  valueh:
 * @param  NewState: new state of the ADCx peripheral.
 *         This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_PCA_SetPwm_output(PCA_TypeDef *PCAx, PCA_Channel channel, uint8_t valuel, uint8_t valueh, FunctionalState NewStatus)
{
    /*Check the parameters */
    assert_param(IS_PCA_ALL_PERIPH(PCAx));
    assert_param(IS_PCA_CHANNEL(channel));

    /*check channel*/

    PCAx->CCAPMx[channel] = 0x00;
    /*Set ECOM*/
    PCAx->CCAPxX[channel * 2] = (uint8_t)valuel;
    PCAx->CCAPxX[channel * 2 + 1] = (uint8_t)valueh;
    /*Set PWM*/
    PCAx->CCAPMx[channel] &= ~PCA_PWM_ENABLE;
    PCAx->CCAPMx[channel] |= PCA_PWM_ENABLE;

    if (NewStatus == ENABLE)
    {
        /*Setting Output Enablation*/
        PCAx->POCR &= ~(PCA_OUTPUT_ENABLE << channel);
        PCAx->POCR |= (PCA_OUTPUT_ENABLE << channel);
        PCAx->POCR &= ~(PCA_PWMTOGGLE_ENABLE << channel);
        PCAx->POCR |= (PCA_PWMTOGGLE_ENABLE << channel);
    }
    else
    {
        PCAx->POCR &= ~(PCA_PWMTOGGLE_ENABLE << channel);
        PCAx->POCR &= ~(PCA_OUTPUT_ENABLE << channel);
    }
}

/** @brief  Clear the interrupt flag
 * @param  PCAx: Set the PCA peripheral.
 * @param  PCA_TI_FLAG:
 * @param  valuel:
 * @param  valueh:
 * @param  NewState: new state of the ADCx peripheral.
 *         This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_PCA_ITClear(PCA_TypeDef *PCAx, uint32_t PCA_TI_FLAG)
{
    /*Check the parameters */
    assert_param(IS_PCA_ALL_PERIPH(PCAx));
    /*Clear the PCA interrupt flag*/
    PCAx->INTCLR |= PCA_TI_FLAG;
}

#endif
