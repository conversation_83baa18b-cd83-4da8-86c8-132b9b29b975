// ALS 变色区间：1024 - （1024 + 200），滞环值30
static uint8_t code parm_data[] =
{
    // head
    0xAA, 0x55,
    // 参数文件版本号
    5,
    // 启动以后延迟若干周期再读ADC
    122,
    // 预留, 4字节
    0, 0, 0, 0,
    // ccode1, 4字节
    0x78, 0x23, 0x44, 0xB6,
    // cid, 12字节
    0x56, 0x33, 0x28, 0x52, 0x74, 0x66, 0x78, 0x84, 0x23, 0x44, 0x21, 0xB5,
    // ccode2, 4字节
    0xA3, 0x53, 0xC4, 0xE2,
    // counter, 4字节
    0, 0, 0, 0,
    // mode
    MODE_ENABLE,
    NTC_TYPE,
    // vec_max_out, ec最高工作电压, 10mv
    100,
    // vec_min_out, ec最低工作电压, 10mv
    0,
    // 眩光采样周期, 10ms
    2,
    // 环境光采样周期, 10ms
    2,
    // 发射率闭环控制周期, 10ms
    10,
    // 预留
    0,
    // ec_max_out, 最大输出PWM值
    85,
    // ec_min_out, 最小输出PWM值
    20,
    // v12_max
    0,
    // v12_min
    0,

    // 电压PID最大PWM值
    100,
    // 电压PID最小PWM值
    30,
    // 电压PID kp
    0, 10,
    // 电压PID ki
    10,
    // 电压PID kd
    10,
    // 电压PID iMax
    39, 16,

    // 反射率PID kp
    50, 0,
    // 反射率PID ki
    8,
    // 反射率PID kd
    16,
    // 反射率PID iMax
    32, 64,

    // 滤波参数（环境光均值）
    10,
    // 滤波参数（眩目光均值）
    20,
    // 滤波参数（环境光低通）
    5,
    // 滤波参数（眩目光低通）
    20,

    // B_dark, 眩目光标定最低值(ADC)
    0,
    // F_dark, 环境光标定最低值(ADC)
    0,
    // B_ref, 光电流标定参考值(ADC)
    0, 0,
    // B_light, 光电流标定值(ADC)
    0, 0,
    // dark_count, 标暗次数
    0,
    // light_count 标亮次数
    0,
    // pwm_step;
    0, 0,
    // gls_step;
    0,
    // reserved4[11];
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    // oecoveriec
    18,
    // tab_length
    6,
    // compensation_tab[6], 滞环带宽表
    60, 4, 8, 16, 32, 64,
    // als_tab[6], 环境光门限值 8bit
    7, 42, 77, 112, 147, 180,
    // target_tab[6], 目标ADC值
    8, 0, 0, 24, 0, 54, 0, 96, 0, 150, 0, 216,
    // temperature_tab[6], 温度补偿表
    6, 143, 5, 70, 4, 46, 3, 74, 2, 149, 2, 74,
    // tc_step, 温度补偿步进值
    1,
    // als_bits
    8,
    // 降额温度对应的ADC值
    3, 74,
    // check_foot
    0x55, 0xAA
};
