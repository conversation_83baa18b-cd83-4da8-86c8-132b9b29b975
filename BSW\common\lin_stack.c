#include "sys.h"
#include "lin_stack.h"
#include "rx_queue.h"

#if LIN_WAKE_0X3C_SUPPORT && (PRJ_NAME == D21)
#define TOTAL_CMD_LEN          8
#elif LIN_WAKE_0X3C_SUPPORT
#define TOTAL_CMD_LEN          7
#elif (PRJ_NAME == D21)
#define TOTAL_CMD_LEN          7
#else
#define TOTAL_CMD_LEN          6
#endif
static unsigned char ack_stat[10] = { 0 };
static unsigned char ack_len = 0;

uSIGGRP_IntrMirrResp uSiggrp_IntrMirrResp;
uINTRMIRRCMD uIntrMirrCmd;
ERROR_TYPE eError_Response = NONE_ERROR;

#if LIN_ERROR_RESPONSE
const TX_PARAM_LEN tx_param_lens[TOTAL_CMD_LEN] = 
{
	{PID_IrmmCem_Lin1Fr01,LEN_IrmmCem_Lin1Fr01}, //0x10
	{PID_IrmmCem_Lin1PartNrFr01,LEN_IrmmCem_Lin1PartNrFr01}, //0x12
	{PID_IrmmCem_Lin1SerNrFr01,LEN_IrmmCem_Lin1SerNrFr01}, //0x14
	{PID_IrmmCem_Lin1PartNrFr02,LEN_IrmmCem_Lin1PartNrFr02}, //0x16
	{PID_CemCem_Lin1Fr01,0}, //0x05
	{PID_CemCem_Lin1Fr03,0}, //0x17
#if (PRJ_NAME == D21)
	{PID_IRMM_01,LEN_IRMM_01}, //0x26 - IRMM状态反馈
#endif
#if LIN_WAKE_0X3C_SUPPORT
	{PID_MASTERReq,0},
#endif
};

unsigned char check_send_len(unsigned char pid)
{
	unsigned char cnt = 0;
	for(cnt = 0; cnt < TOTAL_CMD_LEN; cnt++)
	{
		if(tx_param_lens[cnt].master_cmd == pid)
		{
			return tx_param_lens[cnt].tx_len;
		}
	}
	return 0;
}
#endif

const RX_PARAM_LEN rx_param_lens[TOTAL_CMD_LEN] = 
{
	{PID_IrmmCem_Lin1Fr01,LEN_Rx_IrmmCem_Lin1Fr01},
	{PID_IrmmCem_Lin1PartNrFr01,LEN_Rx_IrmmCem_Lin1PartNrFr01},
	{PID_IrmmCem_Lin1SerNrFr01,LEN_Rx_IrmmCem_Lin1SerNrFr01},
	{PID_IrmmCem_Lin1PartNrFr02,LEN_Rx_IrmmCem_Lin1PartNrFr02},
	{PID_CemCem_Lin1Fr01,LEN_Rx_CemCem_Lin1Fr01},
	{PID_CemCem_Lin1Fr03,LEN_Rx_CemCem_Lin1Fr03},
#if (PRJ_NAME == D21)
	{PID_ZCUL_03,LEN_Rx_ZCUL_03},
	{PID_IRMM_01,LEN_Rx_IRMM_01},
#endif
#if LIN_WAKE_0X3C_SUPPORT
	{PID_MASTERReq,LEN_Rx_MASTEReq},
#endif
};

unsigned char check_return_len(unsigned char buffer)
{
	unsigned char cnt = 0;
	for(cnt = 0; cnt < TOTAL_CMD_LEN; cnt++)
	{
		if(rx_param_lens[cnt].master_cmd == buffer)
		{
			return rx_param_lens[cnt].rx_len;
		}
	}
	return 0;
}
unsigned char caculate_pid(unsigned char LIN_ID)
{
    unsigned char p0 = 0, p1 = 0;

    unsigned char PID = 0x00;

    p0 = (LIN_ID & 0x01) ^ ((LIN_ID & 0x02) >> 1) ^ ((LIN_ID & 0x04) >> 2) ^ ((LIN_ID & 0x10) >> 4); //????

    p0 = p0 & 0x01;

    p1 = ~(((LIN_ID & 0x02) >> 1) ^ ((LIN_ID & 0x08) >> 3) ^ ((LIN_ID & 0x10) >> 4) ^ ((LIN_ID & 0x20) >> 5));

    p1 = p1 & 0x01;

    PID = (p1 << 7) | (p0 << 6) | LIN_ID;
    return PID;
}

unsigned char caculate_checksum(unsigned char cmd, unsigned char *buf, unsigned char len)
{
	unsigned char cnt = 0;
	unsigned int checksum;
	if (cmd == 0x3C || cmd == 0x3D){
		checksum = 0;
	}else{
		checksum = cmd;
	}
	for(cnt = 0; cnt < len; cnt++)
	{
		//printf("%02x ",buf[cnt]);
		//UART_SendData(buf[cnt]);
		checksum = checksum + buf[cnt];
		if(checksum > 0xff)
		{
			checksum = checksum - 0xff;
		}
	}
	checksum = 0xff - checksum;
	return checksum;
}


unsigned char caculate_checksum_ex(unsigned char pid, unsigned char* buf, unsigned char len)
{
    unsigned char cnt = 0;
    unsigned char checksum = pid;
    for (cnt = 0; cnt < len; cnt++)
    {
        checksum = checksum + buf[cnt];
        if (checksum > 0xff)
        {
            checksum = checksum - 0x100 + 0x01;
        }
    }
    checksum = 0xff - checksum;
    return checksum;
}

ERROR_TYPE check_sum_error(unsigned char pid, unsigned char *buffer, unsigned char len, unsigned char checksum)
{
	unsigned char temp_sum = 0;
	if(NULL == buffer || 0 == len)
    {
        return CHECKSUM_ERROR;
    }
	temp_sum = caculate_checksum(pid,buffer,len);
	//printf("temp_sum %d,checksum %d",temp_sum,checksum);
	if(temp_sum == checksum)
	{
		
		return NONE_ERROR;
	} 
	else 
	{
		return CHECKSUM_ERROR;
	}
}

void lin_stack_single_rx(unsigned char buffer)
{
	switch(buffer)
	{
		case PID_IrmmCem_Lin1Fr01: //0x10
		{
			ack_stat[0] = uSiggrp_IntrMirrResp.IntrMirrRespIntrMirrDimPerc;
			ack_stat[1] = ((uSiggrp_IntrMirrResp.IntrMirrRespFailrandReserv.IntrMirrRespFailrandReservBIT.IntrMirrRespIntrMirrIntFailr==1?1:0)<<1);
			ack_stat[2] = 0x00|(((eError_Response!= NONE_ERROR)?1:0)<<7);
			ack_stat[3] = 0x00;
			ack_stat[4] = caculate_checksum(PID_IrmmCem_Lin1Fr01, ack_stat, (LEN_IrmmCem_Lin1Fr01 - 1));
			#if LIN_ERROR_RESPONSE
			lin_stack_send_isr(ack_stat, PID_IrmmCem_Lin1Fr01, LEN_IrmmCem_Lin1Fr01, TRUE);
			#else
			lin_stack_send(ack_stat, LEN_IrmmCem_Lin1Fr01, TRUE);
			#endif
			clean_error_type(NONE_ERROR);
		}
		break;
		case PID_IrmmCem_Lin1PartNrFr02: //0x16
		{
			//分析数据
			ack_stat[0] = Sig_PartNo10IRMMNr1;
			ack_stat[1] = Sig_PartNo10IRMMNr2;
			ack_stat[2] = Sig_PartNo10IRMMNr3;
			ack_stat[3] = Sig_PartNo10IRMMNr4;
			ack_stat[4] = Sig_PartNo10IRMMNr5;
			ack_stat[5] = Sig_PartNo10IRMMEndSgn1;
			ack_stat[6] = Sig_PartNo10IRMMEndSgn2;
			ack_stat[7] = Sig_PartNo10IRMMEndSgn3;
			ack_stat[8] = caculate_checksum(PID_IrmmCem_Lin1PartNrFr02,ack_stat,(LEN_IrmmCem_Lin1PartNrFr02 - 1));
			#if LIN_ERROR_RESPONSE
			lin_stack_send_isr(ack_stat, PID_IrmmCem_Lin1PartNrFr02, LEN_IrmmCem_Lin1PartNrFr02, TRUE);
			#else
			lin_stack_send(ack_stat,LEN_IrmmCem_Lin1PartNrFr02,TRUE);
			#endif
		}
		break;
		case PID_IrmmCem_Lin1PartNrFr01: //0x12
		{
			//分析数据
			ack_stat[0] = Sig_PartNoIRMMNr1;
			ack_stat[1] = Sig_PartNoIRMMNr2;
			ack_stat[2] = Sig_PartNoIRMMNr3;
			ack_stat[3] = Sig_PartNoIRMMNr4;
			ack_stat[4] = Sig_PartNoIRMMEndSgn1;
			ack_stat[5] = Sig_PartNoIRMMEndSgn2;
			ack_stat[6] = Sig_PartNoIRMMEndSgn3;
			ack_stat[7] = caculate_checksum(PID_IrmmCem_Lin1PartNrFr01,ack_stat,(LEN_IrmmCem_Lin1PartNrFr01 - 1));
			#if LIN_ERROR_RESPONSE
			lin_stack_send_isr(ack_stat, PID_IrmmCem_Lin1PartNrFr01, LEN_IrmmCem_Lin1PartNrFr01, TRUE);
			#else
			lin_stack_send(ack_stat,LEN_IrmmCem_Lin1PartNrFr01,TRUE);
			#endif
		}
		break;
		case PID_IrmmCem_Lin1SerNrFr01: //0x14
		{
			//分析数据
			ack_stat[0] = Sig_SerNoIRMMNr1;
			ack_stat[1] = Sig_SerNoIRMMNr2;
			ack_stat[2] = Sig_SerNoIRMMNr3;
			ack_stat[3] = Sig_SerNoIRMMNr4;
			ack_stat[4] = caculate_checksum(PID_IrmmCem_Lin1SerNrFr01,ack_stat,(LEN_IrmmCem_Lin1SerNrFr01 - 1));
			#if LIN_ERROR_RESPONSE
			lin_stack_send_isr(ack_stat, PID_IrmmCem_Lin1SerNrFr01, LEN_IrmmCem_Lin1SerNrFr01, TRUE);
			#else
			lin_stack_send(ack_stat,LEN_IrmmCem_Lin1SerNrFr01,TRUE);
			#endif
			//组装应答数据包

		}
		break;
#if (PRJ_NAME == D21)
		case PID_IRMM_01: //0xA6 - IRMM状态反馈(根据Excel表格定义)
		{
			// 根据Excel表格定义组装IRMM_01帧数据
			ack_stat[0] = uSiggrp_IntrMirrResp.IntrMirrRespIntrMirrDimPerc;  // 字节0: 调光百分比(0-255)

			// 字节1: 位0=内部故障, 位1=保留布尔值(初始值1)
			ack_stat[1] = ((uSiggrp_IntrMirrResp.IntrMirrRespFailrandReserv.IntrMirrRespFailrandReservBIT.IntrMirrRespIntrMirrIntFailr & 0x01)) |
			              ((uSiggrp_IntrMirrResp.IntrMirrRespFailrandReserv.IntrMirrRespFailrandReservBIT.IntrMirrRespResdBoolean & 0x01) << 1) |
			              ((uSiggrp_IntrMirrResp.IntrMirrRespFailrandReserv.IntrMirrRespFailrandReservBIT.IntrMirrRespResdUInt6 & 0x1F) << 2) |  // 位2-6: 保留UInt6(5位,初始值0x1F)
			              (((eError_Response != NONE_ERROR) ? 1 : 0) << 7);  // 位7: 错误响应

			// 字节2-7: 保留字节，填充0
			ack_stat[2] = 0x00;
			ack_stat[3] = 0x00;
			ack_stat[4] = 0x00;
			ack_stat[5] = 0x00;
			ack_stat[6] = 0x00;

			// 字节7: 校验和
			ack_stat[7] = caculate_checksum(PID_IRMM_01, ack_stat, (LEN_IRMM_01 - 1));

			#if LIN_ERROR_RESPONSE
			lin_stack_send_isr(ack_stat, PID_IRMM_01, LEN_IRMM_01, TRUE);
			#else
			lin_stack_send(ack_stat, LEN_IRMM_01, TRUE);
			#endif
			clean_error_type(NONE_ERROR);
		}
		break;
#endif
		default:
		{
			unsigned char id;
			if(id==ID_IrmmCem_Lin1Fr01 || id==ID_IrmmCem_Lin1PartNrFr01 || id==ID_IrmmCem_Lin1SerNrFr01
			|| id==ID_IrmmCem_Lin1PartNrFr02 || id==ID_CemCem_Lin1Fr01)
			{
				if(caculate_pid(id) != buffer)
				{
					set_error_type(PID_ERROR);
				}else{
					//never comein
				}
			}else{
				//other id
			}
		}
		break;
	}
	
}

void set_error_type(ERROR_TYPE err_type)
{
	if(err_type != NONE_ERROR)
	{
		eError_Response |= (1<<err_type);
	}
	
}

void clean_error_type(ERROR_TYPE err_type)
{
	if(err_type != NONE_ERROR)
	{
		eError_Response &= (~(1<<err_type));
	}
	else 
	{
		eError_Response = NONE_ERROR;
	}
	
}

#if LIN_WAKE_SUPPORT
static unsigned char g_lin_need_sleep = 0;
uint8_t lin_stack_sleep_flag(void)
{
	if(g_lin_need_sleep == 1){
		printf("g_lin_need_sleep == 1, sleep\r\n");
	}
	return g_lin_need_sleep;
}

void set_lin_stack_sleep_flag(unsigned char flag)
{
	printf("set g_lin_need_sleep=%bu\r\n", g_lin_need_sleep);
	g_lin_need_sleep = flag;
}
#endif

void lin_stack_recv(unsigned char *buffer, unsigned char len)
{
    unsigned char cnt = 0;
    if(NULL == buffer || 2 > len)
    {
        return;
    }
	if(CHECKSUM_ERROR == check_sum_error(buffer[0], (buffer+1), (len-2), buffer[(len - 1)]))
	{
		set_error_type(CHECKSUM_ERROR);  //出错赋值
		return;
	}
	//解析一下CMD
	switch(buffer[cnt])
	{
#if (PRJ_NAME == D21)
		case PID_ZCUL_03: //0x33 - 内后视镜防眩命令(根据Excel表格定义)
		{       
			// 根据Excel表格定义，从字节0开始解析各信号
			uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrDimSnvty = (buffer[cnt + 1] & 0x03);        // 字节0，位0-1，长度2
			uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdDrvrSide = (buffer[cnt + 1] & 0x04) >> 2;           // 字节0，位2，长度1
			uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrAsyFanCmpMag = (buffer[cnt + 1] & 0x38) >> 3; // 字节0，位3-5，长度3
			uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrDiagcRst = (buffer[cnt + 1] & 0x40) >> 6;   // 字节0，位6，长度1
			uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrEna = (buffer[cnt + 1] & 0x80) >> 7;        // 字节0，位7，长度1
			uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrInhbDim = (buffer[cnt + 2] & 0x01);         // 字节1，位0，长度1
			uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrWindHeatrCmpMag = (buffer[cnt + 2] & 0x0E) >> 1; // 字节1，位1-3，长度3
		}
		break;
#else
		case PID_CemCem_Lin1Fr01: //0x05 - 保留原有实现用于其他项目
		{       
			uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdDrvrSide = (buffer[cnt + 5] & 0x04) >> 2;//start 34 len 1
			uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrAsyFanCmpMag = (buffer[cnt + 5] & 0x80) >> 7;//start 39 len 1
			uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrDiagcRst = (buffer[cnt + 5] & 0x40) >> 6;//start 38 len 1
			uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrDimSnvty = (buffer[cnt + 5] & 0x03);   //start 32 len 2
			uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrEna = (buffer[cnt + 5] & 0x10) >> 4;//start 36 len 1
			uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrInhbDim = (buffer[cnt + 5] & 0x20) >> 5;//start 37 len 1
			uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrWindHeatrCmpMag = (buffer[cnt + 5] & 0x08) >> 3;//start 35 len 1
		}
		break;
#endif
		case PID_CemCem_Lin1Fr03://home link //0x17
		{
			
			//不需要home link信号处理
		}
		break;
#if LIN_WAKE_0X3C_SUPPORT
		case PID_MASTERReq: //0x3C
		{
			//分析数据
			//UART_SendData(0x3C);
			unsigned char needSleep = 1;
			if (buffer[cnt + 1] == 0)
			{
				for (cnt = 1;cnt < 7;cnt++)
				{
					if (buffer[cnt + 1] != 0xFF)
					{
						//sleep cancel
						needSleep = 0;
						break;
					}
				}
				if (needSleep == 1)
				{
					//need sleep
					g_lin_need_sleep = 1;
				}
			}
		}
		break;
#endif
		default:
		{
		}
		break;
	}   
}
void lin_stack_send(unsigned char* buffer, unsigned char len, unsigned char clear_fifo)
{
    unsigned char cnt = 0;
    if (NULL == buffer || 0 == len)
    {
        return;
    }
    
    lin_send(buffer,len);
    

    if (TRUE == clear_fifo)
    {
        clear_rx_queue();
    }
}

#if LIN_ERROR_RESPONSE
extern unsigned char tx_rev[QUEUE_SIZE];
void lin_stack_send_isr(unsigned char* buffer, unsigned char pid, unsigned char len, unsigned char clear_fifo)
{
    unsigned char i;

    if (NULL == buffer || 0 == len)
    {
        return;
    }

    tx_rev[0] = pid;
    set_tx_rev_len(check_send_len(pid));
    UART_Send_Data(UART1, buffer[0]);  //send first byte
    set_tx_queue_used_len(len);
    set_tx_queue_pos(1);
    for (i=0; i<get_tx_queue_used_len(); i++){
        tx_buf[i] = *(buffer++);
    }


    if (TRUE == clear_fifo)
    {
        clear_rx_queue();
    }
}
#endif

//-------------------------------LIN API-------------------------------
uint8_t get_IntrMirrCmdIntrMirrEna(void) //IntrMirrCmdIntrMirrEna
{
    return uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrEna;
}

uint8_t get_IntrMirrRespIntrMirrIntFailr(void) //IntrMirrRespIntrMirrIntFailr
{
	return uSiggrp_IntrMirrResp.IntrMirrRespFailrandReserv.IntrMirrRespFailrandReservBIT.IntrMirrRespIntrMirrIntFailr;
}

uint8_t get_IntrMirrCmdIntrMirrInhbDim(void) //IntrMirrCmdIntrMirrInhbDim
{
    return uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrInhbDim;
}

uint8_t get_IntrMirrCmdIntrMirrDimSnvty(void) //IntrMirrCmdIntrMirrDimSnvty
{
    return uIntrMirrCmd.INTRMIRRBIT.IntrMirrCmdIntrMirrDimSnvty;
}

//-------------

void set_IntrMirrRespIntrMirrDimPerc(unsigned char percent) //IntrMirrRespIntrMirrDimPerc
{
    uSiggrp_IntrMirrResp.IntrMirrRespIntrMirrDimPerc = percent;
}

void set_IntrMirrRespIntrMirrIntFailr(unsigned char failed) //IntrMirrRespIntrMirrIntFailr
{
	if(failed == 0x01){
		uSiggrp_IntrMirrResp.IntrMirrRespFailrandReserv.IntrMirrRespFailrandReservBIT.IntrMirrRespIntrMirrIntFailr = 1;
	}else{
		uSiggrp_IntrMirrResp.IntrMirrRespFailrandReserv.IntrMirrRespFailrandReservBIT.IntrMirrRespIntrMirrIntFailr = 0;
	}
}
