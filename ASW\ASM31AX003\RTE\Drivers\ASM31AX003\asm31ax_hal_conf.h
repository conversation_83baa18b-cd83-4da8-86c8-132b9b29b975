#ifndef ASM31AX_HAL_CONF_H
#define ASM31AX_HAL_CONF_H

#ifdef __cplusplus
extern "C"
{
#endif

    /* Exported types ------------------------------------------------------------*/
    /* Exported constants --------------------------------------------------------*/

    /* ########################## Module Selection ############################## */
    /**
     * @brief This is the list of modules to be used in the HAL driver
     */

#define HAL_MODULE_ENABLED
#define HAL_ADC_MODULE_ENABLED
#define HAL_AWK_MODULE_ENABLED
#define HAL_ADVTIM_MODULE_ENABLED
#define HAL_BEEPER_MODULE_ENABLED
#define HAL_CLKTRIM_MODULE_ENABLED
#define HAL_DELAY_MODULE_ENABLED
#define HAL_FLASH_MODULE_ENABLED
#define HAL_GPIO_MODULE_ENABLED
#define HAL_I2C_MODULE_ENABLED
#define HAL_IWDG_MODULE_ENABLED
#define HAL_WWDG_MODULE_ENABLED
#define HAL_RCC_MODULE_ENABLED
#define HAL_RTC_MODULE_ENABLED
#define HAL_SYSCON_MODULE_ENABLED
#define HAL_SPI_MODULE_ENABLED
#define HAL_TIMER_MODULE_ENABLED
#define HAL_UART_MODULE_ENABLED
#define HAL_LPTIMER_MODULE_ENABLED
#define HAL_LPUART_MODULE_ENABLED
#define HAL_LVD_MODULE_ENABLED
#define HAL_OWIRE_MODULE_ENABLED
#define HAL_PCA_MODULE_ENABLED
#define HAL_RTC_MODULE_ENABLED
#define HAL_VC_MODULE_ENABLED

#ifdef HAL_AWK_MODULE_ENABLED
#include "asm31ax_hal_awk.h"
#endif /* HAL_PMU_MODULE_ENABLED */

#ifdef HAL_BEEPER_MODULE_ENABLED
#include "asm31ax_hal_beeper.h"
#endif /* HAL_PMU_MODULE_ENABLED */

#ifdef HAL_CLKTRIM_MODULE_ENABLED
#include "asm31ax_hal_clktrim.h"
#endif /* HAL_PMU_MODULE_ENABLED */

#ifdef HAL_DELAY_MODULE_ENABLED
#include "asm31ax_hal_delay.h"
#endif /* HAL_PMU_MODULE_ENABLED */

#ifdef HAL_LOWPOWER_MODULE_ENABLED
#include "asm31ax_hal_lowpower.h"
#endif /* HAL_PMU_MODULE_ENABLED */

    /* HAL_PMU_MODULE_ENABLED */

#ifdef HAL_RCC_MODULE_ENABLED
#include "asm31ax_hal_rcc.h"
#endif /* HAL_RCC_MODULE_ENABLED */

#ifdef HAL_GPIO_MODULE_ENABLED
#include "asm31ax_hal_gpio.h"
#endif /* HAL_GPIO_MODULE_ENABLED */

#ifdef HAL_EXTI_MODULE_ENABLED
#include "asm31x_hal_exti.h"
#endif /* HAL_EXTI_MODULE_ENABLED */

#ifdef HAL_ADC_MODULE_ENABLED
#include "asm31ax_hal_adc.h"
#endif /* HAL_ADC_MODULE_ENABLED */

#ifdef HAL_CRC_MODULE_ENABLED
#include "asm31ax_hal_crc.h"
#endif /* HAL_CRC_MODULE_ENABLED */

#ifdef HAL_FLASH_MODULE_ENABLED
#include "asm31ax_hal_flash.h"
#endif /* HAL_FLASH_MODULE_ENABLED */

#ifdef HAL_I2C_MODULE_ENABLED
#include "asm31ax_hal_i2c.h"
#endif /* HAL_I2C_MODULE_ENABLED */

#ifdef HAL_WDG_MODULE_ENABLED
#include "asm31ax_hal_wdg.h"
#endif /* HAL_IWDG_MODULE_ENABLED */

#ifdef HAL_RTC_MODULE_ENABLED
#include "asm31ax_hal_rtc.h"
#endif /* HAL_RTC_MODULE_ENABLED */

#ifdef HAL_SPI_MODULE_ENABLED
#include "asm31ax_hal_spi.h"
#endif /* HAL_SSP_MODULE_ENABLED */

#ifdef HAL_TIMER_MODULE_ENABLED
#include "asm31ax_hal_timer.h"
#endif /* HAL_TIM_MODULE_ENABLED */

#ifdef HAL_LPTIMER_MODULE_ENABLED
#include "asm31ax_hal_lptimer.h"
#endif /* HAL_TIM_MODULE_ENABLED */

#ifdef HAL_LPUART_MODULE_ENABLED
#include "asm31ax_hal_lpuart.h"
#endif /* HAL_TIM_MODULE_ENABLED */

#ifdef HAL_LVD_MODULE_ENABLED
#include "asm31ax_hal_lvd.h"
#endif /* HAL_TIM_MODULE_ENABLED */

#ifdef HAL_UART_MODULE_ENABLED
#include "asm31ax_hal_uart.h"
#endif /* HAL_UART_MODULE_ENABLED */

#ifdef HAL_DEBUG_MODULE_ENABLED
#include "asm31ax_hal_debug.h"
#endif /* HAL_UART_MODULE_ENABLED */

#ifdef HAL_ADVTIM_MODULE_ENABLED
#include "asm31ax_hal_advtim.h"
#endif

#ifdef HAL_WWDG_MODULE_ENABLED
#include "asm31ax_hal_wwdg.h"
#endif /* HAL_WWDG_MODULE_ENABLED */

#ifdef HAL_IWDG_MODULE_ENABLED
#include "asm31ax_hal_iwdg.h"
#endif /* HAL_IWDG_MODULE_ENABLED */

#ifdef HAL_OWIRE_MODULE_ENABLED
#include "asm31ax_hal_owire.h"
#endif

#ifdef HAL_VC_MODULE_ENABLED
#include "asm31ax_hal_vc.h"
#endif

#ifdef HAL_PCA_MODULE_ENABLED
#include "asm31ax_hal_pca.h"
#endif

#ifdef HAL_RTC_MODULE_ENABLED
#include "asm31ax_hal_rtc.h"
#endif

#ifdef HAL_SYSCON_MODULE_ENABLED
#include "asm31ax_hal_syscon.h"
#endif

/* Exported macro ------------------------------------------------------------*/
#ifdef USE_FULL_ASSERT
/**
 * @brief  The assert_param macro is used for function's parameters check.
 * @param  expr If expr is false, it calls assert_failed function
 *         which reports the name of the source file and the source
 *         line number of the call that failed.
 *         If expr is true, it returns no value.
 * @retval None
 */
#define assert_param(expr) ((expr) ? (void)0U : assert_failed((uint8_t *)__FILE__, __LINE__))
    /* Exported functions ------------------------------------------------------- */
    void assert_failed(uint8_t *file, uint32_t line);
#else
#define assert_param(expr) ((void)0U)
#endif /* USE_FULL_ASSERT */

#ifdef __cplusplus
    extern
}
#endif

#endif
