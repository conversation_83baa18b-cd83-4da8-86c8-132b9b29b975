
#include "asm31ax_hal.h"

#ifdef HAL_BEEPER_MODULE_ENABLED

/* Includes ------------------------------------------------------------------*/

void BEEPER_Init(Beeper_InitTypeDef *Beeper_InitStruct)
{

    RCC->UNLOCK = 0x55aa6699;
    if (Beeper_InitStruct->ClkSel == BEEPER_CLKSEL_LICR)
    {
        RCC->SYSCLKCR |= (0x5a690000 | (RCC_SYSCLKCR_LIRCEN_ENABLE << RCC_SYSCLKCR_LIRCEN_Pos));
    }
    else if (Beeper_InitStruct->ClkSel == BEEPER_CLKSEL_HXT)
    {
        RCC->SYSCLKCR |= (0x5a690000 | (RCC_SYSCLKCR_HXTEN_ENABLE << RCC_SYSCLK<PERSON>_HXTEN_Pos));
    }

    RCC->PCLKEN |= RCC_PCLKEN_BEEPCKEN_Msk;

    RCC->UNLOCK = 0x55aa6698;

    Beeper_InitStruct->Instance->CSR &= ~BEEPER_CSR_CLKSEL_Msk;
    Beeper_InitStruct->Instance->CSR |= Beeper_InitStruct->ClkSel;

    Beeper_InitStruct->Instance->CSR &= ~BEEPER_CSR_DIV_Msk;
    Beeper_InitStruct->Instance->CSR |= Beeper_InitStruct->ClkDiv;

    Beeper_InitStruct->Instance->CSR &= ~BEEPER_CSR_BEEPSEL_Msk;
    Beeper_InitStruct->Instance->CSR |= Beeper_InitStruct->BeepSel;

    Beeper_InitStruct->Instance->CSR |= BEEPER_CSR_BEEPEN_Msk;
}

/**
 * @brief  Deinitializes the BEEPER peripheral registers to their default reset values.
 * @param  None
 * @retval None
 */
void BEEPER_DeInit(void)
{
    /*Enable Beeper reset state */
    RCC_PeriphResetCmd(RCC, RCC_APBPeriph_BEEPRST, ENABLE);
    /*Release Beeper from reset state */
    RCC_PeriphResetCmd(RCC, RCC_APBPeriph_BEEPRST, DISABLE);
}

/** @brief Set Beeper_Prescaler
 * @param  BEEPERx: Selects the BEEPER peripheral
 * @param  BeeperPrescaler: Selects the BEEPER Prescaler
 * @retval None
 */
void BEEPER_SetPrescaler(BEEPER_TypeDef *BEEPERx, uint32_t BeeperPrescaler)
{
    /* Check the parameters */
    assert_param(IS_BEEPER_ALL_PERIPH(BEEPERx));
    assert_param(IS_BEEPER_PRESCALER(BeeperPrescaler));
    /*Set the value to the CSR register*/
    BEEPERx->CSR &= ~BEEPERDIV_MASK;
    BEEPERx->CSR |= BeeperPrescaler;
}

/** @brief  BEEPER select prescaler/蜂鸣器输出 BEEP_O 频率选择
 * @param  BEEPERx: Selects the BEEPER peripheral
 * @param  PreClk:  Selects the BEEPER peripheral output frequency
 * @retval None
 */
void BEEPER_SelectPrescaler(BEEPER_TypeDef *BEEPERx, uint32_t PreClk)
{
    /* Check the parameters */
    assert_param(IS_BEEPER_ALL_PERIPH(BEEPERx));
    assert_param(IS_BEEPER_CLOCKSEL(PreClk));

    /*Set the output frequency of beeper*/
    BEEPERx->CSR &= ~BEEPERSELECT_MASK;
    BEEPERx->CSR |= PreClk;
}

/** @brief Enables BEEPER peripheral
 * @param  BEEPERx: Selects the BEEPER peripheral
 * @param  NewState: new state of the WWDG peripheral.
 * This parameter can be: ENABLE or DISABLE.
 */
void BEEPER_Cmd(BEEPER_TypeDef *BEEPERx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /* Enable beeper function */
        BEEPERx->CSR |= BEEPER_ENABLE;
    }
    else
    {
        /* Disable beeper function */
        BEEPERx->CSR &= ~BEEPER_ENABLE;
    }
}

/** @brief Set Beeper Clock
 * @param  BEEPERx: Selects the BEEPER peripheral
 * @param  CLK: Selects the BEEPER peripheral
 * @{
 */
void BEEPER_SetClock(BEEPER_TypeDef *BEEPERx, uint32_t CLK)
{
    /* Check the parameters */
    assert_param(IS_BEEPER_SELECLOCK(CLK));

    /* Set Clock  parameters to the CSR register*/
    BEEPERx->CSR &= ~BEEPERCLOCK_MASK;
    BEEPERx->CSR |= CLK;
}

#endif
