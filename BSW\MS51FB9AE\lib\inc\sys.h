#ifndef __SYS_H
#define __SYS_H	

#include "MS51_16K.h"
#include "Function_Define.h"
#include "SFR_Macro.h"
#include "Common.h"


typedef bit                   BIT;
typedef unsigned char         uint8_t;
typedef unsigned int          uint16_t;
typedef unsigned long         uint32_t;

typedef char         int8_t;
typedef int          int16_t;
typedef long         int32_t;

//#define INT8   int8_t           
//#define INT16  int16_t           
//#define INT32  int32_t          
	

//typedef unsigned char         u8;
//typedef unsigned int          u16;
//typedef unsigned long         u32;

typedef void(*VoidFuncVoid)(void);

//#undef NULL
//#define NULL 0                 



extern bit BIT_TMP;


#endif
