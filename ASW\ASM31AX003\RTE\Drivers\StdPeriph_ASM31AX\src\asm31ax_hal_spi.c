#include "asm31ax_hal.h"

#ifdef HAL_SPI_MODULE_ENABLED

/* Includes ------------------------------------------------------------------*/

/**
 * @brief  Deinitializes the SPI peripheral registers to their default
 *         reset values .
 * @param  SPIx:Select the SPI peripheral.
 * @retval None
 */
void HAL_SPI_DeInit(SPI_TypeDef *SPIx)
{
    /* Enable SPI reset state */
    RCC_PeriphResetCmd(RCC, RCC_APBPeriph_SPIRST, ENABLE);
    /* Release SPI from reset state */
    RCC_PeriphResetCmd(RCC, RCC_APBPeriph_SPIRST, DISABLE);
}

/**
 * @brief  Initializes the SPIx peripheral according to the specified
 *         parameters in the SPI_InitStruct.
 * @param  SPI_InitStruct: pointer to a SPI_InitTypeDef structure that
 *         contains the configuration information for the specified SPI peripheral.
 * @retval None
 */
void HAL_SPI_Init(SPI_InitTypeDef *SPI_InitStruct)
{
    uint32_t tmpreg = 0;
    /* Check the SPI parameters */
    assert_param(IS_SPI_ALL_PERIPH(SPI_InitStruct->Instance));
    assert_param(IS_SPI_MODE(SPI_InitStruct->Mode));
    assert_param(IS_SPI_CPOL(SPI_InitStruct->CPOL));
    assert_param(IS_SPI_CPHA(SPI_InitStruct->CPHA));
    assert_param(IS_SPI_NSS(SPI_InitStruct->SPI_NSS));
    assert_param(IS_SPI_BAUDRATE_PRESCALER(SPI_InitStruct->Prescaler));

    /*------------------------- SPIx CR1 Configuration ------------------------*/
    /* Configure SPIx: direction, BaudRate prescaler
     master/salve mode, CPOL and CPHA */
    /* Set CPOL bit according to CPOL value */
    /* Set CPHA bit according to CPHA value */
    tmpreg |= (uint32_t)((SPI_InitStruct->Mode) |
                         (SPI_InitStruct->CPOL) |
                         (SPI_InitStruct->CPHA) |
                         (SPI_InitStruct->Prescaler));
    /* Write to SPIx CR */
    SPI_InitStruct->Instance->CR = tmpreg;

    SPI_InitStruct->Instance->CR |= SPI_SPEN_ENABLE;
}

/**
 * @brief  Fills each SPI_InitStruct member with its default value.
 * @param  SPI_InitStruct : pointer to a SPI_InitTypeDef structure which will be initialized.
 * @retval None
 */
void HAL_SPI_StructInit(SPI_InitTypeDef *SPI_InitStruct)
{
    /*--------------- Reset SPI init structure parameters values -----------------*/
    /* initialize the Mode member */
    SPI_InitStruct->Mode = SPI_MODE_SLAVE;
    /* Initialize the CPOL member */
    SPI_InitStruct->CPOL = SPI_CPOL_LOW;
    /* Initialize the CPHA member */
    SPI_InitStruct->CPHA = SPI_CPHA_1EDGE;
    /* Initialize the Prescaler member */
    SPI_InitStruct->Prescaler = PRESCALER_2;
}

/**
 * @brief  Enables or disables the specified SPI peripheral.
 * @param  SPIx: Select the SPI peripheral
 * @param  NewState: new state of the SPIx peripheral.
 *   This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_SPI_Cmd(SPI_TypeDef *SPIx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_SPI_ALL_PERIPH(SPIx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));
    if (NewState != DISABLE)
    {
        /* Enable the selected SPI peripheral */
        SPIx->CR |= SPI_SPEN_ENABLE;
    }
    else
    {
        /* Disable the selected SPI peripheral */
        SPIx->CR &= ~SPI_SPEN_ENABLE;
    }
}

/**
 * @brief  Transmits a Data through the SPI peripheral.
 * @param  SPIx:  Select the SPI peripheral
 * @param  Data : Data to be transmitted.
 * @retval None
 */
void HAL_SPI_SendData(SPI_TypeDef *SPIx, uint32_t Data)
{
    /*Check the parameters */
    assert_param(IS_SPI_ALL_PERIPH(SPIx));
    /*Write in the DR register the data to be sent */
    SPIx->DATA = Data;
    while ((SPIx->SR & SPI_FLAG_SPIF) == 0X00)
    {
    }
}

/**
 * @brief  Returns the most recent received data by the SPI peripheral.
 * @param  SPIx: Select the SPI peripheral
 * @retval The value of the received data.
 */
uint8_t HAL_SPI_ReceiveData(SPI_TypeDef *SPIx)
{
    /*Return the data in the DR register */
    return (SPIx->DATA);
}

/**
 * @brief  Enables or disables the SSN output for the selected SPI.
 * @param  SPIx: Select the SPI peripheral
 * @param  NewState: new state of the SPI SSN output.
 *   This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_SPI_SSOutputCmd(SPI_TypeDef *SPIx, FunctionalState NewState)
{
    /*Check the parameters */

    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*Enable the selected SPI SS output */
        SPIx->SSN &= SPI_SSN_LOW;
    }
    else
    {
        /*Disable the selected SPI SS output */
        SPIx->SSN |= SPI_SSN_HIGH;
    }
}

/**
 * @brief  Checks whether the specified SPI flag is set or not.
 * @param  SPIx: Select the SPI peripheral
 * @param  SPI_FLAG: specifies the SPI flag to check.
 *   This parameter can be one of the following values:
 *     @arg SPI_FLAG_SPIF: End of transmission interrupt flag
 *     @arg SPI_FLAG_WCOL: Write Conflict Interruption flag
 *     @arg SPI_FLAG_SSERR: Slave SSN error flag
 *     @arg SPI_FLAG_MDF:  Slave mode error flag
 * @retval The new state of SPI_FLAG (SET or RESET).
 */
FlagStatus HAL_SPI_GetFlagStatus(SPI_TypeDef *SPIx, uint16_t SPI_FLAG)
{
    FlagStatus bitstatus = RESET;
    /*Check the parameters */
    assert_param(IS_SPI_ALL_PERIPH(SPIx));
    assert_param(IS_SPI_FLAG(SPI_FLAG));
    /*Check the status of the specified SPI flag */
    if ((SPIx->SR & SPI_FLAG) != (uint16_t)RESET)
    {
        /*SPI_FLAG is set */
        bitstatus = SET;
    }
    else
    {
        /*SPI_FLAG is reset */
        bitstatus = RESET;
    }
    /*Return the SPI_FLAG status */
    return bitstatus;
}

#endif
