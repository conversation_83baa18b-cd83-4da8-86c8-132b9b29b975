#include "asm31ax.h"
#include "asm31ax_hal_rcc.h"

uint32_t SystemCoreClock = 8000000;
__I uint8_t AHBPrescTable[16] = {0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 3, 4, 6, 7, 8, 9};
extern void SysClkSourceSwitchTo_HXT(void);
void SysClkSourceSwitchTo_HIRC_24MHz(void);
void SystemCoreClockUpdate(void);

extern uint32_t g_time;

static void delay(uint32_t t)
{
    uint32_t i, j;

    for (i = 0; i < t; i++)
    {
        for (j = 0; j < 0xFFEE; j++)
        {
        }
    }
}

uint32_t Get_Sys_Tick(void)
{
    return g_time;
}
/**
 * @}
 */

/**
 * @brief  Setup the microcontroller system.
 *         Initialize the Embedded Flash Interface, the PLL and update the
 *         SystemCoreClock variable.
 * @param  None
 * @retval None
 */
__weak void SystemInit(void)
{
    SystemCoreClockUpdate();

    // SysTick_Config(24000);
}

/**
 * @brief  Update SystemCoreClock according to Clock Register Values
 *         The SystemCoreClock variable contains the core clock (HCLK), it can
 *         be used by the user application to setup the SysTick timer or configure
 *         other parameters.
 *
 * @note   Each time the core clock (HCLK) changes, this function must be called
 *         to update SystemCoreClock variable value. Otherwise, any configuration
 *         based on this variable will be incorrect.
 *
 * @note   - The system frequency computed by this function is not the real
 *           frequency in the chip. It is calculated based on the predefined
 *           constant and the selected clock source:
 *
 *           - If SYSCLK source is HSI, SystemCoreClock will contain the HSI_VALUE(*)
 *
 *           - If SYSCLK source is HSE, SystemCoreClock will contain the HSE_VALUE(**)
 *
 *           - If SYSCLK source is PLL, SystemCoreClock will contain the HSE_VALUE(**)
 *             or HSI_VALUE(*) multiplied/divided by the PLL factors.
 *
 *         (*) HSI_VALUE is a constant defined in stm32f0xx.h file (default value
 *             8 MHz) but the real value may vary depending on the variations
 *             in voltage and temperature.
 *
 *         (**) HSE_VALUE is a constant defined in stm32f0xx.h file (default value
 *              8 MHz), user has to ensure that HSE_VALUE is same as the real
 *              frequency of the crystal used. Otherwise, this function may
 *              have wrong result.
 *
 *         - The result of this function could be not correct when using fractional
 *           value for HSE crystal.
 * @param  None
 * @retval None
 */
void SystemCoreClockUpdate(void)
{
    RCC->HCLKEN = 0xffffffff; // flash,crc,gpioa/b/c/d
    RCC->PCLKEN = 0xffffffff; // uart,i2c,spi,pca,tim,btim, pwm? iwdt,dwdt,rtc,awk,trim

    /* CLK TRIMING */
    RCC->UNLOCK = 0x55aa6699; // Unlock
    RCC->HIRCCR = 0x5a690EE4;
    RCC->UNLOCK = 0x55aa6698; // Lock

    RCC_LIRCTrim(RCC, RCC_LIRC_TRIM_32768HZ);
    SysClkSourceSwitchTo_HIRC_24MHz();
}

void SysClkSourceSwitchTo_HIRC_24MHz(void)
{

    uint32_t trimValue = 0x00000EE4;
    uint8_t ClkStatus = 0;

    /* 获取HIRC, HXT, LIRC使能状态 */
    ClkStatus = RCC_GetSysclkSource(RCC);

    /* 判断HIRC时钟是否开启 */
    if ((ClkStatus & 0x01) != 0x01)
    {
        RCC_SysclkCmd(RCC, RCC_SYSCLKSource_HIRC, ENABLE); // 使能HIRC时钟
    }

    /* HIRC时钟频率调整及校准 */
    RCC_HIRCTrim(RCC, RCC_HIRC_TRIM_24MHZ);

    /* 等待HIRC时钟稳定 */
    RCC_WaitForHIRCStable(RCC);
}

void SysClkSourceSwitchTo_HXT(void)
{

    ErrorStatus Status = ERROR;
    uint8_t ClkStatus = 0;
    delay(200);

    /* 使能外部时钟 */
    RCC_SetHXTPort(RCC, ENABLE);

    /* 获取HIRC, HXT, LIRC使能状态 */
    ClkStatus = RCC_GetSysclkSource(RCC);

    /* 判断HXT时钟是否开启 */
    if ((ClkStatus & 0x02) != 0x02)
    {
        RCC_SysclkCmd(RCC, RCC_SYSCLKSource_HXT, ENABLE); // 使能HXT时钟
    }

    delay(1);
    /* 等待HXT时钟稳定 */
    Status = RCC_WaitForHXTStable(RCC, RCC_HXT_STARTUP_PERIOD16384);

    if (Status == SUCCESS)
    {
        RCC_SelSysclk(RCC, RCC_SYSCLKSource_HXT);
    }
}
