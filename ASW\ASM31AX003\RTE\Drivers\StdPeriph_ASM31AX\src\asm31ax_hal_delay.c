#include "asm31ax_hal.h"

#ifdef HAL_DELAY_MODULE_ENABLED

/**
 * \brief   delay100us
 *          delay approximately 100us.
 * \param   [in]  u32Cnt
 * \retval  void
 */
void HAL_Delay_100us(volatile uint32_t u32Cnt)
{

    uint32_t i;
    while (u32Cnt-- > 0)
    {
        for (i = 0; i < 340; i++)
        {
        }
    }
}

/**
 * \brief   delay1ms
 *          delay approximately 1ms.
 * \param   [in]  u32Cnt
 * \retval  void
 */
void HAL_Delay_1ms(volatile uint32_t u32Cnt)
{

    while (u32Cnt-- > 0)
    {
        HAL_Delay_100us(10);
    }
}

#endif
