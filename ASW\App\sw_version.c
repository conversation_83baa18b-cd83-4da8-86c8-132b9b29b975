#include <stdio.h>
#include "drv.h"
#include "config.h"

#if (MCU==ARM)
#if (FLASH_BASE == 0x08000000UL)
uint8_t const sw_version_data[32] __attribute__((section(".ARM.__at_0x8000100"))) = VCAE_VERSION;
#else
uint8_t const sw_version_data[32] __attribute__((section(".ARM.__at_0x100"))) = VCAE_VERSION;
#endif
uint8_t code *sw_version = sw_version_data;
#else
uint8_t code *sw_version = VCAE_VERSION;
static uint8_t volatile code sw_version_dummy[10] = {0};
#endif
