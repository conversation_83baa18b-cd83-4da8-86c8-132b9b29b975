
static uint8_t code parm_data[] =
{
    // head
    0xAA, 0x55,
    // 参数文件版本号
    5,
    // 启动以后延迟若干周期再读ADC
    122,
    // 预留, 4字节
    0, 0, 0, 0,
    // ccode1, 4字节
    0x78, 0x23, 0x44, 0xB6,
    // cid, 12字节 "\x56\x33\x28\x52\x74\x66\x78\x84\x23\x44\x21\xB5"
    0x56, 0x33, 0x28, 0x52, 0x74, 0x66, 0x78, 0x84, 0x23, 0x44, 0x21, 0xB5,
    // ccode2, 4字节
    0xA3, 0x53, 0xC4, 0xE2,
    // counter, 4字节
    0, 0, 0, 0,
    // mode
    MODE_ENABLE,
    NTC_TYPE,
    // vec_max_out, ec最高工作电压, 10mv
    120,
    // vec_min_out, ec最低工作电压, 10mv
    38,
    // 眩光采样周期, 10ms
    2,
    // 环境光采样周期, 10ms
    10,
    // 发射率闭环控制周期, 10ms
    10,
    // 预留
    0,
    // ec_max_out, 最大输出PWM值
    85,
    // ec_min_out, 最小输出PWM值
    20,
    // v12_max
    0,
    // v12_min
    0,

    // 电压PID最大PWM值
    99,
    // 电压PID最小PWM值
    10,
    // 电压PID kp
    0, 10,
    // 电压PID ki
    10,
    // 电压PID kd
    10,
    // 电压PID iMax
    39, 16,

    // 反射率PID kp
    0, 86,
    // 反射率PID ki
    7,
    // 反射率PID kd
    0,
    // 反射率PID iMax
    31, 64,

    // 滤波参数（环境光均值）
    20,
    // 滤波参数（眩目光均值）
    10,
    // 滤波参数（环境光低通）
    5,
    // 滤波参数（眩目光低通）
    0,

    // B_dark, 眩目光标定最低值(ADC)
    3,
    // F_dark, 环境光标定最低值(ADC)
    0,
    // B_ref, 光电流标定参考值(ADC)
    0, 0,
    // B_light, 光电流标定值(ADC)
    0, 0,
    // dark_count, 标暗次数
    0,
    // light_count 标亮次数
    0,
    // pwm_step;
    0, 0,
    // gls_step;
    0,
    // reserved4[7];
    0, 0, 0, 0, 0, 0, 0, 
    // lin_wake_disable
    0, 
    // ec_dim_snvty
    4, 
    // ec_dim_snvty_ratio
    20, 
    // ec_cem_ena_disable
    0,
    // oecoveriec
    18,
    // tab_length
    6,
    // compensation_tab[6], 滞环带宽表
    2, 4, 8, 16, 32, 64,
    // als_tab[6], 环境光门限值 10bit
    24, 36, 48, 60, 72, 84,
    // target_tab[6], 目标ADC值
    0, 16, 0, 72, 0, 162, 1, 32, 1, 194, 2, 136,
    // temperature_tab[6], 温度补偿表
    6, 143, 5, 70, 4, 46, 3, 74, 2, 149, 2, 7,
    // tc_step, 温度补偿步进值
    1,
    // als_bits
    10,
    // 降额温度对应的ADC值
    3, 74,
    // check_foot
    0x55, 0xAA
};
