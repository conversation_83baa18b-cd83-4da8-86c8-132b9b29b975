/*
  Copyright (C), 2018-2019, VCAE. Co., Ltd.
  FileName:    main.c
  Description:  ASW main文件
  Version: 0.1
  Function List:
    1.
  History:
      <author>   <time>      <version >                <desc>
        benny      19/11/1        0.1                  新创建模块
*/

/**********************************************************************************************
 * External objects
 **********************************************************************************************/
#include "drv.h"

#include "config.h"
#include "signal.h"

#include "db.h"
#include "pid.h"
#include "tuning.h"

#include "config_asw.h"
#include "board.h"

#define ANTI_PIRACY_ENABLE

extern void AntiPiracy_Init(void);
extern uint8_t AntiPiracy_Execute(void);

#ifdef LIN_ENABLE
extern uint8_t lin_ec_disabled(void);
extern void lin_wake_check_process(void);
extern void set_IntrMirrRespIntrMirrDimPerc(unsigned char percent);
extern void lin_adjust_senvity(uint16_t *target);
#endif

extern uint8_t code *sw_version;

extern uint8_t iec_pwm;

/**********************************************************************************************
 * Global variables
 **********************************************************************************************/

/**********************************************************************************************
 * Constants and macros
 **********************************************************************************************/

/**********************************************************************************************
 * Local types
 **********************************************************************************************/

typedef struct
{
    uint16_t gls_value;
    uint16_t als_value;
    uint16_t gls_average;
    uint16_t als_average;
    RingBuffer_t als_ring_buffer;
    RingBuffer_t gls_ring_buffer;

    uint16_t gls_history;
    uint16_t als_history;
    uint16_t gls_final;
    uint16_t als_final;
    uint16_t als_tc;

    uint8_t vin_value;
    uint16_t oec_value;
    uint16_t ec_value;
    uint16_t ec_fb_voltage;
    uint16_t oec_fb_voltage;
    uint16_t ec_target_voltage;
    uint16_t oec_target_voltage;
    uint16_t iec_pwm;
    uint16_t oec_pwm;

    uint16_t target;
    uint8_t bandwidth;

    uint16_t bl_target;

    uint8_t first_skip;
    uint8_t temperature_index;
    uint16_t ntc;
} SensorData_t;

typedef union
{
    uint16_t Word;

    struct
    {
        uint8_t anti_piracy_lock : 1;
        uint8_t first_running : 1;
        uint8_t ec_disabled : 1;

    } Bits;
} StateData_t;

typedef struct
{
    uint16_t iec;
    uint16_t oec;
    uint16_t v12;

    uint8_t sensor_sick;
} IEMDiag_t;

typedef enum
{
    CALI_TYPE_DARK = 1,
    CALI_TYPE_LIGHT
} cali_type_t;

typedef struct
{
    cali_type_t type;
    uint8_t cali_dark_count;
    uint8_t cali_light_count;

    uint8_t F_dark;
    uint8_t F_adc;
    uint8_t B_dark;

    uint16_t B_light;
    uint16_t B_adc;
    uint16_t B_ref;
} cali_data_t;

/**********************************************************************************************
 * Local function prototypes
 *********************************************************************************************/

/**********************************************************************************************
 * Local variables
 **********************************************************************************************/

SensorData_t sensor_data;
StateData_t state_data;
IEMDiag_t iem_diag_data;
PID_Data_t RLoop;
#ifdef EC_VOLTAGE_LOOP
PID_Data_t VLoop;
#ifdef OEC_ENABLE
PID_Data_t VLoop_oec;
#endif
#endif

#ifdef EC_NEGATIVE_VOLTAGE_ENABLE
static uint16_t counter_5s = 0xFFFF;
#endif

uint16_t counter;
uint8_t log_on;
uint8_t is_running_data_output;

uint16_t ec_delay_counter;

static bit is_temp_too_high;

/**********************************************************************************************
 * Local functions
 **********************************************************************************************/
static void GlobalVar_Init(void)
{
    counter = 0;
    ec_delay_counter = 0;

    memset(&state_data, 0, sizeof(StateData_t));
    memset(&sensor_data, 0, sizeof(SensorData_t));
    memset(&iem_diag_data, 0, sizeof(IEMDiag_t));
}

static uint16_t get_gls_value(void)
{
    uint16_t value;

    value = adc_get_back();

#if 0
    if (value>1000)
    {
        value=1000;
    }
#endif
    return value;
}
// 内镜电压PID控制
#ifdef EC_VOLTAGE_LOOP
void EC_Voltage_Ctrl(void)
{
    // 电压闭环
    // sensor_data.ec_value = adc_get_ec_int();
    // sensor_data.ec_fb_voltage = (uint32_t)sensor_data.ec_value * 123 / 1000; // mv,sensor_data.ec_value*VCC_VOLTAGE/MAX_ADC_RANGE;

    VLoop.IMax = VLoop_IMAX;
    VLoop.Kp = VLoop_Kp;
    VLoop.Ki = VLoop_Ki;
    VLoop.Kd = VLoop_Kd;
    VLoop.max = VLoop_MAX - VLoop_MIN;
    VLoop.min = 0;
    VLoop.target = sensor_data.ec_target_voltage;

    if (sensor_data.ec_target_voltage == 0)
    {
        sensor_data.iec_pwm = 0;
    }
    else
    {
        sensor_data.iec_pwm = Voltage_PID_Ctrl(&VLoop, sensor_data.ec_fb_voltage);
        // if (sensor_data.iec_pwm > 0)
        // {
        sensor_data.iec_pwm += VLoop_MIN;
        // }
        // else
        // {
        //     sensor_data.iec_pwm = VLoop_MIN;
        // }
    }

    if (sensor_data.iec_pwm > 5)
    {
        pwm_set_duty(PWM_CH_IEC, sensor_data.iec_pwm);
    }
    else
    {
        pwm_set_duty(PWM_CH_IEC, 0);
    }
}
#endif

// 外镜电压PID控制
#if defined(EC_VOLTAGE_LOOP) && defined(OEC_ENABLE)
void OEC_Voltage_Ctrl(void)
{
    // 电压闭环
    // sensor_data.oec_value = adc_get_ec_ext();
    // sensor_data.oec_fb_voltage = (uint32_t)sensor_data.oec_value * 123 / 1000; // mv,sensor_data.ec_value*VCC_VOLTAGE/MAX_ADC_RANGE;

    VLoop_oec.IMax = VLoop_IMAX;
    VLoop_oec.Kp = VLoop_Kp;
    VLoop_oec.Ki = VLoop_Ki;
    VLoop_oec.Kd = VLoop_Kd;
    VLoop_oec.max = VLoop_MAX - VLoop_MIN;
    VLoop_oec.min = 0;
    sensor_data.oec_target_voltage = sensor_data.ec_target_voltage;
    VLoop_oec.target = sensor_data.oec_target_voltage;

    if (sensor_data.oec_target_voltage == 0)
    {
        sensor_data.oec_pwm = 0;
    }
    else
    {
        sensor_data.oec_pwm = Voltage_PID_Ctrl(&VLoop_oec, sensor_data.oec_fb_voltage);
        // if (sensor_data.oec_pwm > 0)
        // {
        sensor_data.oec_pwm += VLoop_MIN;
        // }
        // else
        // {
        //     sensor_data.oec_pwm = 0;
        // }
    }

    if (sensor_data.oec_pwm > 5)
    {
        pwm_set_duty(PWM_CH_OEC, sensor_data.oec_pwm);
    }
    else
    {
        pwm_set_duty(PWM_CH_OEC, 0);
    }
}
#endif

// 反射率闭环控制, 电压闭环控制
#if defined(REFLECTIVITY_LOOP) && defined(EC_VOLTAGE_LOOP)
void EC_Ctrl(void)
{
    static uint8_t als_index;
    // static uint8_t als_index_prev = 10;
    // static uint8_t counter_als = 20;


    if (Open_Loop_Enabled)
    {
        sensor_data.ec_target_voltage = 88; // mv, duty=80%
            return;
        }

    if (state_data.Bits.ec_disabled == 1 || iem_diag_data.sensor_sick == 1)
    {
        sensor_data.ec_target_voltage = 0;
    }
    else
    {
#if 1
        /*
                温度补偿:
                1.表长6,数值递减排列,温度越高数值越小
                2.温度每高一格ADC减少2
         */

        if (TC_Enabled)
        {
            if (sensor_data.als_final > sensor_data.temperature_index * 2)
            {
                sensor_data.als_tc = sensor_data.als_final - sensor_data.temperature_index * 2;
            }
            else
            {
                sensor_data.als_tc = 0;
            }
        }
        else
        {
            sensor_data.als_tc = sensor_data.als_final;
        }
#endif

        als_index = TAB_GetAlsIndex(sensor_data.als_tc);
#if 0
        if(als_index != als_index_prev)
        {
            counter_als++;
            if(counter > 20)
            {
               counter_als = 0; 
               als_index_prev = als_index;
            }
            else
            {
                als_index = als_index_prev;
            }
                  
        }
        else
        {
            counter_als = 0;
        }
#endif
        // counter_als++;

        if (als_index >= Tab_Length)// && counter_als > 15) // 环境光比较亮EC不工作情况
        {
            // counter_als = 20;
            sensor_data.iec_pwm = 0;
            sensor_data.ec_target_voltage = 0;
            RLoop.target = 0;
            RLoop.err = 0;
            RLoop.err_k_1 = 0;
            RLoop.integral = 0;
        }
        else
        {
            // if(als_index < Tab_Length)
            //     counter_als = 0;
            // else
            //     als_index = 0;

            RLoop.max = VMax_EC_Out - VMin_EC_Out;
            RLoop.min = 0;
            RLoop.target = TAB_GetTarget(als_index);
            RLoop.Kd = RLoop_Kd;
            RLoop.IMax = RLoop_IMAX;            
#ifdef MULTI_PID_PARM_ENABLE            
            RLoop.Kp = RLoop_Kp - als_index * 18;
            if(RLoop.Kp < 0)
            {
                RLoop.Kp = 5;
            }
            if(als_index == 0)
            {
                RLoop.Ki = RLoop_Ki;
                // 加快回眩
                if(sensor_data.gls_final < RLoop.target)
                {
                    // RLoop.Ki =  127;
                }
            }
            else
            {
                RLoop.Ki = 12 - als_index * 2;
            }
            if(RLoop.Ki < 0)
            {
                RLoop.Ki = 2;
            }
#else
            RLoop.Kp = RLoop_Kp;
            RLoop.Ki = RLoop_Ki;
#endif

            /*
                    温度补偿:
                    1.表长6,数值递减排列,温度越高数值越小
                    2.温度每高一格目标提高ADC值1
                 */
#if 1
            if (TC_Enabled)
            {
                RLoop.target += sensor_data.temperature_index * db_main.tc_step;
            }

#endif
#ifdef LIN_ENABLE
            lin_adjust_senvity(&RLoop.target);
#endif

            RLoop.bandwidth = TAB_GetCompensation(als_index);
            /*滞环控制 Hysteresis Control */
            // 启动变黑滞环判断
            if (RLoop.dimming == 0)
            {
                if (sensor_data.gls_final > RLoop.target + RLoop.bandwidth)
                {
                    RLoop.dimming = 1;
                }
            }
            else
            {
                /*PID 控制*/
                sensor_data.ec_target_voltage = PID_Ctrl(&RLoop, sensor_data.gls_final);

                if (sensor_data.ec_target_voltage != 0)
                {

                    sensor_data.ec_target_voltage += VMin_EC_Out;
                }
                else
                {
                    // 退出变黑滞环判断
                    if (sensor_data.gls_final < RLoop.target - RLoop.bandwidth)
                    {
                        RLoop.dimming = 0;
                        RLoop.integral = 0;
                    }
                    else
                    {
                        sensor_data.ec_target_voltage = VMin_EC_Out;
                    }
                }
                // 过温降额
#ifdef LIMIT_VOLTAGE               
                if(db_main.temp_threshold)
                {
                    //小于60度
                    if(sensor_data.ntc > db_main.temp_threshold + 80)
                    {
                        is_temp_too_high = 0;
                    }
                    //大于65度
                    if(sensor_data.ntc < db_main.temp_threshold || is_temp_too_high == 1)
                    {
                        is_temp_too_high = 1;
                        if(sensor_data.ec_target_voltage > LIMIT_VOLTAGE)
                            sensor_data.ec_target_voltage = LIMIT_VOLTAGE;
                    }
                }
#endif                
            }
        }
    }
}

// 反射率闭环控制, 电压开环控制
#elif defined(REFLECTIVITY_LOOP) && !defined(EC_VOLTAGE_LOOP)
void EC_Ctrl(void)
{
    uint8_t als_index;

    if (state_data.Bits.ec_disabled == 1 || iem_diag_data.sensor_sick == 1)
    {
        sensor_data.ec_target_voltage = 0;
    }
    else
    {
#if 1
        /*
                温度补偿:
                1.表长6,数值递减排列,温度越高数值越小
                2.温度每高一格ADC减少2
         */

        if (TC_Enabled)
        {
            if (sensor_data.als_final > sensor_data.temperature_index * 2)
            {
                sensor_data.als_tc = sensor_data.als_final - sensor_data.temperature_index * 2;
            }
            else
            {
                sensor_data.als_tc = 0;
            }
        }
        else
        {
            sensor_data.als_tc = sensor_data.als_final;
        }
#endif

        als_index = TAB_GetAlsIndex(sensor_data.als_tc);
        if (als_index >= Tab_Length) // 环境光比较亮EC不工作情况
        {
            sensor_data.iec_pwm = 0;
            sensor_data.ec_target_voltage = 0;
            RLoop.target = 0;
            RLoop.err = 0;
            RLoop.err_k_1 = 0;
            RLoop.integral = 0;
        }
        else
        {
            RLoop.max = VMax_EC_Out - VMin_EC_Out;
            RLoop.min = 0;
            RLoop.Kp = RLoop_Kp;
            RLoop.Ki = RLoop_Ki;
            RLoop.Kd = RLoop_Kd;
            RLoop.IMax = RLoop_IMAX;

            RLoop.target = TAB_GetTarget(als_index);
            /*
                    温度补偿:
                    1.表长6,数值递减排列,温度越高数值越小
                    2.温度每高一格目标提高ADC值1
                 */
#if 1
            if (TC_Enabled)
            {
                RLoop.target += sensor_data.temperature_index * db_main.tc_step;
            }

#endif
#ifdef LIN_ENABLE
            lin_adjust_senvity(&RLoop.target);
#endif

            RLoop.bandwidth = TAB_GetCompensation(als_index);
            /*滞环控制 Hysteresis Control */
            // 启动变黑滞环判断
            if (RLoop.dimming == 0)
            {
                if (sensor_data.gls_final > RLoop.target + RLoop.bandwidth)
                {
                    RLoop.dimming = 1;
                }
            }
            else
            {
                /*PID 控制*/
                sensor_data.ec_target_voltage = PID_Ctrl(&RLoop, sensor_data.gls_final);

                if (sensor_data.ec_target_voltage != 0)
                {

                    sensor_data.ec_target_voltage += VMin_EC_Out;
                }
                else
                {
                    // 退出变黑滞环判断
                    if (sensor_data.gls_final < RLoop.target - RLoop.bandwidth)
                    {
                        RLoop.dimming = 0;
                        RLoop.integral = 0;
                    }
                    else
                    {
                        sensor_data.ec_target_voltage = VMin_EC_Out;
                    }
                }
                // 过温降额
#ifdef LIMIT_VOLTAGE               
                if(db_main.temp_threshold)
                {
                    //小于60度
                    if(sensor_data.ntc > db_main.temp_threshold + 80)
                    {
                        is_temp_too_high = 0;
                    }
                    //大于65度
                    if(sensor_data.ntc < db_main.temp_threshold || is_temp_too_high == 1)
                    {
                        is_temp_too_high = 1;
                        if(sensor_data.ec_target_voltage > LIMIT_VOLTAGE)
                            sensor_data.ec_target_voltage = LIMIT_VOLTAGE;
                    }
                }
#endif                
            }
        }
    }

    if (Open_Loop_Enabled)
    {
        sensor_data.ec_target_voltage = 88; // mv, duty=80%
    }

    sensor_data.iec_pwm = sensor_data.ec_target_voltage * 7 / 20;
#ifdef INT_EC_ADC_WITH_DIFF
    sensor_data.iec_pwm *= 2;
#endif
    if (sensor_data.iec_pwm > 5)
    {
         pwm_set_duty(PWM_CH_IEC, sensor_data.iec_pwm);
    }
    else
    {
        pwm_set_duty(PWM_CH_IEC, 0);    
    }

#ifdef OEC_ENABLE
    sensor_data.oec_pwm = sensor_data.ec_target_voltage * 7 / 20;
    if (sensor_data.oec_pwm > 5)
    {
        pwm_set_duty(PWM_CH_OEC, sensor_data.oec_pwm);
    }
    else
    {
        pwm_set_duty(PWM_CH_OEC, 0);
    }
#endif


}
// 反射率开环控制，电压闭环
#elif !defined(REFLECTIVITY_LOOP) && defined(EC_VOLTAGE_LOOP)
static uint8_t ec_status; // 0: 全暗和全亮之间的状态 1：全暗 2 : 全亮
void EC_Ctrl(void)
{
    uint8_t als_index;
    uint16_t scope;

    if (Open_Loop_Enabled)
    {
        sensor_data.ec_target_voltage = 88; // mv, duty=80%
        return;
    }

    if (state_data.Bits.ec_disabled == 1 || iem_diag_data.sensor_sick == 1)
    {
        sensor_data.ec_target_voltage = 0;
        ec_status = 0;
    }
    else
    {
#if 1
        /*
                温度补偿:
                1.表长6,数值递减排列,温度越高数值越小
                2.温度每高一格ADC减少2
         */

        if (TC_Enabled)
        {
            if (sensor_data.als_final > sensor_data.temperature_index * 2)
            {
                sensor_data.als_tc = sensor_data.als_final - sensor_data.temperature_index * 2;
            }
            else
            {
                sensor_data.als_tc = 0;
            }
        }
        else
        {
            sensor_data.als_tc = sensor_data.als_final;
        }
#endif

        als_index = TAB_GetAlsIndex(sensor_data.als_tc);
        if (als_index >= Tab_Length) // 环境光比较亮EC不工作情况
        {
            sensor_data.ec_target_voltage = 0;
        }
        else
        {

            RLoop.target = TAB_GetTarget(als_index);
            /*
                    温度补偿:
                    1.表长6,数值递减排列,温度越高数值越小
                    2.温度每高一格目标提高ADC值1
                 */
            if (TC_Enabled)
            {
                RLoop.target += sensor_data.temperature_index * db_main.tc_step;
            }

#ifdef LIN_ENABLE
            lin_adjust_senvity(&target);
#endif

            RLoop.bandwidth = TAB_GetCompensation(als_index);
            scope = *(((uint8_t *)&db_main.rloop.kp) + als_index) * 4; // 借用反射率PID参数

            if (sensor_data.gls_final > RLoop.target + scope)
            {
                // 全暗
                ec_status = 1;
                sensor_data.ec_target_voltage = VMax_EC_Out;
            }
            else if (sensor_data.gls_final < RLoop.target)
            {
                // 全亮
                ec_status = 2;
                sensor_data.ec_target_voltage = VMin_EC_Out;
            }
            else if (ec_status == 1)
            {
                sensor_data.ec_target_voltage = VMax_EC_Out;
                // 退出全暗状态
                if (sensor_data.gls_final + RLoop.bandwidth < RLoop.target + scope)
                    ec_status = 0;
            }
            else if (ec_status == 2)
            {
                sensor_data.ec_target_voltage = VMin_EC_Out;
                // 退出全亮状态
                if (sensor_data.gls_final > RLoop.target + RLoop.bandwidth)
                    ec_status = 0;
            }
            else
            {
                /*根据target和gls的值，计算目标电压*/
                sensor_data.ec_target_voltage = VMin_EC_Out + ((sensor_data.gls_final - RLoop.target) * (VMax_EC_Out - VMin_EC_Out) + scope / 2) / scope;
            }

            // 过温降额
#ifdef LIMIT_VOLTAGE               
            if(db_main.temp_threshold)
            {
                //小于60度
                if(sensor_data.ntc > db_main.temp_threshold + 80)
                {
                    is_temp_too_high = 0;
                }
                //大于65度
                if(sensor_data.ntc < db_main.temp_threshold || is_temp_too_high == 1)
                {
                    is_temp_too_high = 1;
                    if(sensor_data.ec_target_voltage > LIMIT_VOLTAGE)
                        sensor_data.ec_target_voltage = LIMIT_VOLTAGE;
                }
            }
#endif  
        }
    }
}

#else
#error "Pls check REFLECTIVITY_LOOP and EC_VOLTAGE_LOOP"
#endif

void diagnose(void)
{
    uint8_t ec_disable, ec_off;

    ec_disable = gpio_rev_in();
    ec_off = gpio_get_off();

    iem_diag_data.iec = GetIEC();
    iem_diag_data.oec = GetOEC();
    iem_diag_data.v12 = GetV12();

    sensor_data.ntc = get_temp_value();
    sensor_data.temperature_index = TAB_GetTemperatureIndex(sensor_data.ntc);
    if (sensor_data.temperature_index >= Tab_Length)
    {
#ifndef EMC_TEST_ENABLE        
        iem_diag_data.sensor_sick = 1;
#endif        
    }
    else
    {
        iem_diag_data.sensor_sick = 0;
    }
#if 0
    if (log_on)
    {
        printf("F=%d,%d,%d,B=%d,%d,Dis=%d,OFF=%d,vEC=%d,duty=%d,%d,12V=%d,IEC=%d,OEC=%d,"
               "target=%d,err=%d,I=%ld\r\n",
               sensor_data.als_value,
               sensor_data.als_final,
               sensor_data.als_tc,
               sensor_data.gls_value,
               sensor_data.gls_final,
               (uint16_t)ec_disable,
               (uint16_t)ec_off,
               sensor_data.ec_target_voltage,
               sensor_data.iec_pwm,
               (uint16_t)iec_pwm,
               (uint16_t)iem_diag_data.v12,
               (uint16_t)iem_diag_data.iec,
               (uint16_t)iem_diag_data.oec,
               (int16_t)RLoop.target,
               (int16_t)RLoop.err,
               RLoop.integral);
    }
#endif
}

/**
 * @brief    1ms task
 * @param  None
 * @retval   None
 */
void task_1ms(void)
{
}

/**
 * @brief    10ms task
 * @param  None
 * @retval   None
 */

void TASK_10MS_func(void)
{
    if (sensor_data.first_skip < Boot_Delay)
    {
        sensor_data.first_skip++;
        if (sensor_data.first_skip == Boot_Delay - 1)
        {
            if (log_on)
            {
                printf("boot complete.\r\n");
            }
        }
        sensor_data.als_value = get_als_value();
        sensor_data.gls_value = get_gls_value();  
        sensor_data.ntc = get_temp_value();      
        sensor_data.als_history = sensor_data.als_value * 100;
        sensor_data.gls_history = sensor_data.gls_value * 10;
        return;
    }

    if (!state_data.Bits.first_running)
    {
        state_data.Bits.first_running = 1;

        // sensor_data.als_value = get_als_value();
        // sensor_data.gls_value = get_gls_value();
#if 0
        sensor_data.als_average=Filter_Average(&sensor_data.als_ring_buffer,
                                               sensor_data.als_value,
                                               Filter_Average_ALS_Constant);
        sensor_data.gls_average=Filter_Average(&sensor_data.gls_ring_buffer,
                                               sensor_data.gls_value,
                                               Filter_Average_GLS_Constant);
        sensor_data.gls_history=sensor_data.gls_average;
        sensor_data.als_history=sensor_data.als_average;
        sensor_data.als_final=sensor_data.als_average;
        sensor_data.gls_final=sensor_data.gls_average;
#else
        sensor_data.als_final = sensor_data.als_value;
        sensor_data.gls_final = sensor_data.gls_value;

#endif
    }
    else
    {

        if (counter % Period_ALS == 0)
        {

            sensor_data.als_value = get_als_value();
#if 1
            sensor_data.als_average = Filter_Average(&sensor_data.als_ring_buffer,
                                                     sensor_data.als_value,
                                                     Filter_Average_ALS_Constant);
#else
            sensor_data.als_average = sensor_data.als_value;

#endif

            // 零修正
            sensor_data.als_average = sensor_data.als_average >= db_main.cali.F_dark ? sensor_data.als_average - db_main.cali.F_dark : 0;

            if (Filter_Low_Pass_ALS_Constant)
            {
                sensor_data.als_history = Filter_LowPass(sensor_data.als_history,
                                                       sensor_data.als_average * 100,
                                                       Filter_Low_Pass_ALS_Constant);
                // sensor_data.als_history = sensor_data.als_final;
                sensor_data.als_final = (sensor_data.als_history + 50) / 100;
            }
            else
            {
                sensor_data.als_final = sensor_data.als_average;
            }
        }

        if (counter % Period_GLS == 0)
        {

            sensor_data.gls_value = get_gls_value();

#if 1
            sensor_data.gls_average = Filter_Average(&sensor_data.gls_ring_buffer,
                                                     sensor_data.gls_value,
                                                     Filter_Average_GLS_Constant);
#else
            sensor_data.gls_average = sensor_data.gls_value;

#endif

            // 零修正
            sensor_data.gls_average = sensor_data.gls_average >= db_main.cali.B_dark ? sensor_data.gls_average - db_main.cali.B_dark : 0;

            if (Filter_Low_Pass_GLS_Constant)
            {
                sensor_data.gls_final = Filter_LowPass(sensor_data.gls_history,
                                                       sensor_data.gls_average * 10,
                                                       Filter_Low_Pass_GLS_Constant);
                sensor_data.gls_history = sensor_data.gls_final;
                sensor_data.gls_final = (sensor_data.gls_final + 5) / 10;
            }
            else
            {
                sensor_data.gls_final = sensor_data.gls_average;
            }

#if 0
            //光电流校准   B-(B_light-B_ref)*B/B_ref
            {

                int32_t value32;

                value32=db_main.cali.B_light-db_main.cali.B_ref;
                value32*=sensor_data.gls_final;
                value32/=db_main.cali.B_ref;

                value32=(int32_t)sensor_data.gls_final-value32;
                if (value32>4000)
                {
                    sensor_data.gls_final=4000;
                }
                else
                {
                    sensor_data.gls_final=(uint16_t)value32;
                }

            }
            //光电流校准 end
#endif
        }
    }

    // 判断EC 是否打开
    if (gpio_rev_in())
    {
        state_data.Bits.ec_disabled = 1;
    }
#ifdef LIN_ENABLE
    else if (lin_ec_disabled() == 1)
    {
        state_data.Bits.ec_disabled = 1;
    }
#endif
#ifdef SWITCH_KEY_ENABLE
    else if (is_ec_disable_by_key() == 1)
    {
        state_data.Bits.ec_disabled = 1;
    }
#endif
    else
        state_data.Bits.ec_disabled = 0;

    if (counter % Period_CTRL == 0)
    {
        EC_Ctrl();
    }

    if (is_running_data_output || Log_Enabled)
    {
        if (counter % 10 == 0)
        {
            uint8_t length, buffer[36];

            length = 0;
            buffer[length] = 0xA5;
            length += 2;
            memcpy(buffer + length, (uint8_t *)&sensor_data.als_value, 2);
            length += 2;
            memcpy(buffer + length, (uint8_t *)&sensor_data.als_final, 2);
            length += 2;
            memcpy(buffer + length, (uint8_t *)&sensor_data.als_tc, 2);
            length += 2;
            memcpy(buffer + length, (uint8_t *)&sensor_data.gls_value, 2);
            length += 2;
            memcpy(buffer + length, (uint8_t *)&sensor_data.gls_final, 2);
            length += 2;
            memcpy(buffer + length, (uint8_t *)&sensor_data.ec_fb_voltage, 2);
            length += 2;
            memcpy(buffer + length, (uint8_t *)&sensor_data.oec_fb_voltage, 2);
            length += 2;
            memcpy(buffer + length, (uint8_t *)&sensor_data.ec_target_voltage, 2);
            length += 2;
            memcpy(buffer + length, (uint8_t *)&sensor_data.iec_pwm, 2);
            length += 2;
            memcpy(buffer + length, (uint8_t *)&sensor_data.oec_pwm, 2);
            length += 2;
            memcpy(buffer + length, (uint8_t *)&RLoop.target, 2);
            length += 2;
            memcpy(buffer + length, (uint8_t *)&RLoop.err, 2);
            length += 2;
            memcpy(buffer + length, (uint8_t *)&sensor_data.ntc, 2);
            length += 2;
            memcpy(buffer + length, (uint8_t *)&RLoop.integral, 4);
            length += 4;
            buffer[1] = length - 2;
            buffer[length++] = 0x5A;
#if (MCU == ARM)            
            uint8_t tmp;
            uint8_t i;
            for(i = 2; i < length - 6; i = i + 2)
            {
                tmp = buffer[i + 1];
                buffer[i + 1] = buffer[i];
                buffer[i] = tmp;
            }
            tmp = buffer[i + 3];
            buffer[i + 3] = buffer[i];
            buffer[i] = tmp;
            tmp = buffer[i + 2];
            buffer[i + 2] = buffer[i + 1];
            buffer[i + 1] = tmp;
#endif

            uart_tat_send(buffer, length);
        }
    }

    sensor_data.ec_value = adc_get_ec_int();
    sensor_data.ec_fb_voltage = (uint32_t)sensor_data.ec_value * 123 / 1000; // mv,sensor_data.ec_value*VCC_VOLTAGE/MAX_ADC_RANGE;
    sensor_data.oec_value = adc_get_ec_ext();
    sensor_data.oec_fb_voltage = (uint32_t)sensor_data.oec_value * 123 / 1000; // mv,sensor_data.ec_value*VCC_VOLTAGE/MAX_ADC_RANGE;

#ifdef EC_VOLTAGE_LOOP
    EC_Voltage_Ctrl();
#ifdef OEC_ENABLE
    OEC_Voltage_Ctrl();
#endif

#ifdef EC_NEGATIVE_VOLTAGE_ENABLE
    sensor_data.oec_pwm = 0;
    if(sensor_data.ec_target_voltage == VMin_EC_Out)
    {
        if(counter_5s < 400)
        {
            static PID_Data_t VLoop_oec1;

            counter_5s++;
            VLoop_oec1.IMax = VLoop_IMAX;
            VLoop_oec1.Kp = VLoop_Kp;
            VLoop_oec1.Ki = VLoop_Ki;
            VLoop_oec1.Kd = VLoop_Kd;
            VLoop_oec1.max = VLoop_MAX - VLoop_MIN;
            VLoop_oec1.min = 0;
            VLoop_oec1.target = 45;

            sensor_data.oec_pwm = Voltage_PID_Ctrl(&VLoop_oec1, sensor_data.oec_fb_voltage);
            sensor_data.oec_pwm += VLoop_MIN;
        }
    }
    else
    {
        counter_5s = 0;
    }
    pwm_set_duty(PWM_CH_OEC, sensor_data.oec_pwm);
#endif    

#endif



#ifdef ANTI_PIRACY_ENABLE
    if(state_data.Bits.anti_piracy_lock == 1)
    {
        pwm_set_duty(PWM_CH_IEC, Max_EC_Out);
    }
#endif 

#ifdef LIN_ENABLE
    if (sensor_data.ec_target_voltage > VMax_EC_Out)
        set_IntrMirrRespIntrMirrDimPerc(100);
    else
        set_IntrMirrRespIntrMirrDimPerc(sensor_data.ec_target_voltage * 255 / VMax_EC_Out);
#endif

    if (counter > 1000)
    {
        counter = 1;
    }
    else
    {
        counter++;
    }
}

/**
 * @brief    1s task
 * @param  None
 * @retval   None
 */

void TASK_1S_func(void)
{
    #if WDOG_ENABLED
    WDOG_Feed();
#endif
    // 启动延迟的时候信息不打印
    if (sensor_data.first_skip < Boot_Delay)
    {
        return;
    }
#ifdef LIN_ENABLE
#if LIN_WAKE_SUPPORT
    lin_wake_check_process();
#endif
#endif
    diagnose();


#ifdef ANTI_PIRACY_ENABLE
    state_data.Bits.anti_piracy_lock = AntiPiracy_Execute();
#endif    
}

/**
 * @brief    KEY_func
 * @param  None
 * @retval   None
 */

void KEY_func(void)
{
}

/**********************************************************************************************
 * Global functions
 **********************************************************************************************/
uint8_t sensor_calibration(uint8_t *cmd, uint8_t *log_buffer)
{
    cali_data_t *cali_data;
    uint16_t als_value, gls_value;

    gls_value = 0;
    als_value = 0;

    cali_data = (cali_data_t *)(log_buffer);

    if (cmd[PROT_PARAM] == CALI_TYPE_DARK)
    {
        als_value = get_als_value();
        db_main.cali.F_dark = als_value;
        gls_value = get_gls_value();
        db_main.cali.B_dark = (uint8_t)(gls_value & 0xFF);
        db_main.cali.dark_count++;

        cali_data->type = CALI_TYPE_DARK;
    }
    else if (cmd[PROT_PARAM] == CALI_TYPE_LIGHT)
    {
        gls_value = get_gls_value();
        db_main.cali.B_light = gls_value;
        db_main.cali.light_count++;
        db_main.cali.B_ref = cmd[PROT_PARAM + 2] << 8;
        db_main.cali.B_ref += cmd[PROT_PARAM + 1];
        cali_data->type = CALI_TYPE_LIGHT;
    }
    else
    {
        // assert
        return 0;
    }

    DB_Save(DB_MAIN, &db_main);
    DB_Save(DB_MIRROR, &db_main);
    DB_Load(DB_MAIN, &db_main);

    cali_data->cali_dark_count = db_main.cali.dark_count;
    cali_data->cali_light_count = db_main.cali.light_count;

    cali_data->F_dark = db_main.cali.F_dark;
    cali_data->F_adc = als_value;
    cali_data->B_dark = db_main.cali.B_dark;

    cali_data->B_light = db_main.cali.B_light;
    cali_data->B_adc = gls_value;
    cali_data->B_ref = db_main.cali.B_ref;

    return 12;
}

uint8_t sensor_calibration_check(uint8_t *log_buffer)
{
    cali_data_t *cali_data;

    cali_data = (cali_data_t *)(log_buffer);

    cali_data->type = CALI_TYPE_LIGHT;
    cali_data->cali_dark_count = db_main.cali.dark_count;
    cali_data->cali_light_count = db_main.cali.light_count;

    cali_data->F_dark = db_main.cali.F_dark;
    cali_data->F_adc = get_als_value();
    cali_data->B_dark = db_main.cali.B_dark;

    cali_data->B_light = db_main.cali.B_light;
    cali_data->B_adc = get_gls_value();
    cali_data->B_ref = db_main.cali.B_ref;

    return 12;
}

/***********************************************************************************************
 *
 * @brief    main() - Program entry function
 * @param    none
 * @return   none
 *
 ************************************************************************************************/
int main(void)
{
    signal_init();

    hw_init();
    GlobalVar_Init();
    DB_Init();

    if (Log_Enabled)
    {
        log_on = 1;
    }
    else
    {
        log_on = 0;
    }

#ifdef ANTI_PIRACY_ENABLE
    AntiPiracy_Init();
#endif

    // if (log_on)
    {
        printf("\r\n%s\r\n", sw_version);
    }

    for (;;)
    {
        signal_process();

#ifdef EMC_TEST_ENABLE
        task_100us();
#endif

#ifdef IDLE_MODE_ENABLE
#if (MCU == C51)
        set_PCON_IDLE;
        _nop_();
        _nop_();
        _nop_();
#endif
#if (MCU == ARM)
        // HAL_GPIO_WritePin(GPIOA, GPIO_PIN_5, GPIO_PIN_SET);
        __WFI();
        // HAL_GPIO_WritePin(GPIOA, GPIO_PIN_5, GPIO_PIN_RESET);
#endif
#endif
    }
}

typedef struct
{
    uint16_t als_adc;// 环境光对应的ADC值
    uint16_t gls_adc; // 眩光对应的ADC值
    uint16_t ntc_adc;// 温度NTC对应的ADC值
    uint16_t ign_adc;// 12V电源对应的ADC值
    uint16_t ec_fb_voltage; //内镜电压 单位：0.1V
    uint16_t oec_fb_voltage;//外镜电压 单位：0.1V
    uint8_t rev_value;// 0:无倒车信号；1：倒车信号
    uint8_t reserved; // 保留
} sensor_hw_data_t;

uint8_t sensor_get_hw_data(uint8_t *buffer)
{
    sensor_hw_data_t *sensor_hw_data = (sensor_hw_data_t *)buffer;

    sensor_hw_data->als_adc = __SWP16(sensor_data.als_value);
    sensor_hw_data->gls_adc = __SWP16(sensor_data.gls_value);
    sensor_hw_data->ntc_adc = __SWP16(sensor_data.ntc);
    sensor_hw_data->ec_fb_voltage = __SWP16(sensor_data.ec_fb_voltage);
    sensor_hw_data->oec_fb_voltage = __SWP16(sensor_data.oec_fb_voltage);
    sensor_hw_data->ign_adc = __SWP16(adc_get_ign());
    sensor_hw_data->rev_value = gpio_rev_in();
    sensor_hw_data->reserved = sensor_data.als_tc;
    return sizeof(sensor_hw_data_t);
}

