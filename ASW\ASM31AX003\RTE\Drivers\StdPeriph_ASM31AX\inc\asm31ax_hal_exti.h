/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_EXTI__H // asm31x
#define __ASM31AX_EXTI__H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

    /** @defgroup GPI0_IRQ_InitTypeDef
     * @{
     */
    typedef struct
    {
        uint8_t GPIO_IRQ_Pin_Enable; /*!< GPIO中断脚使能位
                                          0: 禁止/屏蔽中断
                                          1：使能中断 */

        uint8_t GPIO_IRQ_Pin_Clear; /*!< GPIO中断脚标志位清除
                                         0: 保留中断标志位
                                         1：清除中断标志位 */

        uint8_t GPIO_IRQ_Pin_Type; /*!< 中断类型：0 --> 边沿触发中断类型
                                                  1 --> 电平触发中断类型 */

        uint8_t GPIO_IRQ_Pin_Polarity; /*!< 中断类型值：0 --> 低电平或下降沿触发中断
                                                        1 --> 高电平或上升沿触发中断 */

        uint8_t GPIO_IRQ_Pin_Edge; /*!< 端口任意边沿触发中断:
                                                    0 --> 中断触发沿由 PxIVALn决定
                                                    1 --> 上升/下降沿都触发中断 */
    } GPIO_IRQ_InitTypeDef;

    /** @defgroup EXTI_Exported_Functions
     * @{
     */

    void GPIO_EXTILineConfig(GPIO_TypeDef *GPIOx, GPIO_IRQ_InitTypeDef *GPIO_IRQ_InitStructure, uint8_t GPIO_Pin);

#ifdef __cplusplus
}
#endif

#endif /*__ASM31X_EXTI__H */
