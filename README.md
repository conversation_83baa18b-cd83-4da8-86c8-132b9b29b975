# LIN 协议文件包 - ZCUL_RLS_FRONTWIPER_IRMM

本文件包包含了基于 Excel 表格 `LIN_ZCUL_RLS_FRONTWIPER_IRMM_V1.0_20250109-send to licon 20250624.xlsx` 创建的完整 LIN 协议定义文件。

## 📁 文件结构

```
e:\SHIKONG\VCAE_IEM\
├── LIN_ZCUL_RLS_FRONTWIPER_IRMM_Protocol.ldf    # 主要的LIN协议描述文件
├── LIN_验证指南.md                              # 协议验证指南
├── LIN_测试脚本.txt                            # 测试脚本
└── README.md                                   # 本说明文档
```

## 🚗 系统概述

本 LIN 协议定义了四个主要节点之间的通信：

- **ZCUL (主节点)**: 中央控制单元，负责协调整个系统
- **RLS (从节点)**: 雨光传感器，提供雨量和光线检测
- **FRONTWIPER (从节点)**: 前雨刷控制器，执行雨刷动作
- **IRMM (从节点)**: 内部后视镜模块，提供防眩功能

## 📋 协议规格

| 参数 | 值 |
|------|----|
| LIN 协议版本 | 2.1 |
| 波特率 | 19200 bps |
| 主节点 | ZCUL |
| 从节点数量 | 3 (RLS, FRONTWIPER, IRMM) |
| 总帧数 | 7 个数据帧 + 2 个诊断帧 |
| 调度表 | Dynamic (10个时隙) |

## 🔧 主要功能

### 1. 雨刷控制系统
- 自动雨刷功能
- 雨量敏感度调节
- 前洗涤功能
- 雨刷速度控制
- 停车位置检测

### 2. 雨光传感器
- 雨量检测和等级输出
- 环境光检测
- 自动大灯控制
- 传感器故障检测
- 温湿度监测

### 3. 内部后视镜防眩
- 防眩敏感度调节
- 防眩百分比控制
- 驾驶员侧控制
- 诊断和故障检测

## 📊 帧定义

| 帧名称 | ID | PID | 长度 | 发送节点 | 主要功能 |
|--------|----|----|------|----------|----------|
| ZCUL_01 | 0x31 | 0x71 | 8 | ZCUL | 基础控制命令 |
| ZCUL_02 | 0x32 | 0x72 | 8 | ZCUL | 雨刷控制命令 |
| ZCUL_03 | 0x33 | 0x73 | 8 | ZCUL | 后视镜控制命令 |
| RLS_01 | 0x23 | 0x63 | 8 | RLS | 雨光传感器状态1 |
| RLS_02 | 0x24 | 0x64 | 8 | RLS | 雨光传感器状态2 |
| FRONTWIPER_01 | 0x25 | 0x65 | 8 | FRONTWIPER | 雨刷状态反馈 |
| IRMM_01 | 0x26 | 0xA6 | 8 | IRMM | 后视镜状态反馈 |

## 🛠️ 使用方法

### 1. 协议文件导入

将 `LIN_ZCUL_RLS_FRONTWIPER_IRMM_Protocol.ldf` 文件导入到您的 LIN 开发工具中：

- **Vector CANoe/CANalyzer**: File → Import → LIN Description File
- **PEAK LIN Tools**: Load LDF File
- **其他工具**: 参考相应工具的 LDF 导入功能

### 2. 节点配置

确保各节点的 NAD (Node Address for Diagnostic) 配置正确：

```
RLS:        NAD = 0x10
FRONTWIPER: NAD = 0x11
IRMM:       NAD = 0x12
```

### 3. 调度表配置

使用 `Dynamic` 调度表，总周期时间为 100ms：

```
ZCUL_01 → ZCUL_02 → ZCUL_03 → RLS_01 → RLS_02 → 
ZCUL_01 → FRONTWIPER_01 → IRMM_01 → RLS_01 → RLS_02
```

## ✅ 验证步骤

1. **语法验证**: 使用 LIN 工具验证 LDF 文件语法
2. **通信验证**: 参考 `LIN_验证指南.md` 进行通信测试
3. **功能验证**: 运行 `LIN_测试脚本.txt` 中的测试用例
4. **性能验证**: 检查总线负载和时序要求

## 🔍 信号编码

协议定义了以下信号编码类型：

- **DimSnvty_Encoding**: 防眩敏感度 (0=Normal, 1=Dark, 2=Light, 3=Inhibit)
- **Boolean_Encoding**: 布尔值 (0=CLOSE, 1=OPEN)
- **Percentage_Encoding**: 百分比值 (0-255)
- **Speed_Encoding**: 速度值 (0-254 times/min, 255=Unknown)
- **Temperature_Encoding**: 温度值 (-40°C to +215°C)
- **Brightness_Encoding**: 亮度值 (0-255 lux)

## 🚨 注意事项

1. **时序要求**: 确保各帧的发送时序符合调度表定义
2. **错误处理**: 实现各节点的错误信号反馈机制
3. **诊断功能**: 支持标准 LIN 诊断服务 (SID 0x22)
4. **信号范围**: 确保所有信号值在定义的范围内
5. **校验和**: 使用增强校验和 (Enhanced Checksum)

## 📞 技术支持

如果在使用过程中遇到问题，请检查：

1. LDF 文件语法是否正确
2. 节点 NAD 配置是否匹配
3. 信号定义是否与实际硬件一致
4. 调度表时序是否合理

## 📝 版本历史

| 版本 | 日期 | 修改内容 |
|------|------|----------|
| 1.0 | 2025-01-24 | 基于 Excel 表格创建初始版本 |

## 📄 相关文档

- `LIN_验证指南.md`: 详细的验证步骤和检查清单
- `LIN_测试脚本.txt`: 自动化测试脚本
- `LIN_ZCUL_RLS_FRONTWIPER_IRMM_V1.0_20250109-send to licon 20250624.xlsx`: 原始需求文档

---

**创建日期**: 2025-01-24  
**基于文档**: LIN_ZCUL_RLS_FRONTWIPER_IRMM_V1.0_20250109-send to licon 20250624.xlsx  
**协议版本**: LIN 2.1