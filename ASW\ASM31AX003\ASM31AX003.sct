; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 0x00000000 0x00007800  {    ; load region size_region
  ER_IROM1 0x00000000 0x00007800  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
  }

  ;ER_IROM3 0x0000100 0x000020  {  ; load address = execution address
   ;sw_version.o (+RO)
   ;.ANY (sw_version)
  ;}

  RW_IRAM1 0x20000000 0x00001000  {  ; RW data
   .ANY (+RW +ZI)
  }
}


LR_IROM2 0x00007800 0x00008800  {
  ER_IROM2 0x00007800 0x00008800  {  ; load address = execution address
   parm.o (+RO)
  }
}

