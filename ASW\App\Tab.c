/*
  Copyright (C), 2018-2019, VCAE. Co., Ltd.
  FileName:    tab.c
  Description:  ASW 标定数据的表格处理
  Version: 0.1
  Function List:
    1.
  History:
      <author>   <time>      <version >                <desc>
        benny      19/11/1        0.1                  新创建模块
*/


/**********************************************************************************************
* External objects
**********************************************************************************************/
#include "config.h"
#include "drv.h"
#include "db.h"

static uint8_t get_index(uint8_t *tab,uint8_t tab_length,uint8_t value)
{
    uint8_t i;

    for (i=0;i<tab_length;i++)
    {
        if (value<tab[i])
        {
            break;
        }
    }

    return i;
}


uint8_t TAB_GetAlsIndex(uint16_t als_value)
{
    uint8_t index,value8;
	if(als_value>0xFF)
	{
		value8=0xFF;
	}
	else
	{
    	value8=(uint8_t)als_value;
	}
	index=get_index(Tab_ALS,Tab_Length,value8);
    return index;

}

uint8_t TAB_GetCompensation(uint8_t index)
{
    uint8_t compensation;

	if(index<Tab_Length)
	{
		compensation=Tab_compensation[index];
	}
	else
	{
		compensation=0;
	}

	return compensation;

}

uint16_t TAB_GetTarget(uint8_t index)
{
    uint16_t target;

	if(index<Tab_Length)
	{
		target=Tab_Target[index];
	}
	else
	{
		target=0;
	}
	
    
	
    return target;
}


/*
    1.表格递减，温度越高adc值越小
    2.大于第一个温度按常温处理
*/
uint8_t TAB_GetTemperatureIndex(uint16_t value)
{
    uint8_t i;

	// 如果NTC电路故障，直接返回常温
	if(value < 50)
		return 0;

    for (i=0;i<Tab_Length;i++)
    {
        if (value>Tab_Temperature[i])
        {
            break;
        }
    }

    return i;
}


uint8_t TAB_GetTarget_Linear(uint8_t als_value)
{
    uint8_t target,i;
	uint16_t value;

    for (i=0;i<Tab_Length;i++)
    {
        if (als_value<Tab_ALS[i])
        {
            break;
        }
    }

	if(i>=Tab_Length)
	{
		//assert  
		target=0xFF;
	}
	else
	{
		if(i==0)
		{
			target=Tab_Target[i];
		}
		else
		{
			value=Tab_Target[i]-Tab_Target[i-1];
			value *= (als_value-Tab_ALS[i-1]);
			value /= (Tab_ALS[i]-Tab_ALS[i-1]);
			value += Tab_Target[i-1];

			target=(uint8_t)value;
		}
	}
	
    
	
    return target;
}


