#ifndef _LIN_STACK_H_
#define _LIN_STACK_H_

#include "config.h"


/*接收数据需要解析位置*/
#define MIRRCMD             4     //CemCem_Lin1Fr01  B[4]
/*发送数据需要解析位置*/
#define MIRRDIMPERC         0     //IrmmCem_Lin1Fr01 B[0]

#define TRUE                1
#define FALSE               0
typedef union
{
    unsigned char mirr_buf;
    struct
    {
        unsigned char IntrMirrCmdIntrMirrDimSnvty : 2;
        unsigned char IntrMirrCmdDrvrSide : 1;
        unsigned char IntrMirrCmdIntrMirrWindHeatrCmpMag : 1;
        unsigned char IntrMirrCmdIntrMirrEna : 1;
        unsigned char IntrMirrCmdIntrMirrInhbDim : 1;
        unsigned char IntrMirrCmdIntrMirrDiagcRst : 1;
        unsigned char IntrMirrCmdIntrMirrAsyFanCmpMag : 1;
    }INTRMIRRBIT;
}uINTRMIRRCMD;

typedef union
{
    unsigned char IntrMirrRespFailrandReserv_BIT;
    struct
    {
        unsigned char IntrMirrRespIntrMirrIntFailr : 1;
        unsigned char IntrMirrRespResdBoolean : 1;
        unsigned char IntrMirrRespResdUInt6 : 6;
    }IntrMirrRespFailrandReservBIT;
}IntrMirrResp_FailrandReserv;

typedef struct
{
    unsigned char IntrMirrRespIntrMirrDimPerc;
    IntrMirrResp_FailrandReserv IntrMirrRespFailrandReserv;
}uSIGGRP_IntrMirrResp;

/*IntrMirrCmdIntrMirrDimSnvty类型*/
typedef enum
{
    Dim_Normal = 0,
    Dim_Dark = 1,
    Dim_Light = 2,
    Dim_Inhibit = 3,
}DIMSNVTY_TYPE;

/*IntrMirrCmdIntrMirrEna类型*/
typedef enum
{
    Dis_Auto = 0,
    ENABLE_Auto = 1,
}MIRRENA_TYPE;

/*IntrMirrCmdIntrMirrInhbDim类型*/
typedef enum
{
    Normal_Oper = 0,
    EC_Oper = 1,
}MIRRINHBDIM;

typedef enum
{
    Sig_SerNoIRMMNr1 = 0x01,
    Sig_SerNoIRMMNr2 = 0x02,
    Sig_SerNoIRMMNr3 = 0x03,
    Sig_SerNoIRMMNr4 = 0x04,
}SIGGRP_SERNOIRMM;

typedef enum
{
    Sig_PartNoIRMMEndSgn1 = LIN_SN_VERSION_SUFFIX1, //ASCII码
    Sig_PartNoIRMMEndSgn2 = LIN_SN_VERSION_SUFFIX2, //ASCII码
    Sig_PartNoIRMMEndSgn3 = LIN_SN_VERSION_SUFFIX3, //ASCII码
    Sig_PartNoIRMMNr1 = 0x01,
    Sig_PartNoIRMMNr2 = 0x02,
    Sig_PartNoIRMMNr3 = 0x03,
    Sig_PartNoIRMMNr4 = 0x04,
}SIGGRP_PARTNOIRMM;

typedef enum
{
    Sig_PartNo10IRMMEndSgn1 = LIN_SN_VERSION_SUFFIX1, //ASCII码
    Sig_PartNo10IRMMEndSgn2 = LIN_SN_VERSION_SUFFIX2, //ASCII码
    Sig_PartNo10IRMMEndSgn3 = LIN_SN_VERSION_SUFFIX3, //ASCII码
    Sig_PartNo10IRMMNr1 = 0x66,
    Sig_PartNo10IRMMNr2 = 0x08,
    Sig_PartNo10IRMMNr3 = 0x03,
    Sig_PartNo10IRMMNr4 = 0x68,
    Sig_PartNo10IRMMNr5 = 0x34,
}SIGGRP_PARTNO10IRMM;

/**转换以后的参数**/
typedef enum
{   //sig grp                   id
    ID_IrmmCem_Lin1Fr01 = 0x10,
    ID_IrmmCem_Lin1PartNrFr01 = 0x12,
    ID_IrmmCem_Lin1SerNrFr01 = 0x14,
    ID_IrmmCem_Lin1PartNrFr02 = 0x16,
    ID_CemCem_Lin1Fr01 = 0x05,
    ID_CemCem_Lin1Fr03 = 0x17,
}LIN_MASTER_CMD_ID;

/**转换以后的参数**/
typedef enum
{   //sig grp                    pid     id
    PID_IrmmCem_Lin1Fr01 = 0x50,//0x10,  
    PID_IrmmCem_Lin1PartNrFr01 = 0x92,//0x12,
    PID_IrmmCem_Lin1SerNrFr01 = 0x14,//0x14,
    PID_IrmmCem_Lin1PartNrFr02 = 0xD6,//0x16,
    PID_CemCem_Lin1Fr01 = 0X85,//0x05
    PID_CemCem_Lin1Fr03 = 0x97,//0x17
#if (PRJ_NAME == D21)
    PID_ZCUL_03 = 0x73,//0x33 - 内后视镜防眩命令(根据Excel表格定义)
#endif
#if LIN_WAKE_0X3C_SUPPORT
    PID_MASTERReq = 0x3C,//0x3C
#endif
}LIN_MASTER_CMD_PID;

typedef enum
{
    LEN_IrmmCem_Lin1Fr01 = 5,
    LEN_IrmmCem_Lin1PartNrFr01 = 8,
    LEN_IrmmCem_Lin1SerNrFr01 = 5,
    LEN_IrmmCem_Lin1PartNrFr02 = 9,
    LEN_CemCem_Lin1Fr01 = 7,
    LEN_CemCem_Lin1Fr03 = 9,
#if (PRJ_NAME == D21)
    LEN_ZCUL_03 = 8,//根据Excel表格，ZCUL_03报文长度为8字节
#endif
#if LIN_WAKE_0X3C_SUPPORT
    LEN_MASTEReq = 9,
#endif
}LIN_FRAME_LEN;

typedef enum 
{
	LEN_Rx_IrmmCem_Lin1Fr01       = 2,  
    LEN_Rx_IrmmCem_Lin1PartNrFr01 = 2,
    LEN_Rx_IrmmCem_Lin1SerNrFr01  = 2,
    LEN_Rx_IrmmCem_Lin1PartNrFr02 = 2,
    LEN_Rx_CemCem_Lin1Fr01        = 9,
    LEN_Rx_CemCem_Lin1Fr03        = 11,
#if (PRJ_NAME == D21)
    LEN_Rx_ZCUL_03                = 10,//PID(1) + Data(8) + Checksum(1)
#endif
#if LIN_WAKE_0X3C_SUPPORT
    LEN_Rx_MASTEReq = 11,
#endif
}LEN_FRAME_RX_LEN;

typedef struct 
{
	LIN_MASTER_CMD_PID     master_cmd;   //接收的cmd
	unsigned char          rx_len;       //接收长度
}RX_PARAM_LEN;

#if LIN_ERROR_RESPONSE
typedef struct
{
	LIN_MASTER_CMD_PID     master_cmd;   //接收的cmd
	unsigned char          tx_len;       //发送长度
}TX_PARAM_LEN;
#endif

typedef enum 
{
	BIT_ERROR,
	FRAME_SYNC_ERROR,
	PID_ERROR,
	NO_RESPONSE_ERROR,
	RESPONSE_INCOMPLETE_ERROR,
	CHECKSUM_ERROR,
	FRAME_ERROR,
	BUS_ERROR,
	NONE_ERROR,
}ERROR_TYPE;

unsigned char check_return_len(unsigned char buffer);
void lin_stack_recv(unsigned char *buffer, unsigned char len);
void lin_stack_send(unsigned char *buffer, unsigned char len, unsigned char clear_fifo);
void lin_stack_single_rx(unsigned char buffer);
void clean_error_type(ERROR_TYPE err_type);
void set_error_type(ERROR_TYPE err_type);

#if LIN_ERROR_RESPONSE
void lin_stack_send_isr(unsigned char* buffer, unsigned char len, unsigned char sendlen, unsigned char clear_fifo);
#endif

#endif