/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_TIM_H
#define __ASM31AX_TIM_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

    /**
     * @brief  TIM Time Base Init structure definition
     * @note   This structure is used with TIM10/11
     */
    typedef struct
    {
        BASETIM_TypeDef *Instance;
        uint32_t GatePolarity; /*!<Port polarity control / 端口Gate极性控制 */

        uint32_t GateEn; /*!<Gate control / 定时器门控 0：无门控，TR=1 时定时器工作
                                                         1：只有端口 GATE 有效并且 TR=1 时才工作 */
        uint32_t TogEn;

        uint32_t WorkMode; /*!<TIM working mode:timering/counting /计数模式 or 定时模式*/

        uint32_t CountMode; /*!<TIM mode select:free-running/periodic*/

        uint32_t CountSize; /*!<Counting mode:16bits/32bits*/

        uint32_t OneShot; /*!<Counting mode:wrapping/one_shot 计数器运行一次使能 0：重复运行;   1：运行一次*/

        uint32_t ClockDivision; /*!< Specifies the clock division.
                                     This parameter can be a value of @ref TIM_Clock_Division_CKD */
        uint32_t Period;

        uint32_t TogPin;
        uint32_t TogNPin;

        uint32_t ExtPin;
        uint32_t GatePin;

    } TIM_TimeBaseInitTypeDef;

/*Control register Mask*/
#define TMRxCON_MASK (uint32_t)0x00000280

/** @defgroup TIM_Exported_constants
 * @{
 */
#define IS_BASETIM_ALL_PERIPH(PERIPH) (((PERIPH) == TMER10) || \
                                       ((PERIPH) == TMER11))

/** @defgroup TIM_Clock_Division_CKD
 * @{
 */
#define TIM_CLOCK_DIV0 ((uint32_t)0x00000000)   /*!< TIM 预除频选择: 1分频 */
#define TIM_CLOCK_DIV2 ((uint32_t)0x00000001)   /*!< TIM 预除频选择: 2分频 */
#define TIM_CLOCK_DIV4 ((uint32_t)0x00000002)   /*!< TIM 预除频选择: 4分频 */
#define TIM_CLOCK_DIV8 ((uint32_t)0x00000003)   /*!< TIM 预除频选择: 8分频 */
#define TIM_CLOCK_DIV16 ((uint32_t)0x00000004)  /*!< TIM 预除频选择: 16分频 */
#define TIM_CLOCK_DIV32 ((uint32_t)0x00000005)  /*!< TIM 预除频选择: 32分频 */
#define TIM_CLOCK_DIV64 ((uint32_t)0x00000006)  /*!< TIM 预除频选择: 64分频 */
#define TIM_CLOCK_DIV128 ((uint32_t)0x00000007) /*!< TIM 预除频选择: 128分频 */

#define IS_TIM_CLOCK_DIV(DIV) (((DIV) == TIM_CLOCK_DIV0) ||  \
                               ((DIV) == TIM_CLOCK_DIV2) ||  \
                               ((DIV) == TIM_CLOCK_DIV4) ||  \
                               ((DIV) == TIM_CLOCK_DIV8) ||  \
                               ((DIV) == TIM_CLOCK_DIV16) || \
                               ((DIV) == TIM_CLOCK_DIV32) || \
                               ((DIV) == TIM_CLOCK_DIV64) || \
                               ((DIV) == TIM_CLOCK_DIV128))

/** @defgroup TIM_Counter_Mode
 * @{
 */
/*set free_runing/periodic 定时器工作模式*/
#define TIM_COUNT_MODE_FREERUN ((uint32_t)0x00000000)  /*!< 0：模式 1 计数器/定时器 */
#define TIM_COUNT_MODE_PERIODIC ((uint32_t)0x00000040) /*!< 1：模式 2 自动重装载计数器/定时器 */

/*set TIM interrupt enable 中断使能控制*/
#define TIM_COUNTER_TMRIE_DISABLE ((uint32_t)0x00000000) /*!< 定时器禁止中断 */
#define TIM_COUNTER_TMRIE_ENABLE ((uint32_t)0x00000020)  /*!< 定时器使能中断 */

/*set 16bits/32bits's counting/设置定时器最大计数值 */
#define TIM_COUNT_SIZE_16BIT ((uint32_t)0x00000000) /*!< 16位计数器 */
#define TIM_COUNT_SIZE_32BIT ((uint32_t)0x00000010) /*!< 32位计数器 */

/*set wrapping(重复模式)/one_shot*/
#define TIM_ONESHOT_DISABLE ((uint32_t)0x00000000) /*!< 0:重复模式 */
#define TIM_ONESHOT_ENABLE ((uint32_t)0x00000008)  /*!< 1：oneshot 模式 */

#define IS_TIM_COUNTER_MODE(MODE) (((MODE) == TIM_COUNT_MODE_FREERUN) ||    \
                                   ((MODE) == TIM_COUNT_MODE_PERIODIC) ||   \
                                   ((MODE) == TIM_COUNTER_TMRIE_DISABLE) || \
                                   ((MODE) == TIM_COUNTER_TMRIE_ENABLE) ||  \
                                   ((MODE) == TIM_COUNT_SIZE_16BIT) ||      \
                                   ((MODE) == TIM_COUNT_SIZE_32BIT) ||      \
                                   ((MODE) == TIM_ONESHOT_DISABLE) ||       \
                                   ((MODE) == TIM_ONESHOT_ENABLE))

/** @defgroup TIM_CT_MODE计数模式/定时模式选择
 * @{
 */
#define TIM_WORKMODE_TIMER ((uint32_t)0x00000000)   /*!< 定时模式 */
#define TIM_WORKMODE_COUNTER ((uint32_t)0x00000100) /*!< 计数模式 */
#define IS_TIM_CT_WORKMODE(MODE) (((MODE) == TIM_WORKMODE_TIMER) || \
                                  ((MODE) == TIM_WORKMODE_COUNTER))

/** @defgroup TIM_TOG_Output/TIM反转输出
 * @{
 */
/*TOG and TOGN Output the same value "0"*/
#define TIM_TOG_EN ((uint32_t)0x000000000)
/*TOG and TOGN Output the contrary value*/
#define TIM_TOG_ENABLE ((uint32_t)0x000000200)
#define TIM_TOG_DISABLE ((uint32_t)0x000000000)
#define IS_TIM_TOG_EN(TOGEN) (((TOGEN) == TIM_TOG_EN) || \
                              ((TOGEN) == TIM_TOG_NE))

/** @defgroup TIM_Gate_Control/TIM门控制位选择
 * @{
 */
#define TIM_GATE_DISABLE ((uint32_t)0x000000000) /*!< Gate门控禁止 */
#define TIM_GATE_ENABLE ((uint32_t)0x000000400)  /*!< Gate门控使能 */
#define IS_GATE_STATE(STATE) (((STATE) == TIM_GATE_DISABLE) || \
                              ((STATE) == TIM_GATE_ENABLE))
/**
 * @}TIM_TR_ENABLE
 */
/*TR close*/
#define TIM_TR_DISABLE ((uint32_t)0x000000000)
/*TR run*/
#define TIM_TR_ENABLE ((uint32_t)0x000000080)
#define IS_TR_STATE(STATE) (((STATE) == TIM_TR_DISABLE) || \
                            ((STATE) == TIM_TR_ENABLE))

/**
 * @}TIM_IT_Enable
 */
#define TIM_IT_DISABLE ((uint32_t)0x000000000)
#define TIM_IT_ENABLE ((uint32_t)0x000000020)
#define IS_IT_STATE(STATE) (((STATE) == TIM_TR_DISABLE) || \
                            ((STATE) == TIM_TR_ENABLE))

/**
 * @}TIM_IT_FLAG
 */
#define TIM_IT_FLAG ((uint32_t)0x000000001)

/** @defgroup GatePolarity
 * @{
 */
/*Gate active high*/
#define TIM_GATE_POLARITY_HIGH ((uint32_t)0x000000000) /*!< 门控极性高电平有效 */
/*Gate active low*/
#define TIM_GATE_POLARITY_LOW ((uint32_t)0x000000800) /*!< 门控极性低电平有效 */
#define TIM_GATE_POLARITY_STATE(STATE) (((STATE) == TIM_GATE_POLARITY_HIGH) || \
                                        ((STATE) == TIM_GATE_POLARITY_LOW))

    /** @defgroup TIM_Exported_Functions
     * @{
     */
    void HAL_BASETIM_DeInit(BASETIM_TypeDef *TIMx);
    void HAL_BASETIM_Init(TIM_TimeBaseInitTypeDef *TIM_TimeBaseInitStruct);
    void HAL_BASETIM_StructInit(TIM_TimeBaseInitTypeDef *TIM_TimeBaseInitStruct);
    void HAL_BASETIM_Cmd(BASETIM_TypeDef *TIMx, FunctionalState NewState);
    void HAL_BASETIM_ITConfig(BASETIM_TypeDef *TIMx, FunctionalState NewState);
    void HAL_BASETIM_GenerateEvent(BASETIM_TypeDef *TIMx, uint32_t TIM_EventSource);
    void HAL_BASETIM_PrescalerConfig(BASETIM_TypeDef *TIMx, uint32_t Prescaler, uint16_t TIM_PSCReloadMode);
    void HAL_BASETIM_CounterModeConfig(BASETIM_TypeDef *TIMx, uint32_t Mode);
    void HAL_BASETIM_SelectGatePolarity(BASETIM_TypeDef *TIMx, uint32_t TIM_GatePolarity);
    void HAL_BASETIM_SetTimerLoadRegister(BASETIM_TypeDef *TIMx, uint32_t TIM_Value);
    uint32_t HAL_BASETIM_GetCountRegister(BASETIM_TypeDef *TIMx);
    void HAL_BASETIM_SetAutoreload(BASETIM_TypeDef *TIMx, uint32_t Autoreload);
    void HAL_BASETIM_SetClockDivision(BASETIM_TypeDef *TIMx, uint32_t TIM_DIV);
    uint32_t HAL_BASETIM_GetTimerLoadRegister(BASETIM_TypeDef *TIMx);
    uint16_t HAL_BASETIM_GetPrescaler(BASETIM_TypeDef *TIMx);
    ITStatus HAL_BASETIM_GetITSourceFlagStatus(BASETIM_TypeDef *TIMx, uint32_t TIM_ITFLAG);
    ITStatus HAL_BASETIM_GetITShieldFlagStatus(BASETIM_TypeDef *TIMx, uint32_t TIM_ITFLAG);
    void HAL_BASETIM_ClearITFlag(BASETIM_TypeDef *TIMx, uint32_t TIM_ITFLAG);
    void HAL_BASETIM_TogCmd(BASETIM_TypeDef *TIMx, FunctionalState NewState);

#ifdef __cplusplus
}
#endif

#endif /*__ASM31X_TIM_H */
