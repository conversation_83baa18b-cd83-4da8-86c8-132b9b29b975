#ifndef __UART_H
#define __UART_H

#include "sys.h"
#define UART0_Timer1  0
#define UART0_Timer3  1
#define UART1_Timer3  2
#define UART0 0
#define UART1 1
void UART_Init(uint32_t Baudrate);
uint8_t UART_ReceiveData(void);
void UART_SendData(uint8_t Data);

void UART0_Init(uint32_t Baudrate);

uint8_t UART1_ReceiveData(void);
void UART1_Init(void);
void UART_Open(unsigned long u32SysClock, unsigned char u8UARTPort, unsigned long u32Baudrate);
void UART_Send_Data(unsigned char UARTPort, unsigned char c);
unsigned char Receive_Data(unsigned char UARTPort);
void UART_Interrupt_Enable(unsigned char u8UARTPort, unsigned char u8UARTINTStatus);
void Enable_UART0_VCOM_printf_24M_115200(void);

#endif
