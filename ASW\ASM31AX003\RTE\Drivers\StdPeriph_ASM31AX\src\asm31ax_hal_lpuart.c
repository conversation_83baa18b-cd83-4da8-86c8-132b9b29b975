#include "asm31ax_hal.h"

#ifdef HAL_LPUART_MODULE_ENABLED

/* Includes ------------------------------------------------------------------*/

uint8_t LPUART_FLAG = 0;

/**
 * @brief  Deinitializes the LPUARTx peripheral registers to their default reset values.
 * @param  LPUARTx: Select the LPUART or the LPUART peripheral.
 *         This parameter can be one of the following values:
 *         LPUART.
 * @retval None
 */
void HAL_LPUART_DeInit(LPUART_TypeDef *LPUARTx)
{
    /* Check the parameters */
    assert_param(IS_LPUART_ALL_PERIPH(LPUARTx));

    if (LPUARTx == LPUART)
    {
        /* Enable LPUART reset state */
        RCC_PeriphResetCmd(RCC, RCC_APBPeriph_LPUARTRST, ENABLE);
        /* Release LPUART from reset state */
        RCC_PeriphResetCmd(RCC, RCC_APBPeriph_LPUARTRST, DISABLE);
    }
}

/**
 * @brief  Initializes the LPUARTx peripheral according to the specified
 *         parameters in the LPUART_InitStruct .
 * @param  LPUARTx: Select the LPUART peripheral.
 *     This parameter can be one of the following values: LPUART
 * @param  TIMERx: Select the LPTIMER peripheral.
 *     This parameter can be one of the following values: LPTIMER
 * @param  LPUART_InitStruct: pointer to a LPUART_InitTypeDef structure
 *         that contains the configuration information for the specified LPUART
 *         peripheral.
 * @param  LPModeState: new state of the LPUART peripheral.
 * 	This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_LPUART_Init(LPUART_InitTypeDef *LPUART_InitStruct)
{
    float baudrate = 0;
    uint32_t pclk = 0;
    /* Check the parameters */
    assert_param(IS_LPUART_BAUDRATE(LPUART_InitStruct->BaudRate));
    assert_param(IS_LPUART_MODE(LPUART_InitStruct->Mode));
    assert_param(IS_LPUART_SCLK(LPUART_InitStruct->SclkSel));
    assert_param(IS_LPUART_SCLK(LPUART_InitStruct->SclkSel));

    pclk = HAL_RCC_GetPclk();

    /*Initialization lpuart mode of work*/
    LPUART_InitStruct->Instance->SCON &= ~(LPUART_SCON_SM1 | LPUART_SCON_SM0); // LPUART_SCON.bit6 = 0, LPUART_SCON.bit7 = 0
    LPUART_InitStruct->Instance->SCON &= ~LPUART_SCON_DBAUD;                   // LPUART_SCON.bit9 = 0,单倍波特率

    /*config mode0/mode2  fixed baud*/
    if ((LPUART_InitStruct->Mode == LPUART_MODE0) ||
        (LPUART_InitStruct->Mode == LPUART_MODE2))
    {
        LPUART_InitStruct->Instance->SCON |= (LPUART_InitStruct->Mode << 6);        // 设置实际工作模式
        LPUART_InitStruct->Instance->SCON |= (LPUART_InitStruct->BraudDouble << 9); // 设置双倍波特率
    }
    else
    {
        /*config mode1/mode3  variable baud*/
        if ((LPUART_InitStruct->Mode == LPUART_MODE1) ||
            (LPUART_InitStruct->Mode == LPUART_MODE3))
        {
            LPUART_InitStruct->Instance->SCON |= (LPUART_InitStruct->Mode << 6);        // 设置实际工作模式
            LPUART_InitStruct->Instance->SCON |= (LPUART_InitStruct->BraudDouble << 9); // 设置双倍波特率
            /* LPUART_SCON.LPMOD = 0 (不使能低功耗模式)*/
            if (LPUART_InitStruct->LPModeState != ENABLE)
            {
                LPUART_InitStruct->Instance->SCON &= ~LPUART_SCON_LPMODE; // LPUART_SCON.LPMOD = 0,正常工作模式
                /*Use LPTIMER to set baud rate*/
                if (LPUART_InitStruct->TimerEnable)
                {
                    /*Close lptimer interrupt enable*/
                    NVIC_DisableIRQ(LPTIMER_IRQn);
                    LPUART_InitStruct->Instance->BAUDCR &= ~LPUART_SELF_BRG; // 设置波特率产生方式由LPTIMER产生
                    /*Calculating baud rate,4 homes and 5 entries*/
                    baudrate = (1.0) * pclk * (LPUART_InitStruct->BraudDouble + 1) /
                                   (LPUART_InitStruct->BaudRate * 32) +
                               0.5;
                    /*config lptimer load register*/
                    LPTIMER->LOAD = (0x01 << 16) - (int16_t)baudrate;
                    /*config lptimer BGLOAD register*/
                    LPTIMER->BGLOAD = (0x01 << 16) - (int16_t)baudrate;
                    /*config lptimer CR register*/
                    LPTIMER->CR = LPUART_LPTIM_EABLE; // LPTIMER计数时钟使能，TOG输出使能，自动重装载 16 位计数器/定时器
                    /*open lptimer*/
                    LPTIMER->CR |= LPUART_LPTIMRUN_EABLE;
                }
                /*Setting Baud Rate with Self Clock*/
                else
                {
                    LPUART->BAUDCR |= LPUART_SELF_BRG;
                    /*Calculating baud rate,4 homes and 5 entries*/
                    baudrate = (1.0) * pclk * (LPUART_InitStruct->BraudDouble + 1) /
                                   (LPUART_InitStruct->BaudRate * 32) +
                               0.5;
                    LPUART->BAUDCR |= (int16_t)baudrate - 1;
                }
            }
            else /* LPUART_SCON.LPMOD = 1 (使能低功耗模式)*/
            {
                LPUART_InitStruct->Instance->SCON |= LPUART_SCON_LPMODE; // 使能低功耗模式
                LPUART_InitStruct->Instance->SCON |= (LPUART_InitStruct->Mode << 6);
                LPUART_InitStruct->Instance->SCON &= ~LPUART_SCLK_MASK;
                LPUART_InitStruct->Instance->SCON &= ~LPUART_SCLK_DIV_MASK; // 传输时钟SCLK位清0
                /*Configuration Clock Selection*/
                LPUART_InitStruct->Instance->SCON |= LPUART_InitStruct->SclkSel;
                /*Configuring Clock Frequency Division*/
                LPUART_InitStruct->Instance->SCON |= LPUART_InitStruct->SclkDiv;
            }
        }
    }

    LPUART_InitStruct->Instance->SCON |= LPUART_SCON_EN_Msk;

    if (LPUART_InitStruct->ITEnable == LPUART_IT_TIEN)
    {
        HAL_LPUART_ITConfig(LPUART_InitStruct->Instance, LPUART_TIEN_EABLE, ENABLE);
    }
    else if (LPUART_InitStruct->ITEnable == LPUART_IT_RIEN)
    {

        HAL_LPUART_ITConfig(LPUART_InitStruct->Instance, LPUART_RIEN_EABLE, ENABLE);
    }
    else if (LPUART_InitStruct->ITEnable == LPUART_IT_TIRIEN)
    {
        HAL_LPUART_ITConfig(LPUART_InitStruct->Instance, LPUART_TIEN_EABLE, ENABLE);
        HAL_LPUART_ITConfig(LPUART_InitStruct->Instance, LPUART_RIEN_EABLE, ENABLE);
    }
}

/**
 * @brief  Fills each LPUART_InitStruct member with its default value.
 * @param  LPUART_InitStruct: pointer to a LPUART_InitTypeDef structure
 *         which will be initialized.
 * @retval None
 */
void HAL_LPUART_StructInit(LPUART_InitTypeDef *LPUART_InitStruct)
{
    /* LPUART_InitStruct members default value */
    LPUART_InitStruct->BaudRate = 9600;
    LPUART_InitStruct->Mode = LPUART_MODE0;
    LPUART_InitStruct->BraudDouble = 0x00;
}

/**
  * @brief  Enables or disables the specified LPUART peripheral.
  * @param  LPUARTx: Select the LPUART or the LPUART peripheral.
  *         This parameter can be one of the following values:
  *           LPUART
  * @param  FuncState: Lpuart function status
  *         This parameter can be one of the following values:
              LPUART_RXEN_EABLE
              LPUART_TXEN_EABLE
  * @param  NewState: new state of the LPUARTx peripheral.
  *         This parameter can be: ENABLE or DISABLE.
  * @retval None
  */
void HAL_LPUART_Cmd(LPUART_TypeDef *LPUARTx, uint16_t FuncState, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_USART_ALL_PERIPH(LPUARTx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));
    LPUARTx->SCON &= ~LPUART_SCON_REN; // 使能发送TxD

    if (FuncState == LPUART_RXEN_EABLE)
    {
        if (NewState != DISABLE)
        {
            /* Enable the selected LPUART by setting the REN bit in the SCON register */
            LPUARTx->SCON |= LPUART_SCON_REN; // 使能接收RxD
        }
        else
        {
            LPUARTx->SCON &= ~LPUART_SCON_REN; // 使能发送TxD
        }
    }
    else
    {
        /*Send function*/
        LPUARTx->SCON &= (uint16_t) ~((uint16_t)LPUART_SCON_REN);
    }
}

/**
 * @brief  Enables or disables  LPUART LowPower mode.
 * @param  LPUARTx: Select the LPUART or the LPUART peripheral.
 *         This parameter can be one of the following values:
 *           LPUART.
 * @param  NewState: new state of the LPUARTx peripheral.
 *         This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_LPUART_LowPowerCmd(LPUART_TypeDef *LPUARTx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_USART_ALL_PERIPH(LPUARTx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState != DISABLE)
    {
        /*Open LPUART LowPower mode*/
        LPUARTx->SCON |= LPUART_SCON_LPEN;
    }
    else
    {
        /*Close LPUART LowPower mode*/
        LPUARTx->SCON &= ~LPUART_SCON_LPEN;
    }
}

/**
 * @brief  Enables or disables the specified LPUART interrupts.
 * @param  LPUARTx: Select the LPUART or the LPUART peripheral.
 *   This parameter can be one of the following values:
 *   LPUART
 * @param  LPUART_IT: specifies the LPUART interrupt sources to be enabled or disabled.
 *   This parameter can be one of the following values:
 *     @arg LPUART_RIEN_EABLE:  接收完成中断使能
 *     @arg LPUART_TIEN_EABLE:  发送完成中断使能
 * @param  NewState: new state of the specified LPUARTx interrupts.
 *   This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_LPUART_ITConfig(LPUART_TypeDef *LPUARTx, uint32_t LPUART_IT, FunctionalState NewState)
{
    /*Check the parameters */
    assert_param(IS_USART_ALL_PERIPH(LPUARTx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));
    assert_param(IS_LPUART_ITENABLE(LPUART_IT));

    if (NewState != DISABLE)
    {
        /*Enable the lpuart interrupts*/
        LPUARTx->SCON |= LPUART_IT;
    }
    else
    {
        /*Close the lpuart interrupts*/
        LPUARTx->SCON &= ~LPUART_IT;
    }
}

/**
 * @brief  Sets the address of the LPUART node.
 * @param  LPUARTx: Select the LPUART or the LPUART peripheral.
 *   This parameter can be one of the following values:LPUART
 * @param  LPUART_Address: Indicates the address of the LPUART node.
 * @param  LPUART_Address_Enable: LPUART Address Enable parameters.
 * @retval None
 */
void HAL_LPUART_SetAddress(LPUART_TypeDef *LPUARTx, uint8_t LPUART_Address, uint8_t LPUART_Address_Enable)
{
    /* Check the parameters */
    assert_param(IS_LPUART_ALL_PERIPH(LPUARTx));
    assert_param(IS_LPUART_ADDRESS(LPUART_Address));

    /* Clear the LPUART address */
    LPUARTx->SADDR &= LPUART_Address_Mask;
    /* Set the LPUART address node */
    LPUARTx->SADDR |= LPUART_Address;

    /* Clear the LPUART Address enable*/
    LPUARTx->SADEN &= LPUART_Address_Mask;
    /* Clear the LPUART Address enable*/
    LPUARTx->SADEN |= LPUART_Address_Enable;
}

/**
 * @brief  Enables or disables the LPUART LIN mode.
 * @param  LPUARTx: Select the LPUART or the LPUART peripheral.
 *   This parameter can be one of the following values:LPUART
 * @param  LPUART_InitStruct:  pointer to a LPUART_InitTypeDef structure
 *         which will be configed.
 * @param  NewState: new state of the LPUART mode.
 *   This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_LPUART_ReciveCmd(LPUART_TypeDef *LPUARTx, LPUART_InitTypeDef *LPUART_InitStruct, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_LPUART_ALL_PERIPH(LPUARTx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    if (NewState)
    {
        /*Setting Receive Enables*/
        if ((LPUART_InitStruct->Mode == LPUART_MODE0) ||
            (LPUART_InitStruct->Mode == LPUART_MODE1))
        {
            LPUARTx->SCON |= LPUART_SCON_REN;
            LPUARTx->ISR &= ~LPUART_ISR_RI;
        }
        else if ((LPUART_InitStruct->Mode == LPUART_MODE2) ||
                 (LPUART_InitStruct->Mode == LPUART_MODE3))
        {
            LPUARTx->ISR &= ~LPUART_ISR_RI;
        }
    }
    else
    {
        /*Setting Send Enables*/
        LPUARTx->SCON &= LPUART_SCON_REN;
    }
}

/**
 * @brief  Transmits single data through the LPUARTx peripheral.
 * @param  LPUARTx: Select the LPUART or the LPUART peripheral.
 *   This parameter can be one of the following values: LPUART
 * @param  Data: the data to transmit.
 * @retval None
 */
void HAL_LPUART_SendData(LPUART_TypeDef *LPUARTx, uint8_t Data)
{
    /* Check the parameters */
    assert_param(IS_LPUART_ALL_PERIPH(LPUARTx));

    /* Transmit Data */
    LPUARTx->SBUF = (uint32_t)Data;
}

/**
 * @brief  Returns the most recent received data by the LPUARTx peripheral.
 * @param  LPUARTx: Select the LPUART or the LPUART peripheral.
 *   This parameter can be one of the following values: LPUART
 * @retval The received data.
 */
uint8_t HAL_LPUART_ReceiveData(LPUART_TypeDef *LPUARTx)
{
    /* Check the parameters */
    assert_param(IS_LPUART_ALL_PERIPH(LPUARTx));

    /* Receive Data */
    return (uint8_t)(LPUARTx->SBUF & (uint8_t)0xFF);
}

/**
 * @brief  Checks whether the specified LPUART interrupt has occurred or not.
 * @param  LPUARTx: Select the LPUART or the LPUART peripheral.
 *   This parameter can be one of the following values:
 *   LPUART
 * @param  LPUART_IT: specifies the LPUART interrupt source to check.
 *   This parameter can be one of the following values:
 *		@arg LPUART_ICR_RI      (uint32_t)0x00000001
 *		@arg LPUART_ICR_TI      (uint32_t)0x00000002
 *		@arg LPUART_ICR_FEI     (uint32_t)0x00000004
 *		@arg LPUART_ICR_TE      (uint32_t)0x00000008
 * @retval The new state of LPUART_IT (SET or RESET).
 */
FlagStatus HAL_LPUART_GetFlagStatus(LPUART_TypeDef *LPUARTx, uint16_t LPUART_Flag)
{
    FlagStatus bitstatus = RESET;

    /* Check the parameters */
    assert_param(IS_LPUART_ALL_PERIPH(LPUARTx));
    assert_param(IS_LPUART_ISR(LPUART_Flag));

    if ((LPUARTx->ISR & LPUART_Flag) != (uint16_t)RESET)
    {
        bitstatus = SET;
    }
    else
    {
        bitstatus = RESET;
    }

    return bitstatus;
}

/**
 * @brief  Clears the LPUARTx's interrupt pending bits.
 * @param  LPUARTx:  Select the LPUART or the LPUART peripheral.
 * @param  LPUART_Flag: LPUART Interrupt flag.
 *   This parameter can be one of the following values:
 *		@arg LPUART_ICR_RI      (uint32_t)0x00000001
 *		@arg LPUART_ICR_TI      (uint32_t)0x00000002
 *		@arg LPUART_ICR_FEI     (uint32_t)0x00000004
 *		@arg LPUART_ICR_TE      (uint32_t)0x00000008
 *		@arg LPUART_ICR_ALL     (uint32_t)0x0000000f
 * @retval None
 */
void HAL_LPUART_ClearITFlag(LPUART_TypeDef *LPUARTx, uint16_t LPUART_Flag)
{
    /* Check the parameters */
    assert_param(IS_LPUART_ALL_PERIPH(LPUARTx));
    assert_param(IS_LPUART_ICR(LPUART_Flag));

    /*clear it falg*/
    LPUARTx->ICR = (uint32_t)LPUART_Flag;
}

#endif
