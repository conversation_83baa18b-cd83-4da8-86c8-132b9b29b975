#ifndef _UART_H_
#define _UART_H_
#ifdef __cplusplus
extern "C"
{
#endif

#include "SKEAZN84.h"

#include <stdarg.h>

typedef struct
{
	uint32_t UART_BaudRate; /*!< This member configures the USART communication baud rate.
										The baud rate is computed using the following formula:
										- IntegerDivider = ((PCLKx) / (16 * (USART_InitStruct->USART_BaudRate)))
										- FractionalDivider = ((IntegerDivider - ((u32) IntegerDivider)) * 16) + 0.5 */

	uint16_t UART_WordLength; /*!< Specifies the number of data bits transmitted or received in a frame.
										This parameter can be a value of @ref USART_Word_Length */

	uint16_t UART_StopBits; /*!< Specifies the number of stop bits transmitted.
										This parameter can be a value of @ref USART_Stop_Bits */

	uint16_t UART_Parity; /*!< Specifies the parity mode.
										This parameter can be a value of @ref USART_Parity
										@note When parity is enabled, the computed parity is inserted
												at the MSB position of the transmitted data (9th bit when
												the word length is set to 9 data bits; 8th bit when the
												word length is set to 8 data bits). */

	uint16_t UART_Mode; /*!< Specifies wether the Receive or Transmit mode is enabled or disabled.
										This parameter can be a value of @ref USART_Mode */

	uint16_t UART_PIN; //

} UART_InitTypeDef;

#define RX_PTA2_TX_PTA3 ((uint8_t)0x01)
#define RX_PTB0_TX_PTB1 ((uint8_t)0x00)

#define UART_WordLength_8b ((uint8_t)0x00)
#define UART_WordLength_9b ((uint8_t)0x10)

#define IS_USART_WORD_LENGTH(LENGTH) (((LENGTH) == UART_WordLength_8b) || \
									  ((LENGTH) == UART_WordLength_9b))

#define UART_StopBits_1 ((uint8_t)0x00)
#define UART_StopBits_2 ((uint8_t)0x20)
#define IS_UART_STOPBITS(STOPBITS) (((STOPBITS) == UART_StopBits_1) || \
									((STOPBITS) == UART_StopBits_2))

#define UART_Parity_No ((uint8_t)0x00)
#define UART_Parity_Even ((uint8_t)0x02)
#define UART_Parity_Odd ((uint8_t)0x03)
#define IS_USART_PARITY(PARITY) (((PARITY) == USART_Parity_No) ||   \
								 ((PARITY) == USART_Parity_Even) || \
								 ((PARITY) == USART_Parity_Odd))

#define UART_Mode_Rx UART_C2_RE_MASK
#define UART_Mode_Tx UART_C2_TE_MASK
#define IS_USART_MODE(MODE) (((MODE) == USART_Mode_Rx) || \
							 ((MODE) == USART_Mode_Rx))

typedef enum
{
	UART_IT_TXE = UART_C2_TIE_MASK,	 /*!< transmit buffer empty interrupt */
	UART_IT_TC = UART_C2_TCIE_MASK,	 /*!< transmit complete interrupt */
	UART_IT_RXNE = UART_C2_RIE_MASK, /*!< receive buffer full interrupt */

	UART_IT_IDLE = UART_C2_ILIE_MASK, /*!< idle line interrupt */

	UART_IT_ORE = UART_C3_ORIE_MASK,  /*!< receive overrun interrupt */
	UART_IT_NERR = UART_C3_NEIE_MASK, /*!< noise error interrupt */
	UART_IT_ERR = UART_C3_FEIE_MASK,  /*!< framing error interrupt */
	UART_IT_PE = UART_C3_FEIE_MASK,	  /*!< parity error interrupt */
} UART_InterruptType;

typedef enum
{
	UART_Flag_PF = 0, /*!< Parity error flag */
	UART_Flag_FE,	  /*!< Framing error flag */
	UART_Flag_NF,	  /*!< Noise flag */
	UART_Flag_ORE,	  /*!< Receive overrun */
	UART_Flag_IDLE,	  /*!< Idle line flag */
	UART_Flag_RDRF,	  /*!< Receive data register full flag */
	UART_Flag_TC,	  /*!< Transmission complete flag */
	UART_Flag_TDRE,	  /*!< Transmit data register flag */

	UART_Flag_RAF,	   /*!< Receiver active flag */
	UART_Flag_LBKDE,   /*!< LIN break detection enable */
	UART_Flag_BRK13,   /*!< Break character generation length */
	UART_Flag_RWUID,   /*!< Receive wake up idle detect */
	UART_Flag_RXINV,   /*!< Receive data inversion */
	UART_Flag_Rev1,	   /*!< Reserved */
	UART_Flag_RXEDGIF, /*!< RxD pin active edge interrupt flag */
	UART_Flag_LBKDIF,  /*!< LIN break detect interrupt flag */
} UART_FlagType;

void UART_Init(UART_InitTypeDef *UART_InitStruct);

void UART_ITConfig(UART_InterruptType UART_IT, FunctionalState NewState);
FlagStatus UART_GetFlagStatus(UART_FlagType UART_FLAG);

void UART_SendData(unsigned char c);

void UART_SendStr(uint8_t *buff);

uint8_t UART_ReceiveData(void);

void UART_SendBuffer(uint8_t *buffer, uint8_t length);

#endif
