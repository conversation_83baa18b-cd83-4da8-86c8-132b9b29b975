/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_LPLPUART_H
#define __ASM31AX_LPLPUART_H

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

    /** @addtogroup
     * @{
     */
    typedef struct
    {
        LPUART_TypeDef *Instance;

        uint32_t ITEnable;

        FunctionalState LPModeState;

        uint32_t BaudRate;

        uint32_t BraudDouble; /*baud rate double enable
                            This parameter can be: ENABLE or DISABLE. */

        uint32_t Mode; /*!< Specifies wether the Receive or Transmit mode is enabled or disabled.
                                 This parameter can be a value of @ref Mode */

        uint32_t TimerEnable; /*LPUART波特率是否由LPTIMER产生
                              This parameter can be: ENABLE or DISABLE. */

        uint32_t SclkSel; /*Select the lpuart sclk*/

        uint32_t SclkDiv; /*lpuart Prescale Vlaue*/

    } LPUART_InitTypeDef;

    /**
     * @brief  LPUART Clock Init Structure definition
     */

    typedef struct
    {
        uint16_t LPUART_SetSCLK; /*Set the lpuart sclk*/

        uint16_t LPUART_PreDivSCLK; /*Set the lpuart PreDivSCLK*/

    } LPUART_ClockInitTypeDef;

    /** @defgroup LPUART_Exported_Constants
     * @{
     */

#define IS_LPUART_ALL_PERIPH(PERIPH)    (PERIPH) == LPUART)

/** @defgroup LPUART_Clock
 * @{
 */
#define LPUART_Clock_Disable ((uint16_t)0x00000000)
#define LPUART_Clock_Enable ((uint16_t)0x00000800)
#define IS_LPUART_CLOCK(CLOCK) (((CLOCK) == LPUART_Clock_Disable) || \
                                ((CLOCK) == LPUART_Clock_Enable))

#define LPUART_IT_DISABLE (0x00000000UL)
#define LPUART_IT_TIEN (0x00000001UL)
#define LPUART_IT_RIEN (0x00000002UL)
#define LPUART_IT_TIRIEN (0x00000004UL)

/** @defgroup LPUART_MODE
 * @{
 */
#define LPUART_MODE0 (uint16_t)0x00000000
#define LPUART_MODE1 (uint16_t)0x00000001
#define LPUART_MODE2 (uint16_t)0x00000002
#define LPUART_MODE3 (uint16_t)0x00000003
#define IS_LPUART_MODE(MODE) (((MODE) == LPUART_MODE0) || \
                              ((MODE) == LPUART_MODE1) || \
                              ((MODE) == LPUART_MODE2) || \
                              ((MODE) == LPUART_MODE3))

#define IS_LPUART_BAUDRATE(BAUDRATE) (((BAUDRATE) > 0) && ((BAUDRATE) < 0x0044AA21))
#define LPUART_Address_Mask ((uint16_t)0xFF00) /*!< USART address Mask */
#define IS_LPUART_ADDRESS(ADDRESS) ((ADDRESS) <= 0xFF)

/** @defgroup LPUART_SCLK Source select
 * @{
 */
#define LPUART_SCLK_MASK ((uint32_t)0x00000003 << 11)
#define LPUART_SCLK_PCLK ((uint32_t)0x00000000 << 11)  /*!< LPUART SCLK时钟源选择：PCLK */
#define LPUART_SCLK_PCLK1 ((uint32_t)0x00000001 << 11) /*!< LPUART SCLK时钟源选择：PCLK */
#define LPUART_SCLK_LXT ((uint32_t)0x00000002 << 11)   /*!< LPUART SCLK时钟源选择：LXT */
#define LPUART_SCLK_LIRC ((uint32_t)0x00000003 << 11)  /*!< LPUART SCLK时钟源选择：LIRC */
#define IS_LPUART_SCLK(CLK) ((CLK == LPUART_SCLK_PCLK) ||  \
                             (CLK == LPUART_SCLK_PCLK1) || \
                             (CLK == LPUART_SCLK_LXT) ||   \
                             (CLK == LPUART_SCLK_LIRC))

/** @defgroup LPUART_SCLK_DIV
 * @{
 */
#define LPUART_SCLK_DIV_MASK ((uint32_t)0x00000007 << 13)
#define LPUART_SCLK_DIV128 ((uint32_t)0x00000000 << 13)
#define LPUART_SCLK_DIV64 ((uint32_t)0x00000001 << 13)
#define LPUART_SCLK_DIV32 ((uint32_t)0x00000002 << 13)
#define LPUART_SCLK_DIV16 ((uint32_t)0x00000003 << 13)
#define LPUART_SCLK_DIV8 ((uint32_t)0x00000004 << 13)
#define LPUART_SCLK_DIV4 ((uint32_t)0x00000005 << 13)
#define LPUART_SCLK_DIV2 ((uint32_t)0x00000006 << 13)
#define LPUART_SCLK_DIV1 ((uint32_t)0x00000007 << 13)
#define IS_LPUART_SCLKDIV(CLKDIV) ((CLKDIV == LPUART_SCLK_DIV128) || \
                                   (CLKDIV == LPUART_SCLK_DIV64) ||  \
                                   (CLKDIV == LPUART_SCLK_DIV32) ||  \
                                   (CLKDIV == LPUART_SCLK_DIV16) ||  \
                                   (CLKDIV == LPUART_SCLK_DIV8) ||   \
                                   (CLKDIV == LPUART_SCLK_DIV4) ||   \
                                   (CLKDIV == LPUART_SCLK_DIV2) ||   \
                                   (CLKDIV == LPUART_SCLK_DIV1))

/** @defgroup LPUART_SCLK_LP_MODE
 * @{
 */
#define LPUART_SCON_LPENABLE ((uint32_t)0x00000001 << 16)
#define LPUART_SCON_LPDISABLE ((uint32_t)0x00000000 << 16)

/**
 * @}
 */
/******************  Bit definition for USART_SCON register  *******************/
#define LPUART_SCON_RIEN (uint32_t)0x00000001
#define LPUART_SCON_TIEN (uint32_t)0x00000002
#define LPUART_SCON_RB8 (uint32_t)0x00000004
#define LPUART_SCON_TB8 (uint32_t)0x00000008
#define LPUART_SCON_REN (uint32_t)0x00000010
#define LPUART_SCON_SM2 (uint32_t)0x00000020
#define LPUART_SCON_SM1 (uint32_t)0x00000040
#define LPUART_SCON_SM0 (uint32_t)0x00000080
#define LPUART_SCON_TEEN (uint32_t)0x00000100
#define LPUART_SCON_DBAUD (uint32_t)0x00000200
#define LPUART_SCON_LPMODE (uint32_t)0x00000400
#define LPUART_SCON_LPEN (uint32_t)0x00010000

#define LPUART_SCON_MODE0 ~(LPUART_SCON_SM1 | LPUART_SCON_SM0)
#define LPUART_SCON_MODE1 LPUART_SCON_SM1
#define LPUART_SCON_MODE2 LPUART_SCON_SM0
#define LPUART_SCON_MODE3 LPUART_SCON_SM1 | LPUART_SCON_SM0

/******************  Bit definition for USART_ISR register  *******************/
#define LPUART_ISR_RI (uint32_t)0x00000001
#define LPUART_ISR_TI (uint32_t)0x00000002
#define LPUART_ISR_FEI (uint32_t)0x00000004
#define LPUART_ISR_TE (uint32_t)0x00000008
#define IS_LPUART_ISR(FLAG) ((FLAG == LPUART_ISR_RI) ||  \
                             (FLAG == LPUART_ISR_TI) ||  \
                             (FLAG == LPUART_ISR_FEI) || \
                             (FLAG == LPUART_ISR_TE))

/******************  Bit definition for USART_ICR register  *******************/
#define LPUART_ICR_RI (uint32_t)0x00000001
#define LPUART_ICR_TI (uint32_t)0x00000002
#define LPUART_ICR_FEI (uint32_t)0x00000004
#define LPUART_ICR_TE (uint32_t)0x00000008
#define LPUART_ICR_ALL (uint32_t)0x0000000f
#define IS_LPUART_ICR(FLAG) ((FLAG == LPUART_ICR_RI) ||  \
                             (FLAG == LPUART_ICR_TI) ||  \
                             (FLAG == LPUART_ICR_FEI) || \
                             (FLAG == LPUART_ICR_TE) ||  \
                             (FLAG == LPUART_ICR_ALL))

#define LPUART_SELF_BRG (uint32_t)0x00010000 /*<!  UART 的波特率由 Fsclk/(BRG[7:0]+1)生成 */
#define LPUART_RXEN_EABLE (uint32_t)0x00000001
#define LPUART_TXEN_EABLE (uint32_t)0x00000002
#define LPUART_RIEN_EABLE (uint32_t)0x00000001
#define LPUART_TIEN_EABLE (uint32_t)0x00000002
#define LPUART_REEN_EABLE (uint32_t)0x00000010
#define IS_LPUART_ITENABLE(ITENABLE) ((ITENABLE == LPUART_RIEN_EABLE) || \
                                      (ITENABLE == LPUART_TIEN_EABLE) || \
                                      (ITENABLE == LPUART_REEN_EABLE))

#define LPUART_LPTIM_EABLE (uint32_t)0x0000020B
#define LPUART_LPTIMRUN_EABLE (uint32_t)0x00000001

#define LPUART_TE_ENABLE ((uint32_t)0x00000001 << 8)
#define LPUART_TE_DISABLE ((uint32_t)0x00000000 << 8)
#define IS_LPUART_TE(NewStatus) ((NewStatus == LPUART_TE_ENABLE) || \
                                 (NewStatus == LPUART_TE_DISABLE))

    /** @defgroup LPUART_Exported_Functions
     * @{
     */
    void HAL_LPUART_DeInit(LPUART_TypeDef *LPUARTx);
    void HAL_LPUART_Init(LPUART_InitTypeDef *LPUART_InitStruct);
    void HAL_LPUART_StructInit(LPUART_InitTypeDef *LPUART_InitStruct);
    void HAL_LPUART_ClockInit(LPUART_TypeDef *LPUARTx, LPUART_ClockInitTypeDef *LPUART_ClockInitStruct);
    void HAL_LPUART_ClockStructInit(LPUART_ClockInitTypeDef *LPUART_ClockInitStruct);
    void HAL_LPUART_Cmd(LPUART_TypeDef *LPUARTx, uint16_t FuncState, FunctionalState NewState);
    void HAL_LPUART_ITConfig(LPUART_TypeDef *LPUARTx, uint32_t LPUART_IT, FunctionalState NewState);
    void HAL_LPUART_LowPowerCmd(LPUART_TypeDef *LPUARTx, FunctionalState NewState);
    void HAL_LPUART_SetAddress(LPUART_TypeDef *LPUARTx, uint8_t LPUART_Address, uint8_t LPUART_Address_Enable);
    void HAL_LPUART_ModeConfig(LPUART_TypeDef *LPUARTx, uint32_t LpuartMode);
    void HAL_LPUART_ReciveCmd(LPUART_TypeDef *LPUARTx, LPUART_InitTypeDef *LPUART_InitStruct, FunctionalState NewState);
    void HAL_LPUART_SendData(LPUART_TypeDef *LPUARTx, uint8_t Data);
    uint8_t HAL_LPUART_ReceiveData(LPUART_TypeDef *LPUARTx);
    FlagStatus HAL_LPUART_GetFlagStatus(LPUART_TypeDef *LPUARTx, uint16_t LPUART_Flag);
    void HAL_LPUART_ClearITFlag(LPUART_TypeDef *LPUARTx, uint16_t LPUART_Flag);

#ifdef __cplusplus
}
#endif

#endif /* __ASM31X_LPLPUART_H */
