#include "asm31ax_hal.h"

#ifdef HAL_AWK_MODULE_ENABLED
/* Includes ------------------------------------------------------------------*/

uint8_t awk_flg;

/**
 * @brief  Initializes the AWK peripheral according to the specified
 *         parameters in the AWK_InitStruct.
 * @param  AWKx:selects the AWK peripheral
 * @param  AWK_InitStruct:pointer to a AWK_InitTypeDef structure which will
 *         be initialized.
 * @retval None
 */
void HAL_AWK_Init(AWK_InitTypeDef *AWK_InitStruct)
{
    int tmpreg = 0;
    /*Check the parameters */
    assert_param(IS_AWK_ALL_PERIPH(AWKx));
    /*read the value of CR*/
    tmpreg = AWK_InitStruct->Instance->CR;

    /*Clear the function bits to zero*/
    tmpreg &= AWK_CR_MASK;

    /*---------------------------- AWK CR Configuration ------------------------*/
    /*set the specified parameters */
    /*set XTLPRSC to the XTLPRSC*/
    /*set ClkSel to the TCLKSEL*/
    /*set ClkDiv to the DIVSEL*/
    /*
    tmpreg |= ((AWK_InitStruct->XTLPRSC << 8)|\
               (AWK_InitStruct->ClkSel<<5)|\
               (AWK_InitStruct->ClkDiv));
    */
    tmpreg |= ((AWK_InitStruct->XTLPRSC) |
               (AWK_InitStruct->ClkSel) |
               (AWK_InitStruct->ClkDiv));

    /*set the value to CR register*/
    AWK_InitStruct->Instance->CR = tmpreg;

    AWK_InitStruct->Instance->RLOAD = AWK_InitStruct->Value;
}

/**
 * @brief  Enables or disables the specified AWK peripheral function.
 * @param  AWKx:selects the AWK peripheral
 * @param  NewState: new state of the AWK peripheral.
 * This parameter can be: ENABLE or DISABLE.
 * @retval None
 */
void HAL_AWK_Cmd(AWK_TypeDef *AWKx, FunctionalState NewState)
{
    /* Check the parameters */
    assert_param(IS_AWK_ALL_PERIPH(AWKx));
    assert_param(IS_FUNCTIONAL_STATE(NewState));

    /*---------------------------- AWK CMD ENABLE------------------------*/
    if (NewState != DISABLE)
    {
        /*Enable the awk peripheral*/
        AWKx->CR |= AWK_ENABLE;
    }
    else
    {
        /*Disalbe the awk peripheral*/
        AWKx->CR &= ~AWK_ENABLE;
    }
}

/**
 * @brief  Set the clk of AWK
 * @param  AWKx:selects the AWK peripheral
 * @param  AWKClk: specifies the clock source used as AWK clock.
 *   This parameter can be one of the following values:
 *     @arg AWK_CLKLIRC
 *     @arg AWK_CLKHXT
 *     @arg AWK_CLKLXT
 * @retval None
 */
void HAL_AWK_SelClk(AWK_TypeDef *AWKx, uint8_t AWKClk)
{
    /* Check the parameters */
    assert_param(IS_AWK_ALL_PERIPH(AWKx));
    assert_param(IS_AWK_SELCLK(AWKClk));
    /*Select the awk clk*/
    AWKx->CR &= ~AWK_CLKMASK;
    AWKx->CR |= AWKClk;
}

/**
 * @brief  Setting up over loaded data registers
 * @param  AWKx: selects the AWK peripheral
 * @param  value: set the value to the register of RLDVAL
 * @retval None
 */
void HAL_AWK_SetRldval(AWK_TypeDef *AWKx, uint8_t value)
{
    /* Check the parameters */
    assert_param(IS_AWK_ALL_PERIPH(AWKx));
    /*Set the value to the register of RLDVAL*/
    AWKx->RLOAD = value;
}

/**
 * @brief  Clear the IT Flag
 * @param  AWKx: selects the AWK peripheral
 * @retval None
 */
void HAL_AWK_ClearITFlag(AWK_TypeDef *AWKx)
{
    /*Check the parameters */
    assert_param(IS_AWK_ALL_PERIPH(AWKx));
    /*Clear the TI Flag*/
    AWKx->ICR = AWK_TI_CLEAR;
}

#endif
