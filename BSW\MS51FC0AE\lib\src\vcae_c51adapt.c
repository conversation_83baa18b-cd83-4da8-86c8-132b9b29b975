/**
 * Copyright (C), 2018-2019, VCAE. Co., Ltd.
 * FILENAME:        vcae_c51adapt.c
 * DESCRIPTION:     use this file to adapt some function which can't
 *                      realize in [8051] framework.
 * VERSION: 0.2
 * FUNCTIONLIST:
 *      01. putchar
 * HISTORY:
 *      xyf 2020/12/24 10:52:18: the first version release.
 *      xyf 2021/2/25 14:06:21: change header file include.
*/
#include "derivative.h"

typedef unsigned char uint8_t;

extern void UART_SendData(uint8_t Data);

/**
  * BRIEF:      support printf function.
  * PARAM:
  *             ch: the string need to send.
  * RETURN:     the string sent in fact.
  * HISTORY:
  *             xyf 2020/11/16 16:42:42: function created.
  */
int putchar(const uint8_t ch)
{
    uint8_t ch8;

    ch8 = (uint8_t)ch;
    UART_SendData(ch8);
    return (ch);
}
