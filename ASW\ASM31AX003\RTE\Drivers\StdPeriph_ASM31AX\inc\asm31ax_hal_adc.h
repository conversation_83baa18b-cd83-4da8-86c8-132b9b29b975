/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __ASM31AX_ADC_H__
#define __ASM31AX_ADC_H__

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "asm31ax.h"
#include "asm31ax_hal.h"

/** @defgroup ADC_Exported_Constants
 * @{
 */
#define IS_ADC_ALL_PERIPH(PERIPH) (((PERIPH) == ADC))

    /**
     * @brief   ADC Init structure definition
     */
    typedef struct
    {
        uint32_t ADC_ClkSel;     /* ADC时钟分频选择 */
        uint32_t ADC_ChannelSel; /* ADC转换通道选择 */
        uint32_t ADC_SamSel;     /* 采样周期设置 */
    } ADC_InitTypeDef;

    /**
     * @brief   ADC Common Init structure definition
     */
    typedef struct
    {
        uint32_t ADC_CtMode; /*!< ADC转换模式选择：0：单次转换模式
                                                   1：连续转换模式 */

        uint32_t ADC_Trigs0Sel;  /* ADC转换自动触发选择0 */
        uint32_t ADC_Trigs1Sel;  /* ADC转换自动触发选择1 */
        uint32_t ADC_CircleMode; /* ADC转换循环模式选择 / This parameter can be a value of @ref ADC_Common_mode */
        uint32_t ADC_SetAdcCnt;  /* ADC连续转换次数配置*/

    } ADC_CommonInitTypeDef;

/** @defgroup ADC_Common_mode/ADC转换循环模式选择
 * @{
 */
#define ADC_Mode_Independent ((uint32_t)0x00000000) /* 非循环模式 */
#define ADC_Mode_Circle ((uint32_t)0x00010000)      /* 循环模式 */

#define IS_ADC_MODE(MODE) (((MODE) == ADC_Mode_Independent) || \
                           ((MODE) == ADC_Mode_Circle))

/** @defgroup ADC_Common_mode/ADC转换模式选择
 * @{
 */
#define ADC_Ct_Single ((uint32_t)0x00000000)  /* 单次转换模式 */
#define ADC_Ct_Contine ((uint32_t)0x00000400) /* 连续转换模式 */
#define IS_ADC_CT(MODE) (((MODE) == ADC_Ct_Single) || \
                         ((MODE) == ADC_Ct_Contine))

/**
 * @}CR0_MASK
 */
#define ADC_CLKSEL_MASK ((uint32_t)0x00000070)
#define ADC_ChannelEL_MASK ((uint32_t)0x00000700)
#define ADC_SamCycle_MASK ((uint32_t)0x00000080)
#define ADC_CR0_MASK ((uint32_t)0x000007F0)

/**
 * @}CR1_ACC
 */
#define ADC_ACC_EN ((uint32_t)0x00000800)          /* 使能ADC转换结果自动累加功能 */
#define ADC_ACC_DISABLE ((uint32_t)0x00000000)     /* 禁止ADC转换结果自动累加功能 */
#define ADC_ACCResult_Clear ((uint32_t)0x00008000) /* ADC 转换结果累加寄存器（ADC_result_acc）清零 */

/**
 * @}CR1_MASK
 */
#define ADC_CT_MASK ((uint32_t)0x00000400)
#define ADC_TRIGS1_MASK ((uint32_t)0x000003E0)
#define ADC_TRIGS0_MASK ((uint32_t)0x0000001F)
#define ADC_CR1_MASK ((uint32_t)0x000007FF)

/**
 * @}CR2_MASK
 */
#define ADC_CIRCLEMODE_MASK ((uint32_t)0x00010000)
#define ADC_ADCCNT_MASK ((uint32_t)0x0000FF00)
#define ADC_CR2_MASK ((uint32_t)0x0001FF00)

/** @defgroup ADC_Prescaler/ADC时钟分频选择
 * @{
 */
#define ADC_Prescaler_Div1 ((uint32_t)0x00000000)   /* 不分频，PCLK时钟 */
#define ADC_Prescaler_Div2 ((uint32_t)0x00000010)   /* PCLK 时钟 2 分频 */
#define ADC_Prescaler_Div4 ((uint32_t)0x00000020)   /* PCLK 时钟 4 分频 */
#define ADC_Prescaler_Div8 ((uint32_t)0x00000030)   /* PCLK 时钟 8 分频 */
#define ADC_Prescaler_Div16 ((uint32_t)0x00000040)  /* PCLK 时钟 16 分频 */
#define ADC_Prescaler_Div32 ((uint32_t)0x00000050)  /* PCLK 时钟 32 分频 */
#define ADC_Prescaler_Div64 ((uint32_t)0x00000060)  /* PCLK 时钟 64 分频 */
#define ADC_Prescaler_Div128 ((uint32_t)0x00000070) /* PCLK 时钟 128 分频 */

#define IS_ADC_PRESCALER(PRESCALER) (((PRESCALER) == ADC_Prescaler_Div1) ||  \
                                     ((PRESCALER) == ADC_Prescaler_Div2) ||  \
                                     ((PRESCALER) == ADC_Prescaler_Div4) ||  \
                                     ((PRESCALER) == ADC_Prescaler_Div8) ||  \
                                     ((PRESCALER) == ADC_Prescaler_Div16) || \
                                     ((PRESCALER) == ADC_Prescaler_Div32) || \
                                     ((PRESCALER) == ADC_Prescaler_Div64) || \
                                     ((PRESCALER) == ADC_Prescaler_Div128))

/** @defgroup ADC_trigger1_edge_for_regular_channels_conversion
 * @{
 */
#define ADC_Trigs1_DISABLE ((uint32_t)0x00000000)   /* 禁用自动触发ADC转换 */
#define ADC_Trigs1_TIMER10 ((uint32_t)0x00000020)   /* Timer10中断，自动触发ADC转换 */
#define ADC_Trigs1_TIMER11 ((uint32_t)0x00000040)   /* Timer11中断，自动触发ADC转换 */
#define ADC_Trigs1_TIM1 ((uint32_t)0x00000060)      /* TIM1中断，自动触发ADC转换 */
#define ADC_Trigs1_LPTIMER ((uint32_t)0x00000080)   /* LPTimer中断，自动触发ADC转换 */
#define ADC_Trigs1_TIM1_TRGO ((uint32_t)0x000000A0) /* TIM1 TRGO，自动触发ADC转换 */
#define ADC_Trigs1_TIM2_GRGO ((uint32_t)0x000000C0) /* TIM2TRGO，自动触发ADC转换 */
#define ADC_Trigs1_TIM2 ((uint32_t)0x000000E0)      /* TIM2中断，自动触发ADC转换 */
#define ADC_Trigs1_UART0 ((uint32_t)0x00000100)     /* UART0中断，自动触发ADC转换 */
#define ADC_Trigs1_UART1 ((uint32_t)0x00000120)     /* UART1中断，自动触发ADC转换 */
#define ADC_Trigs1_LPUART ((uint32_t)0x00000140)    /* LPUART中断，自动触发ADC转换 */
#define ADC_Trigs1_VC0 ((uint32_t)0x00000160)       /* VC0中断，自动触发ADC转换 */
#define ADC_Trigs1_NC2 ((uint32_t)0x00000180)       /* NC */
#define ADC_Trigs1_RTC ((uint32_t)0x000001A0)       /* RTC中断，自动触发ADC转换 */
#define ADC_Trigs1_PCA ((uint32_t)0x000001C0)       /* PCA中断，自动触发ADC转换 */
#define ADC_Trigs1_SPI ((uint32_t)0x000001E0)       /* SPI中断，自动触发ADC转换 */
#define ADC_Trigs1_PA1 ((uint32_t)0x00000200)       /* PA1中断，自动触发ADC转换 */
#define ADC_Trigs1_PA2 ((uint32_t)0x00000220)       /* PA2中断，自动触发ADC转换 */
#define ADC_Trigs1_PA3 ((uint32_t)0x00000240)       /* PA3中断，自动触发ADC转换 */
#define ADC_Trigs1_PB4 ((uint32_t)0x00000260)       /* PB4中断，自动触发ADC转换 */
#define ADC_Trigs1_PB5 ((uint32_t)0x00000280)       /* PB5中断，自动触发ADC转换 */
#define ADC_Trigs1_PC3 ((uint32_t)0x000002A0)       /* PC3中断，自动触发ADC转换 */
#define ADC_Trigs1_PC4 ((uint32_t)0x000002C0)       /* PC4中断，自动触发ADC转换 */
#define ADC_Trigs1_PC5 ((uint32_t)0x000002E0)       /* PC5中断，自动触发ADC转换 */
#define ADC_Trigs1_PC6 ((uint32_t)0x00000300)       /* PC6中断，自动触发ADC转换 */
#define ADC_Trigs1_PC7 ((uint32_t)0x00000320)       /* PC7中断，自动触发ADC转换 */
#define ADC_Trigs1_PD1 ((uint32_t)0x00000340)       /* PD1中断，自动触发ADC转换 */
#define ADC_Trigs1_PD2 ((uint32_t)0x00000360)       /* PD2中断，自动触发ADC转换 */
#define ADC_Trigs1_PD3 ((uint32_t)0x00000380)       /* PD3中断，自动触发ADC转换 */
#define ADC_Trigs1_PD4 ((uint32_t)0x000003A0)       /* PD4中断，自动触发ADC转换 */
#define ADC_Trigs1_PD5 ((uint32_t)0x000003C0)       /* PD5中断，自动触发ADC转换 */
#define ADC_Trigs1_PD6 ((uint32_t)0x000003E0)       /* PD6中断，自动触发ADC转换 */

#define IS_ADC_TRIG1_EDGE(EDGE) (((EDGE) == ADC_Trigs1_DISABLE) || \
                                 ((EDGE) == ADC_Trigs1_TIMER10) || \
                                 ((EDGE) == ADC_Trigs1_TIMER11) || \
                                 ((EDGE) == ADC_Trigs1_NC0_) ||    \
                                 ((EDGE) == ADC_Trigs1_LPTIMER) || \
                                 ((EDGE) == ADC_Trigs1_TIM1) ||    \
                                 ((EDGE) == ADC_Trigs1_TIM2) ||    \
                                 ((EDGE) == ADC_Trigs1_NC1) ||     \
                                 ((EDGE) == ADC_Trigs1_UART0) ||   \
                                 ((EDGE) == ADC_Trigs1_UART1) ||   \
                                 ((EDGE) == ADC_Trigs1_LPUART) ||  \
                                 ((EDGE) == ADC_Trigs1_VC0) ||     \
                                 ((EDGE) == ADC_Trigs1_NC2) ||     \
                                 ((EDGE) == ADC_Trigs1_RTC) ||     \
                                 ((EDGE) == ADC_Trigs1_PCA) ||     \
                                 ((EDGE) == ADC_Trigs1_SPI) ||     \
                                 ((EDGE) == ADC_Trigs1_PA1) ||     \
                                 ((EDGE) == ADC_Trigs1_PA2) ||     \
                                 ((EDGE) == ADC_Trigs1_PA3) ||     \
                                 ((EDGE) == ADC_Trigs1_PB4) ||     \
                                 ((EDGE) == ADC_Trigs1_PB5) ||     \
                                 ((EDGE) == ADC_Trigs1_PC3) ||     \
                                 ((EDGE) == ADC_Trigs1_PC4) ||     \
                                 ((EDGE) == ADC_Trigs1_PC5) ||     \
                                 ((EDGE) == ADC_Trigs1_PC6) ||     \
                                 ((EDGE) == ADC_Trigs1_PC7) ||     \
                                 ((EDGE) == ADC_Trigs1_PD1) ||     \
                                 ((EDGE) == ADC_Trigs1_PD2) ||     \
                                 ((EDGE) == ADC_Trigs1_PD3) ||     \
                                 ((EDGE) == ADC_Trigs1_PD4) ||     \
                                 ((EDGE) == ADC_Trigs1_PD5) ||     \
                                 ((EDGE) == ADC_Trigs1_PD6))

/** @defgroup ADC_trigger0_edge_for_regular_channels_conversion
 * @{
 */
#define ADC_Trigs0_DISABLE ((uint32_t)0x00000000)   /* 禁用自动触发ADC转换 */
#define ADC_Trigs0_TIMER10 ((uint32_t)0x00000001)   /* Timer10中断，自动触发ADC转换 */
#define ADC_Trigs0_TIMER11 ((uint32_t)0x00000002)   /* Timer11中断，自动触发ADC转换 */
#define ADC_Trigs0_TIM1 ((uint32_t)0x00000003)      /* TIM1中断，自动触发ADC转换 */
#define ADC_Trigs0_LPTIMER ((uint32_t)0x00000004)   /* LPTimer中断，自动触发ADC转换 */
#define ADC_Trigs0_TIM1_TRGO ((uint32_t)0x00000005) /* TIM1 TRGO，自动触发ADC转换 */
#define ADC_Trigs0_TIM2_TRGO ((uint32_t)0x00000006) /* TIM2TRGO，自动触发ADC转换 */
#define ADC_Trigs0_TIM2 ((uint32_t)0x00000007)      /* TIM2中断，自动触发ADC转换 */
#define ADC_Trigs0_UART0 ((uint32_t)0x00000008)     /* UART0中断，自动触发ADC转换 */
#define ADC_Trigs0_UART1 ((uint32_t)0x00000009)     /* UART1中断，自动触发ADC转换 */
#define ADC_Trigs0_LPUART ((uint32_t)0x0000000A)    /* LPUART中断，自动触发ADC转换 */
#define ADC_Trigs0_VC0 ((uint32_t)0x0000000B)       /* VC0中断，自动触发ADC转换 */
#define ADC_Trigs0_NC2 ((uint32_t)0x0000000C)       /* NC */
#define ADC_Trigs0_RTC ((uint32_t)0x0000000D)       /* RTC中断，自动触发ADC转换 */
#define ADC_Trigs0_PCA ((uint32_t)0x0000000E)       /* PCA中断，自动触发ADC转换 */
#define ADC_Trigs0_SPI ((uint32_t)0x0000000F)       /* SPI中断，自动触发ADC转换 */
#define ADC_Trigs0_PA1 ((uint32_t)0x00000010)       /* PA1中断，自动触发ADC转换 */
#define ADC_Trigs0_PA2 ((uint32_t)0x00000011)       /* PA2中断，自动触发ADC转换 */
#define ADC_Trigs0_PA3 ((uint32_t)0x00000012)       /* PA3中断，自动触发ADC转换 */
#define ADC_Trigs0_PB4 ((uint32_t)0x00000013)       /* PB4中断，自动触发ADC转换 */
#define ADC_Trigs0_PB5 ((uint32_t)0x00000014)       /* PB5中断，自动触发ADC转换 */
#define ADC_Trigs0_PC3 ((uint32_t)0x00000015)       /* PC3中断，自动触发ADC转换 */
#define ADC_Trigs0_PC4 ((uint32_t)0x00000016)       /* PC4中断，自动触发ADC转换 */
#define ADC_Trigs0_PC5 ((uint32_t)0x00000017)       /* PC5中断，自动触发ADC转换 */
#define ADC_Trigs0_PC6 ((uint32_t)0x00000018)       /* PC6中断，自动触发ADC转换 */
#define ADC_Trigs0_PC7 ((uint32_t)0x00000019)       /* PC7中断，自动触发ADC转换 */
#define ADC_Trigs0_PD1 ((uint32_t)0x0000001A)       /* PD1中断，自动触发ADC转换 */
#define ADC_Trigs0_PD2 ((uint32_t)0x0000001B)       /* PD2中断，自动触发ADC转换 */
#define ADC_Trigs0_PD3 ((uint32_t)0x0000001C)       /* PD3中断，自动触发ADC转换 */
#define ADC_Trigs0_PD4 ((uint32_t)0x0000001D)       /* PD4中断，自动触发ADC转换 */
#define ADC_Trigs0_PD5 ((uint32_t)0x0000001E)       /* PD5中断，自动触发ADC转换 */
#define ADC_Trigs0_PD6 ((uint32_t)0x0000001F)       /* PD6中断，自动触发ADC转换 */

#define IS_ADC_TRIG0_EDGE(EDGE) (((EDGE) == ADC_Trigs0_DISABLE) || \
                                 ((EDGE) == ADC_Trigs0_TIMER10) || \
                                 ((EDGE) == ADC_Trigs0_TIMER11) || \
                                 ((EDGE) == ADC_Trigs0_NC0_) ||    \
                                 ((EDGE) == ADC_Trigs0_LPTIMER) || \
                                 ((EDGE) == ADC_Trigs0_TIM1) ||    \
                                 ((EDGE) == ADC_Trigs0_TIM2) ||    \
                                 ((EDGE) == ADC_Trigs0_NC1) ||     \
                                 ((EDGE) == ADC_Trigs0_UART0) ||   \
                                 ((EDGE) == ADC_Trigs0_UART1) ||   \
                                 ((EDGE) == ADC_Trigs0_LPUART) ||  \
                                 ((EDGE) == ADC_Trigs0_VC0) ||     \
                                 ((EDGE) == ADC_Trigs0_NC2) ||     \
                                 ((EDGE) == ADC_Trigs0_RTC) ||     \
                                 ((EDGE) == ADC_Trigs0_PCA) ||     \
                                 ((EDGE) == ADC_Trigs0_SPI) ||     \
                                 ((EDGE) == ADC_Trigs0_PA1) ||     \
                                 ((EDGE) == ADC_Trigs0_PA2) ||     \
                                 ((EDGE) == ADC_Trigs0_PA3) ||     \
                                 ((EDGE) == ADC_Trigs0_PB4) ||     \
                                 ((EDGE) == ADC_Trigs0_PB5) ||     \
                                 ((EDGE) == ADC_Trigs0_PC3) ||     \
                                 ((EDGE) == ADC_Trigs0_PC4) ||     \
                                 ((EDGE) == ADC_Trigs0_PC5) ||     \
                                 ((EDGE) == ADC_Trigs0_PC6) ||     \
                                 ((EDGE) == ADC_Trigs0_PC7) ||     \
                                 ((EDGE) == ADC_Trigs0_PD1) ||     \
                                 ((EDGE) == ADC_Trigs0_PD2) ||     \
                                 ((EDGE) == ADC_Trigs0_PD3) ||     \
                                 ((EDGE) == ADC_Trigs0_PD4) ||     \
                                 ((EDGE) == ADC_Trigs0_PD5) ||     \
                                 ((EDGE) == ADC_Trigs0_PD6))

/** @defgroup ADC_channels
 * @{
 */
#define ADC_Channel0 ((uint32_t)0x0000)
#define ADC_Channel1 ((uint32_t)0x0100)
#define ADC_Channel2 ((uint32_t)0x0200)
#define ADC_Channel3 ((uint32_t)0x0300)
#define ADC_Channel4 ((uint32_t)0x0400)
#define ADC_Channel5 ((uint32_t)0x0500)
#define ADC_Channel6 ((uint32_t)0x0600)
#define ADC_Channel7 ((uint32_t)0x0700)

#define IS_ADC_CHANNEL(CHANNEL) (((CHANNEL) == ADC_Channel0) || \
                                 ((CHANNEL) == ADC_Channel1) || \
                                 ((CHANNEL) == ADC_Channel2) || \
                                 ((CHANNEL) == ADC_Channel3) || \
                                 ((CHANNEL) == ADC_Channel4) || \
                                 ((CHANNEL) == ADC_Channel5) || \
                                 ((CHANNEL) == ADC_Channel6) || \
                                 ((CHANNEL) == ADC_Channel7))

/** @defgroup ADC_channels
 * @{
 */
#define ADC_Channel0_ENABLE ((uint32_t)0x0001)
#define ADC_Channel1_ENABLE ((uint32_t)0x0002)
#define ADC_Channel2_ENABLE ((uint32_t)0x0004)
#define ADC_Channel3_ENABLE ((uint32_t)0x0008)
#define ADC_Channel4_ENABLE ((uint32_t)0x0010)
#define ADC_Channel5_ENABLE ((uint32_t)0x0020)
#define ADC_Channel6_ENABLE ((uint32_t)0x0040)
#define ADC_Channel7_ENABLE ((uint32_t)0x0080)

#define IS_ADC_CHANNEL_CMD(NewStatus) (((NewStatus) == ADC_Channel0_ENABLE) || \
                                       ((NewStatus) == ADC_Channel1_ENABLE) || \
                                       ((NewStatus) == ADC_Channel2_ENABLE) || \
                                       ((NewStatus) == ADC_Channel3_ENABLE) || \
                                       ((NewStatus) == ADC_Channel4_ENABLE) || \
                                       ((NewStatus) == ADC_Channel5_ENABLE) || \
                                       ((NewStatus) == ADC_Channel6_ENABLE) || \
                                       ((NewStatus) == ADC_Channel7_ENABLE))

/**
 * @}ADC_IMCSMASK
 */
#define ADC_IMCSCHANNEL0MASK_ENABLE ((uint32_t)0x0001)
#define ADC_IMCSCHANNEL1MASK_ENABLE ((uint32_t)0x0002)
#define ADC_IMCSCHANNEL2MASK_ENABLE ((uint32_t)0x0004)
#define ADC_IMCSCHANNEL3MASK_ENABLE ((uint32_t)0x0008)
#define ADC_IMCSCHANNEL4MASK_ENABLE ((uint32_t)0x0010)
#define ADC_IMCSCHANNEL5MASK_ENABLE ((uint32_t)0x0020)
#define ADC_IMCSCHANNEL6MASK_ENABLE ((uint32_t)0x0040)
#define ADC_IMCSCHANNEL7MASK_ENABLE ((uint32_t)0x0080)
#define ADC_LLTIMCSMASK_ENABLE ((uint32_t)0x0100)
#define ADC_HHTIMCSMASK_ENABLE ((uint32_t)0x0200)
#define ADC_REGIMCSMASK_ENABLE ((uint32_t)0x0400)
#define ADC_CONTIMCSMASK_ENABLE ((uint32_t)0x0800)

#define IS_ADC_TIMASK(MASK) (((MASK) == ADC_IMCSCHANNEL0MASK_ENABLE) || \
                             ((MASK) == ADC_IMCSCHANNEL1MASK_ENABLE) || \
                             ((MASK) == ADC_IMCSCHANNEL2MASK_ENABLE) || \
                             ((MASK) == ADC_IMCSCHANNEL3MASK_ENABLE) || \
                             ((MASK) == ADC_IMCSCHANNEL4MASK_ENABLE) || \
                             ((MASK) == ADC_IMCSCHANNEL5MASK_ENABLE) || \
                             ((MASK) == ADC_IMCSCHANNEL6MASK_ENABLE) || \
                             ((MASK) == ADC_IMCSCHANNEL7MASK_ENABLE) || \
                             ((MASK) == ADC_LLTIMCSMASK_ENABLE) ||      \
                             ((MASK) == ADC_HHTIMCSMASK_ENABLE) ||      \
                             ((MASK) == ADC_REGIMCSMASK_ENABLE) ||      \
                             ((MASK) == ADC_CONTIMCSMASK_ENABLE))

/** @defgroup ADC_sampling_times
 * @{
 */
#define IS_ADC_SAMPLE_TIMES(TIMES) (((TIMES) >= 0x00) || \
                                    ((TIMES) <= 0xff))

#define IS_ADC_COMPARE_VALUES(VALUE) (((VALUE) >= 0x0000) || \
                                      ((VALUE) <= 0x0fff))

/**
 * @ADC 采样周期选择
 * @}
 */
#define ADC_SAMPLE_4CYCLE ((uint32_t)0x00000000)  /* 4 个采样周期 */
#define ADC_SAMPLE_16CYCLE ((uint32_t)0x00000800) /* 8 个采样周期 */
#define IS_ADC_SAMPLE_CYCLE(CYCLE) ((CYCLE == ADC_SAMPLE_4CYCLE) || \
                                    (CYCLE == ADC_SAMPLE_16CYCLE))

/** @defgroup ADC_interrupts_definition
 * @{
 */
#define ADC_CR0_ADON ((uint32_t)0x00000001)     /* 使能ADC */
#define ADC_CR0_ADOFF ((uint32_t)0x00000000)    /* 禁止ADC */
#define ADC_CR0_SWSTART ((uint32_t)0x00000002)  /* 启动ADC转换 */
#define ADC_CR0_STATERST ((uint32_t)0x00008000) /* 停止ADC转换 */

#define ADC_CR1_RACCEN ((uint32_t)0x00000800)
#define ADC_CR1_LTCMPEN ((uint32_t)0x00001000)
#define ADC_CR1_HTCMPEN ((uint32_t)0x00002000)
#define ADC_CR1_REGCMPEN ((uint32_t)0x00004000)
#define ADC_CR1_RACCCLREN ((uint32_t)0x00008000)
#define IS_ADC_CR1_CMD(NewStatus) ((NewStatus == ADC_CR1_RACCEN) ||   \
                                   (NewStatus == ADC_CR1_LTCMPEN) ||  \
                                   (NewStatus == ADC_CR1_HTCMPEN) ||  \
                                   (NewStatus == ADC_CR1_REGCMPEN) || \
                                   (NewStatus == ADC_CR1_RACCCLREN))

/**
 * @}ADC_IMCSMASK
 */
#define ADC_TICHANNEL0_FLAG ((uint32_t)0x0001) /* ADC 通道 0 中断掩码配置 */
#define ADC_TICHANNEL1_FLAG ((uint32_t)0x0002) /* ADC 通道 1 中断掩码配置 */
#define ADC_TICHANNEL2_FLAG ((uint32_t)0x0004) /* ADC 通道 2 中断掩码配置 */
#define ADC_TICHANNEL3_FLAG ((uint32_t)0x0008) /* ADC 通道 3 中断掩码配置 */
#define ADC_TICHANNEL4_FLAG ((uint32_t)0x0010) /* ADC 通道 4 中断掩码配置 */
#define ADC_TICHANNEL5_FLAG ((uint32_t)0x0020) /* ADC 通道 5 中断掩码配置 */
#define ADC_TICHANNEL6_FLAG ((uint32_t)0x0040) /* ADC 通道 6 中断掩码配置 */
#define ADC_TICHANNEL7_FLAG ((uint32_t)0x0080) /* ADC 通道 7 中断掩码配置 */
#define ADC_TILLT_FLAG ((uint32_t)0x0100)      /* ADC转换结果比较下阈值中断掩码配置 */
#define ADC_TIHHT_FLAG ((uint32_t)0x0200)      /* ADC转换结果比较上阈值中断掩码配置 */
#define ADC_TIREG_FLAG ((uint32_t)0x0400)      /* ADC转换结果比较区间中断掩码配置 */
#define ADC_TICONT_FLAG ((uint32_t)0x0800)     /* 连续转换完成中断掩码配置 */

#define IS_ADC_FLAG(FLAG) (((FLAG) == ADC_TICHANNEL0_FLAG) || \
                           ((FLAG) == ADC_TICHANNEL1_FLAG) || \
                           ((FLAG) == ADC_TICHANNEL2_FLAG) || \
                           ((FLAG) == ADC_TICHANNEL3_FLAG) || \
                           ((FLAG) == ADC_TICHANNEL4_FLAG) || \
                           ((FLAG) == ADC_TICHANNEL5_FLAG) || \
                           ((FLAG) == ADC_TICHANNEL6_FLAG) || \
                           ((FLAG) == ADC_TICHANNEL7_FLAG) || \
                           ((FLAG) == ADC_TILLT_FLAG) ||      \
                           ((FLAG) == ADC_TIHHT_FLAG) ||      \
                           ((FLAG) == ADC_TIREG_FLAG) ||      \
                           ((FLAG) == ADC_TICONT_FLAG))

    /* Exported functions --------------------------------------------------------*/
    /* Function used to set the ADC configuration to the default reset state *****/
    void HAL_ADC_DeInit(void);
    void HAL_ADC_Init(ADC_TypeDef *ADCx, ADC_InitTypeDef *ADC_InitStruct);
    void HAL_ADC_StructInit(ADC_InitTypeDef *ADC_InitStruct);
    void HAL_ADC_CommonInit(ADC_TypeDef *ADCx, ADC_CommonInitTypeDef *ADC_CommonInitStruct);
    void HAL_ADC_CommonStructInit(ADC_CommonInitTypeDef *ADC_CommonInitStruct);
    void HAL_ADC_Cmd(ADC_TypeDef *ADCx, FunctionalState NewState);
    void HAL_ADC_RegularChannelConfig(ADC_TypeDef *ADCx, uint32_t ADC_Channel, uint32_t ADC_CircleMode, uint32_t ADC_SampleTime);
    void HAL_ADC_SoftwareStartConv(ADC_TypeDef *ADCx);
    FlagStatus HAL_ADC_GetSoftwareStartConvStatus(ADC_TypeDef *ADCx);
    void HAL_ADC_ContinuousModeCmd(ADC_TypeDef *ADCx, FunctionalState NewState);
    void HAL_ADC_ContinuousConverChannelCmd(ADC_TypeDef *ADCx, uint32_t Channel_Enable, FunctionalState NewState);
    void HAL_ADC_ContinuousConverRstCmd(ADC_TypeDef *ADCx, uint32_t NewState);
    void HAL_ADC_DisContinuousModeCmd(ADC_TypeDef *ADCx, FunctionalState NewState);
    uint32_t HAL_ADC_GetConversionValue(ADC_TypeDef *ADCx);
    uint32_t HAL_ADC_GetChannelConversionValue(ADC_TypeDef *ADCx, uint32_t Channel);
    uint32_t HAL_ADC_SetCompareHtValue(ADC_TypeDef *ADCx, uint32_t HtValue);
    uint32_t HAL_ADC_SetCompareLtValue(ADC_TypeDef *ADCx, uint32_t LtValue);
    uint32_t HAL_ADC_CompareControl(ADC_TypeDef *ADCx, uint32_t ADC_ControlCmd, FunctionalState NewState);
    void HAL_ADC_ExternalTrigInjectedConvConfig(ADC_TypeDef *ADCx, uint32_t ADC_ExternalTrigInjecConv);
    void HAL_ADC_ITCmd(ADC_TypeDef *ADCx, uint32_t ADC_FLAG, FunctionalState NewState);
    FlagStatus HAL_ADC_GetRISFlagStatus(ADC_TypeDef *ADCx, uint32_t ADC_FLAG);
    FlagStatus HAL_ADC_GetMISFlagStatus(ADC_TypeDef *ADCx, uint32_t ADC_FLAG);
    void HAL_ADC_ClearFlag(ADC_TypeDef *ADCx, uint32_t ADC_FLAG);
    void HAL_ADC_AccCmd(ADC_TypeDef *ADCx, FunctionalState NewState);
    void HAL_ADC_AccResultClear(ADC_TypeDef *ADCx, FunctionalState NewState);
    uint32_t HAL_ADC_GetAccResult(ADC_TypeDef *ADCx);

#ifdef __cplusplus
}
#endif

#endif /*__ASM31X_ADC_H__ */
