/**********************************************************************************************
 * External objects
 **********************************************************************************************/
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include "drv.h"
#include "config.h"
#include "signal.h"

/**********************************************************************************************
 * Global variables
 **********************************************************************************************/
// extern uint32_t SystemCoreClock;

/**********************************************************************************************
 * Constants and macros
 **********************************************************************************************/
#define UART_RCV_BUFFER_SIZE 30

#define FLASH_SECTOR_DATA 2                                     // save user data by 2 sector.
#define FLASH_ADDRESS_USER (FLASH_BASE + FLASH_SECTOR_SIZE * 4) // app  16k   data 4k + 4k
#define FLASH_DATA_SIZE (FLASH_SECTOR_SIZE * FLASH_SECTOR_DATA) // user flash size.

/**********************************************************************************************
 * Local types
 **********************************************************************************************/

/**********************************************************************************************
 * Local function prototypes
 *********************************************************************************************/
void uart_rcv_timeout_tick(void);

/**********************************************************************************************
 * Local variables
 **********************************************************************************************/
static uint8_t uart_rcv_buffer[UART_RCV_BUFFER_SIZE];
static uint8_t uart_rcv_length;
static uint8_t uart_rcv_timeout;

static ADC_HandleTypeDef hadc;
static TIM_HandleTypeDef TimHandle;
static TIM_OC_InitTypeDef TimersConfig;
UART_HandleTypeDef UartHandle;

static void gpio_init(void)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};

  __HAL_RCC_GPIOA_CLK_ENABLE(); /* GPIOA clock enable */
  __HAL_RCC_GPIOB_CLK_ENABLE(); /* GPIOB clock enable */
  __HAL_RCC_GPIOC_CLK_ENABLE(); /* GPIOC clock enable */
  __HAL_RCC_TIM1_CLK_ENABLE();  /* TIM1 clock enable */
  __HAL_RCC_USART1_CLK_ENABLE();

  // initialize GPIOA pin1 an output
  GPIO_InitStruct.Pin = GPIO_PIN_1 | GPIO_PIN_5;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;   /* Push-pull output */
  GPIO_InitStruct.Pull = GPIO_PULLUP;           /* Enable pull-up */
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH; /* GPIO speed */
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);       /* GPIO initialization */

  // initialize GPIOA pin5 and pin6 as input
  GPIO_InitStruct.Pin = GPIO_PIN_6;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT; /* Push-pull output */
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct); /* GPIO initialization */

  // initialize GPIOA pin3, pin4, pin7 as analog input
  GPIO_InitStruct.Pin = GPIO_PIN_3 | GPIO_PIN_4 | GPIO_PIN_7;
  GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  // initialize GPIOB pin0, pin1, as analog input
  GPIO_InitStruct.Pin = GPIO_PIN_0 | GPIO_PIN_1;
  GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

  // initialize GPIOB pin2, pin3, pin7, as digital input
  GPIO_InitStruct.Pin = GPIO_PIN_2 | GPIO_PIN_3 | GPIO_PIN_7;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

  // initialize PPIOC pin0 as input
  // GPIO_InitStruct.Pin = GPIO_PIN_0;
  // GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  // GPIO_InitStruct.Pull = GPIO_PULLUP;
  // HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

  /*PB04：TX, PB05：RX*/
  GPIO_InitStruct.Pin = GPIO_PIN_4;
  GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
  GPIO_InitStruct.Pull = GPIO_PULLUP;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
  GPIO_InitStruct.Alternate = GPIO_AF1_USART1;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

  GPIO_InitStruct.Pin = GPIO_PIN_5;
  GPIO_InitStruct.Alternate = GPIO_AF1_USART1;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

  // initialize GPIOA pin0 as pwm output
  GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
  GPIO_InitStruct.Pull = GPIO_PULLUP;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
  GPIO_InitStruct.Pin = GPIO_PIN_0;
  GPIO_InitStruct.Alternate = GPIO_AF2_TIM1;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
}

void adc_init(void)
{
  __HAL_RCC_ADC_CLK_ENABLE(); /* Enable ADC clock */

  hadc.Instance = ADC1;

  hadc.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV64;           /* Set ADC clock*/
  hadc.Init.Resolution = ADC_RESOLUTION_12B;                      /* 12-bit resolution for converted data */
  hadc.Init.DataAlign = ADC_DATAALIGN_RIGHT;                      /* Right-alignment for converted data */
  hadc.Init.ScanConvMode = ADC_SCAN_DIRECTION_FORWARD;            /* Scan sequence direction: forward */
  hadc.Init.EOCSelection = ADC_EOC_SINGLE_CONV;                   /* Single Conversion */
  hadc.Init.LowPowerAutoWait = DISABLE;                           /* Auto-delayed conversion feature disabled */
  hadc.Init.ContinuousConvMode = DISABLE;                         /* Continuous mode disabled to have only 1 conversion at each conversion trig */
  hadc.Init.DiscontinuousConvMode = DISABLE;                      /* Disable discontinuous mode */
  hadc.Init.ExternalTrigConv = ADC_SOFTWARE_START;                /* Software start to trig the 1st conversion manually, without external event */
  hadc.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE; /* Parameter discarded because software trigger chosen */
  hadc.Init.Overrun = ADC_OVR_DATA_OVERWRITTEN;                   /* DR register is overwritten with the last conversion result in case of overrun */
  hadc.Init.SamplingTimeCommon = ADC_SAMPLETIME_239CYCLES_5;      /* The channel sampling time is 239.5 ADC clock cycles */
  if (HAL_ADC_Init(&hadc) != HAL_OK)                              /* ADC initialization */
  {
    // APP_ErrorHandler();
  }

  if (HAL_ADCEx_Calibration_Start(&hadc) != HAL_OK) /* ADC Calibration */
  {
    // APP_ErrorHandler();
  }
}

static uint16_t ADC_Read_Channel(uint32_t Channel)
{
  uint16_t adcvalue = 0;
  ADC_ChannelConfTypeDef sConfig;

  /* Configure channel need ADC is disable */
  if (READ_BIT(hadc.Instance->CR, ADC_CR_ADEN) == ADC_CR_ADEN)
  {
    __HAL_ADC_DISABLE(&hadc);
  }

  /* Config selected channels */
  sConfig.Rank = ADC_RANK_CHANNEL_NUMBER;
  sConfig.Channel = Channel;
  if (HAL_ADC_ConfigChannel(&hadc, &sConfig) != HAL_OK) /* Configure ADC Channel */
  {
    // APP_ErrorHandler();
  }

  /* ADC Start */
  HAL_ADC_Start(&hadc);

  /* Polling for ADC Conversion */
  HAL_ADC_PollForConversion(&hadc, 1000000);

  /* Get ADC Value */
  adcvalue = HAL_ADC_GetValue(&hadc);

  /* Disable ADC to clear channel configuration */
  __HAL_ADC_DISABLE(&hadc);

  /* Clear the selected channels */
  sConfig.Rank = ADC_RANK_NONE;
  sConfig.Channel = Channel;
  if (HAL_ADC_ConfigChannel(&hadc, &sConfig) != HAL_OK) /* Configure ADC Channel */
  {
    // APP_ErrorHandler();
  }

  return (uint32_t)adcvalue;
}

static void board_pwm_init(void)
{

  /* TIM1 */
  TimHandle.Instance = TIM1;

  /* Period = 2000 - 1  */
  TimHandle.Init.Period = 200 - 1;

  /* Prescaler = 1200 - 1 */
  TimHandle.Init.Prescaler = 2 - 1;

  /* ClockDivision = 0  */
  TimHandle.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;

  /* Counter direction = Up */
  TimHandle.Init.CounterMode = TIM_COUNTERMODE_UP;

  /* Repetition = 0 */
  TimHandle.Init.RepetitionCounter = 1 - 1;

  /* Auto-reload register not buffered  */
  TimHandle.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;

  if (HAL_TIM_Base_Init(&TimHandle) != HAL_OK)
  {
    // APP_ErrorHandler();
  }

  /* Set output compare mode: PWM1 */
  TimersConfig.OCMode = TIM_OCMODE_PWM1;

  /* OC channel output high level effective */
  TimersConfig.OCPolarity = TIM_OCPOLARITY_HIGH;

  /* Disable OC FastMode */
  TimersConfig.OCFastMode = TIM_OCFAST_DISABLE;

  /* OCN channel output high level effective */
  TimersConfig.OCNPolarity = TIM_OCNPOLARITY_HIGH;

  /* Idle state OC1N output low level */
  TimersConfig.OCNIdleState = TIM_OCNIDLESTATE_RESET;

  /* Idle state OC1 output low level*/
  TimersConfig.OCIdleState = TIM_OCIDLESTATE_RESET;

  TimersConfig.Pulse = 40;

  /* Channel 1 configuration */
  if (HAL_TIM_PWM_ConfigChannel(&TimHandle, &TimersConfig, TIM_CHANNEL_1) != HAL_OK)
  {
    // APP_ErrorHandler();
  }
}

void pwm_set_duty(pwm_ch_t ch, uint8_t duty)
{
  if (duty > 100)
  {
    duty = 100;
  }

  if (duty > 0)
  {
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_1, GPIO_PIN_RESET);
  }

  TimersConfig.Pulse = duty * 2;

  /* Channel 1 configuration */
  if (HAL_TIM_PWM_ConfigChannel(&TimHandle, &TimersConfig, TIM_CHANNEL_1) != HAL_OK)
  {
    // APP_ErrorHandler();
  }

  /* Channel1 Start PWM */
  if (HAL_TIM_PWM_Start(&TimHandle, TIM_CHANNEL_1) != HAL_OK)
  {
    //   APP_ErrorHandler();
  }

  if (duty == 0)
  {
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_1, GPIO_PIN_SET);
  }
}

/**
 * BRIEF:      PIT init.
 * PARAM:      none
 * RETURN:     none
 * HISTORY:
 *             xyf 2021/2/25 9:28:12: function created.
 */

static uint16_t ticks_counter;
extern void PIT_Task0(void);
extern void PIT_Task1(void);

static void pit_init(void)
{
  ticks_counter = 0;
}

void HAL_IncTick(void)
{
  ticks_counter++;
  // if(ticks_counter%10==0)  //10 ms
  {
    PIT_Task1();
  }

  if (ticks_counter % 100 == 0) ////	1s
  {
    PIT_Task0();
    ticks_counter = 0;
    // if (ticks_counter / 100 % 2 == 1)
    //   HAL_GPIO_ResetBits(GPIOB, GPIO_PIN_4);
    // else
    //   HAL_GPIO_SetBits(GPIOB, GPIO_PIN_4);
  }
}

void task_100us(void)
{
}

/**
 * BRIEF:      init UART.
 * PARAM:      none
 * RETURN:     none
 * HISTORY:
 *             xyf 2021/2/25 9:28:18: function created.
 */
static void uart_init(void)
{
  /* USART initialization */
  UartHandle.Instance = USART1;
  UartHandle.Init.BaudRate = 38400;
  UartHandle.Init.WordLength = UART_WORDLENGTH_8B;
  UartHandle.Init.StopBits = UART_STOPBITS_1;
  UartHandle.Init.Parity = UART_PARITY_NONE;
  UartHandle.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  UartHandle.Init.Mode = UART_MODE_TX_RX;
  UartHandle.Init.OverSampling = UART_OVERSAMPLING_16;
  UartHandle.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;
  HAL_UART_Init(&UartHandle);

  HAL_UART_Receive_IT(&UartHandle, (uint8_t *)uart_rcv_buffer, UART_RCV_BUFFER_SIZE);

  /* USART1 interrupt enable */
  HAL_NVIC_SetPriority(USART1_IRQn, 0, 1);
  HAL_NVIC_EnableIRQ(USART1_IRQn);
}

/**
 * BRIEF:      init watch dog system.
 * PARAM:      none
 * RETURN:     none
 * HISTORY:
 *             xyf 2021/2/25 9:28:24: function created.
 */
static IWDG_HandleTypeDef IwdgHandle;
static void wdog_init(void)
{
  IwdgHandle.Instance = IWDG;                    /* IWDG */
  IwdgHandle.Init.Prescaler = IWDG_PRESCALER_64; /* Prescaler DIV 32 */
  IwdgHandle.Init.Reload = (1024);               /* IWDG Reload value 1024 */

  if (HAL_IWDG_Init(&IwdgHandle) != HAL_OK) /* Initialize the IWDG */
  {
    // APP_ErrorHandler();
  }
}

void WDOG_Feed(void)
{
  /* Refresh the Iwdg */
  if (HAL_IWDG_Refresh(&IwdgHandle) != HAL_OK)
  {
    // APP_ErrorHandler();
  }
}

void PIT_Task0(void)
{
  signal_set(TASK_1S_SIGNAL);
}

void PIT_Task1(void)
{
  signal_set(TASK_10MS_SIGNAL);

  uart_rcv_timeout_tick();
}

void UART_Task(void)
{
  uint8_t ch;

  ch = (uint8_t)(UartHandle.Instance->DR & (uint8_t)0x00FF);
  if (uart_rcv_length < UART_RCV_BUFFER_SIZE)
  {
    uart_rcv_buffer[uart_rcv_length] = ch;
    uart_rcv_length++;
    // timeout manage.
    if (uart_rcv_timeout == 0)
    {
      uart_rcv_timeout = 5; // 50ms
    }
  }
}

void uart_rcv_timeout_tick(void)
{
  if (uart_rcv_timeout > 0)
  {
    uart_rcv_timeout--;
    if (uart_rcv_timeout == 0)
    {
      signal_set(UART_TAT_RCV_SIGNAL);
    }
  }
}
#ifdef LIN_ENABLE
void lin_send(uint8_t *_buf, uint8_t _len)
{
  HAL_UART_Transmit(&UartHandle, _buf, _len, 100);
}
#endif
void uart_tat_send(uint8_t *buffer, uint8_t length)
{
  // uint8_t i;

  HAL_UART_Transmit(&UartHandle, buffer, length, 100);

  // for (i = 0; i < length; i++)
  // {
  //   fputc(buffer[i], NULL);
  // }
}

int fputc(int ch, FILE *f)
{
  HAL_UART_Transmit(&UartHandle, (uint8_t *)&ch, 1, 10);
  return ch;
}

void nvm_erase(uint16_t addr)
{
  if (addr < FLASH_DATA_SIZE)
  {
    uint32_t SECTORError = 0;
    FLASH_EraseInitTypeDef EraseInitStruct = {0};

    EraseInitStruct.TypeErase = FLASH_TYPEERASE_SECTORERASE;   /* Sector Erase */
    EraseInitStruct.SectorAddress = addr + FLASH_ADDRESS_USER; /* Erase Start Address */
    EraseInitStruct.NbSectors = 1;                             /* Erase Sector Number */
    HAL_FLASH_Unlock();
    HAL_FLASHEx_Erase(&EraseInitStruct, &SECTORError); /* Sector Erase */
    HAL_FLASH_Lock();
  }
}

void nvm_read(uint32_t addr, uint8_t *buffer, uint16_t size)
{
  uint16_t i;

  if (addr + size <= FLASH_DATA_SIZE)
  {
    for (i = 0; i < size; i++)
    {
      buffer[i] = *((uint8_t code *)((FLASH_ADDRESS_USER + addr) + i));
    }
  }
}

void nvm_write(uint32_t addr, uint8_t *buffer, uint16_t size)
{
  if (addr + size <= FLASH_DATA_SIZE)
  {
    uint32_t flash_program_start = FLASH_ADDRESS_USER + addr;        /* Start address of user write flash */
    uint32_t flash_program_end = (FLASH_ADDRESS_USER + addr + size); /* End address of user write flash */
    uint32_t *src = (uint32_t *)buffer;                              /* Pointer to array */

    HAL_FLASH_Unlock();

    while (flash_program_start < flash_program_end)
    {
      if (HAL_FLASH_Program(FLASH_TYPEPROGRAM_PAGE, flash_program_start, src) == HAL_OK) /* Perform Program */
      {
        flash_program_start += FLASH_PAGE_SIZE; /* Point to the start address of the next page to be written */
        src += FLASH_PAGE_SIZE / 4;             /* Point to the next data to be written */
      }
    }

    HAL_FLASH_Lock();
  }
}

uint8_t nvm_readchipid(uint8_t *cid)
{

  memcpy(cid, (uint8_t *)0x1FFF0004, 12);
  return 12;
}

static uint8_t is_rev_pressed;
static uint8_t rev_counter;

uint8_t gpio_rev_in(void)
{
  if (HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_2) == GPIO_PIN_RESET)
  {
    // rev released
    if (rev_counter == 0)
    {
      is_rev_pressed = 0;
    }
    else
    {
      rev_counter--;
    }
  }
  else
  {
    // rev pressed
    if (rev_counter > 20)
    {
      is_rev_pressed = 1;
    }
    else
    {
      rev_counter++;
    }
  }
  return is_rev_pressed;
}

uint8_t gpio_get_off(void)
{
  return HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_1);
}

void uart_tat_rcv_buffer_clear(void)
{
  uart_rcv_length = 0;
  memset(uart_rcv_buffer, 0, UART_RCV_BUFFER_SIZE);
}

uint8_t *uart_tat_get_buffer(uint8_t *length)
{
  *length = uart_rcv_length;

  return uart_rcv_buffer;
}

uint16_t adc_get_ec_int(void)
{
  uint16_t value;
#ifdef INT_EC_ADC_WITH_DIFF
  uint16_t value_ext;
#endif

  value = ADC_Read_Channel(7);

#ifdef INT_EC_ADC_WITH_DIFF
  value_ext = adc_get_ec_ext();

  if (value > value_ext)
    value -= value_ext;
#endif

  return value;
}

uint16_t adc_get_back(void)
{
  uint16_t value;

  value = ADC_Read_Channel(2);

#ifdef GLS_2_GND
  value = 4096 - value;
#endif

  return (value);
}

uint16_t adc_get_front(void)
{
  uint16_t value;

  value = ADC_Read_Channel(4);

#ifdef ALS_2_GND
  value = 4096 - value;
#endif

  return (value);
}

uint16_t adc_get_ign(void)
{
  uint16_t value;

  value = ADC_Read_Channel(0);

  return value;
}

uint16_t adc_get_ec_ext(void)
{
  uint16_t value = 0;

  // ENABLE_ADC_AIN7;
  // value = ADC_PollRead();

  return value;
}

uint16_t adc_get_ntc(void)
{
  uint16_t value;

  value = ADC_Read_Channel(1);
  value = 47l * value * 4096l / (100l * 4096l - 53l * value);

  return value;
}

extern uint8_t parm_data[];

static void APP_SystemClockConfig(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /* Oscillator configuration */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE | RCC_OSCILLATORTYPE_HSI | RCC_OSCILLATORTYPE_LSI | RCC_OSCILLATORTYPE_LSE; /* Select oscillator HSE, HSI, LSI, LSE */
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;                                                                                              /* Enable HSI */
  RCC_OscInitStruct.HSIDiv = RCC_HSI_DIV1;                                                                                              /* HSI 1 frequency division */
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_24MHz;                                                                     /* Configure HSI clock 24MHz */
  RCC_OscInitStruct.HSEState = RCC_HSE_BYPASS_DISABLE;                                                                                  /* Close HSE bypass */
  RCC_OscInitStruct.LSIState = RCC_LSI_OFF;                                                                                             /* Close LSI */
  /*RCC_OscInitStruct.LSICalibrationValue = RCC_LSICALIBRATION_32768Hz;*/
  RCC_OscInitStruct.LSEState = RCC_LSE_OFF; /* Close LSE */
  /*RCC_OscInitStruct.LSEDriver = RCC_LSEDRIVE_MEDIUM;*/
  /* Configure oscillator */
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    // APP_ErrorHandler();
  }

  /* Clock source configuration */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK | RCC_CLOCKTYPE_PCLK1; /* Choose to configure clock HCLK, SYSCLK, PCLK1 */
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_HSISYS;                                      /* Select HSISYS as the system clock */
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;                                             /* AHB clock 1 division */
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;                                              /* APB clock 1 division */
  /* Configure clock source */
  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    // APP_ErrorHandler();
  }
}

static void APP_FlashOBProgram(void)
{
  FLASH_OBProgramInitTypeDef OBInitCfg = {0};

  HAL_FLASH_Unlock();    /* Unlock Flash */
  HAL_FLASH_OB_Unlock(); /* Unlock Option */

  OBInitCfg.OptionType = OPTIONBYTE_USER;
  OBInitCfg.USERType = /*OB_USER_BOR_EN | OB_USER_BOR_LEV | OB_USER_IWDG_SW | */ OB_USER_SWD_NRST_MODE;

  OBInitCfg.USERConfig = /*OB_BOR_DISABLE | OB_BOR_LEVEL_3p1_3p2 | OB_IWDG_SW | */ OB_SWD_PB6_GPIO_PC0;

  /* Option Program */
  HAL_FLASH_OBProgram(&OBInitCfg);

  HAL_FLASH_Lock();    /* Lock Flash */
  HAL_FLASH_OB_Lock(); /* Lock Option */

  /* Option Launch */
  HAL_FLASH_OB_Launch();
}

void hw_init(void)
{
  /* Reset of all peripherals, Initializes the Systick. */
  HAL_Init();
  /* System clock configuration */
  APP_SystemClockConfig();

  gpio_init();
  uart_init();
  pit_init();
  wdog_init();
  board_pwm_init();
  adc_init();
  if (READ_BIT(FLASH->OPTR, OB_USER_SWD_NRST_MODE) != OB_SWD_PB6_GPIO_PC0)
  {
    printf("system first restart.\n");
    /* OPTION Program */
    APP_FlashOBProgram();
  }
  printf("hw_init %d\n", parm_data[0]);
}

/**
 * BRIEF:      set value of OFF pin.
 * PARAM:      none
 * RETURN:     none
 * HISTORY:
 *
 */
void offpin_value_set(uint8_t value)
{
  if (value == 0)
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_1, GPIO_PIN_RESET);
  else
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_1, GPIO_PIN_SET);
}
