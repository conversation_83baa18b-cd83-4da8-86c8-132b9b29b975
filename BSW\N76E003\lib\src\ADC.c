#include "ADC.h"

/**
  * BRIEF:      get 12 bit adc value from ADCRH and ADCRL.
  *             Must use Enable_ADC_AINx first.
  * PARAM:      none
  * RETURN:
  *             the adc value.
  * HISTORY:
  *             xyf 2020/12/23 15:52:07: function created.
  */
uint16_t ADC_PollRead(void)
{
    uint16_t adc_value;

    clr_ADCF;
    set_ADCS;
    while (ADCF == 0);
    adc_value = ADCRH;
    adc_value = (adc_value << 4) | ADCRL;

    return adc_value;
}
