#ifndef __ASM31AX_FLASH_H__
#define __ASM31AX_FLASH_H__

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>

    /**
     ****************************************************************
     * @brief 读取Flash
     *
     * @param addr  地址
     * @param buf 数据缓存
     * @param len 数据长度
     *
     ****************************************************************
     */
    void Flash_Read(uint32_t addr, uint8_t *buf, uint32_t len);

    /**
     ****************************************************************
     * @brief 写Flash
     *
     * @param addr  地址
     * @param buf 数据缓存
     * @param len 数据长度
     *
     ****************************************************************
     */
    void Flash_Write(uint32_t addr, uint8_t *buf, uint32_t len);

    /**
     ****************************************************************
     * @brief 擦除Flash
     *
     * @param addr  地址
     *
     ****************************************************************
     */
    void Flash_Erase(uint32_t addr);

#ifdef __cplusplus
}
#endif

#endif
