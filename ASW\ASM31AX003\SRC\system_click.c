/* USER CODE BEGIN Header */
/**
 ****************************************************************************** 
 * File Name          : system_click.c                                           
 * Description        : This file provides code for the MSP Initialization      
 *                      and de-Initialization codes.                            
 ****************************************************************************** 
 * @attention                                                                   
 *                                                                              
 * <h2><center>&copy; Copyright (c) 2021 
 * All rights reserved.</center></h2>                                           
 *                                                                              
 * This software component is licensed by Mcu Studio under BSD 3-Clause license,        
 * the License ; You may not use this file except in compliance with the 
 * License.You may obtain a copy of the License at:                             
 *                        opensource.org/licenses/BSD-3-Clause                  
 *                                                                              
 ****************************************************************************** 
 */                                                                             
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "system_click.h"
/* USER CODE END Includes */

// <<< Use Configuration Wizard in Context Menu >>>
/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN TD */

/* USER CODE END TD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN Define */

/* USER CODE END Define */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN Macro */

/* USER CODE END Macro */

/* USER CODE END Macro */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* External functions --------------------------------------------------------*/
/* USER CODE BEGIN ExternalFunctions */
//annotation start SystemInit
// <h> SystemInit
/*******************************************
 * @brief 系统时钟配置
 * 
 ******************************************/
//annotation end SystemInit
void SystemInit(void)
{
    RCC->HCLKEN = 0xffffffff;
    RCC->PCLKEN = 0xffffffff;
    RCC->UNLOCK = 0x55aa6699;
    RCC->HIRCCR = 0x5a690EE4;
    RCC->UNLOCK = 0x55aa6698;
    RCC_LIRCTrim(RCC,RCC_LIRC_TRIM_32768HZ);
    RCC_SysClkSourceSwitchHirc(RCC,RCC_HIRC_TRIM_24MHZ);
    RCC_SelSysclk(RCC,RCC_SYSCLKSource_HIRC);
    RCC_SetHclkDiv(RCC,0);
    RCC_SetPclkDiv(RCC,0);
}
/* USER CODE END ExternalFunctions */

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

// <<< end of configuration section >>>
