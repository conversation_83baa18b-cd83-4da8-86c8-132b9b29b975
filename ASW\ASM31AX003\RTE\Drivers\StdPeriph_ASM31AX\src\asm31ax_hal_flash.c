#include "asm31ax_hal.h"

#ifdef HAL_FLASH_MODULE_ENABLED

#define FLASH_BYPASS()        \
    FMC->BYPASS = 0x00005A5A; \
    FMC->BYPASS = 0x0000A5A5

/**
 ****************************************************************
 * @brief 解锁
 *
 * @param addr Flash 地址
 ****************************************************************
 */
static void Flash_Unlock(uint32_t addr)
{
    uint32_t val;
    val = addr / 512;
    FLASH_BYPASS();
    if (val > 63)
    {
        FMC->SLOCK1 |= 1 << ((val % 64) / 2);
    }
    else
    {
        FMC->SLOCK0 |= 1 << (val / 2);
    }
}

/**
 ****************************************************************
 * @brief 上锁
 *
 * @param addr Flash 地址
 ****************************************************************
 */
static void Flash_Lock(uint32_t addr)
{
    uint32_t val;
    val = addr / 512;
    FLASH_BYPASS();
    if (val > 63)
    {
        FMC->SLOCK1 = 0x00000000;
    }
    else
    {
        FMC->SLOCK0 = 0x00000000;
    }
}

/**
 ****************************************************************
 * @brief 写1byte数据
 *
 * @param u32Addr Flash 地址
 * @param u8Data  数据
 ****************************************************************
 */
static void Flash_WriteByte(uint32_t u32Addr, uint8_t u8Data)
{
    uint32_t tempreg;
    tempreg = FMC->CR;
    tempreg &= 0XFFFFFFFC;
    tempreg |= (uint32_t)0X01;

    // set OP
    FLASH_BYPASS();
    FMC->CR = tempreg;

    // write data
    *((volatile uint8_t *)u32Addr) = u8Data;

    // busy?
    while ((FMC->CR & (uint32_t)0X04) != 0)
    {
        ;
    }
}

void Flash_Read(uint32_t addr, uint8_t *buf, uint32_t len)
{
    while (len--)
    {
        *buf = *((volatile uint8_t *)addr);
        buf++;
        addr++;
    }
}

void Flash_Write(uint32_t addr, uint8_t *buf, uint32_t len)
{
    uint8_t page, i;
    uint32_t iser;
    iser = NVIC->ISER[0];
    page = (len + 511) / 512;
    // 解锁
    for (i = 0; i < page; i++)
    {
        Flash_Unlock(addr + 512 * i);
    }
    NVIC->ICER[0] = 0XFFFFFFFF;
    while (len--)
    {
        Flash_WriteByte(addr, *buf);
        addr++;
        buf++;
    }
    // 加锁
    Flash_Lock(addr);
    // 开启中断
    NVIC->ISER[0] = iser;
}

void Flash_Erase(uint32_t addr)
{
    uint32_t tempreg, iser;
    // 关闭中断
    iser = NVIC->ISER[0];
    NVIC->ICER[0] = 0XFFFFFFFF;

    Flash_Unlock(addr);
    tempreg = FMC->CR;
    tempreg &= 0XFFFFFFFC;
    tempreg |= (uint32_t)0X02;
    FLASH_BYPASS();
    FMC->CR = tempreg;

    // write data
    *((volatile uint32_t *)addr) = 0x00;

    // busy?
    while ((FMC->CR & (uint32_t)0X04) != 0)
    {
        ;
    }

    Flash_Lock(addr);
    // 开启中断
    NVIC->ISER[0] = iser;
}

#endif
